import request from '@/utils/request'

/* 1. 质量问题管理 */

// 查询质量问题管理列表
export function queryList(data) {
  return request({
    url: '/dataqulityQstnManager/queryList',
    method: 'post',
    data
  })
}
// 编辑质量问题信息
export function editQstnInfo(data) {
  return request({
    url: '/dataqulityQstnManager/editQstnInfo',
    method: 'post',
    data
  })
}
// 获取所有规则的系统和数据库
export function getAllSysAndDb(params) {
  return request({
    url: '/dataqulityQstnManager/getAllSysAndDb',
    method: 'get',
    params
  })
}
// 查询质量问题数据详情
export function queryDetailQstnData(params) {
  return request({
    url: '/dataqulityQstnManager/queryDetailQstnData',
    method: 'get',
    params
  })
}

