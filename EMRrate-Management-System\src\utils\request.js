import axios from "axios"
import {
  MessageBox,
  Message,
  Loading
} from "element-ui"
import store from "@/store"
import {
  getToken
} from "@/utils/auth"
import {
  tansParams,
  blobValidate
} from "@/utils/ruoyi";
import {
  saveAs
} from "file-saver";
// create an axios instance
const service = axios.create({
  baseURL: process.env.NODE_ENV === "development" ? "/api" : "", // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 300000, // request timeout
})

// request interceptor
service.interceptors.request.use(
  (config) => {
    if (!("token" in config.headers)) {
      config.headers["token"] = getToken()
    }
    return config
  },
  (error) => {
    // console.log(error)
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  (response, options) => {
    //如果返回的是文件流 处理成文件对象
    if (
      response.headers &&
      response.headers["content-transfer-encoding"] === 'binary'
    ) {

      let s = response.headers["content-disposition"]
      let fileName = s.split("=")[1]
      return new File([response.data], fileName, {
        type: response.headers["content-type"],
      })
    }
    return response.data
  },
  (error) => {
    //授权过期一类的东西
    if (error.message.indexOf("403") != -1) {
      MessageBox.confirm("登录信息已过期，请重新登录！", "确认重新登陆", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        store.dispatch("user/resetToken").then(() => {
          location.reload()
        })
      })
      return Promise.reject(new Error(error.message || "Error"))
    } else {
      Message({
        message: error.message,
        type: "error",
        duration: 5 * 1000,
      })
    }
    return Promise.reject(error)
  }
)

export function download(url, params, filename, config) {
  let loadingInstance = Loading.service({
    text: "正在下载数据，请稍候",
    spinner: "el-icon-loading",
    background: "rgba(0, 0, 0, 0.7)",
  })
  return service
    .post(url, params, {
      transformRequest: [
        (params) => {
          return tansParams(params)
        },
      ],
      headers: {
        "Content-Type": "application/x-www-form-urlencoded"
      },
      responseType: "blob",
      ...config,
    })
    .then(async (data) => {
      const isLogin = await blobValidate(data)
      if (isLogin) {
        const blob = new Blob([data])
        saveAs(blob, filename)
      } else {
        const resText = await data.text()
        const rspObj = JSON.parse(resText)
        const errMsg =
          errorCode[rspObj.code] || rspObj.msg || errorCode["default"]
        Message.error(errMsg)
      }
      loadingInstance.close()
    })
    .catch((r) => {
      console.error(r)
      Message.error("下载文件出现错误，请联系管理员！")
      loadingInstance.close()
    })
}
export default service
