<template>
  <MainCard>
    <div class="user-container">
      <!-- <el-form
        :inline="true"
        :model="formInline"
        size="small"
        class="user-form-inline">
        <el-form-item label="姓名">
          <el-input
            v-model="formInline.userName"
            placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="账号">
          <el-input
            v-model="formInline.loginId"
            placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="电话">
          <el-input
            v-model="formInline.mobile"
            placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-col :span="11">
            <el-date-picker
              type="date"
              placeholder="开始时间"
              v-model="formInline.startTime"
              style="width: 100%"
              value-format="yyyy-MM-dd"></el-date-picker>
          </el-col>
          <el-col
            class="line"
            :span="2"
            >-</el-col
          >
          <el-col :span="11">
            <el-date-picker
              type="date"
              placeholder="结束时间"
              v-model="formInline.endTime"
              style="width: 100%"
              value-format="yyyy-MM-dd"></el-date-picker>
          </el-col>
        </el-form-item>
        <el-form-item style="margin-right: 0px">
          <el-button
            type="primary"
            @click="getUserQueryList"
            >查询</el-button
          >
          <el-button @click="startReset">重置</el-button>
        </el-form-item>
      </el-form> -->
      <div class="header-search">
        <div class="newAdd">
          <el-button
            type="primary"
            @click="flag = 'create'"
            >添加成员</el-button
          >
        </div>
        <div class="search-form">
          <div class="title">
            <span>角色：</span>
          </div>
          <el-select placeholder="请选择">
           
          </el-select>
          <el-input
            type="text"
            v-model="formInline.userName"
            placeholder="搜索成员（姓名/账号）">
            <i
              slot="append"
              class="el-icon-search"></i>
          </el-input>
        </div>
      </div>
      <div class="table-container">
        <el-table
          :data="tableData"
          ref="multipleTable"
          tooltip-effect="dark"
          :header-cell-style="{ background: '#fff', color: '#606266' }"
          size="mini">
          <el-table-column
            type="index"
            width="50"
            label="序号"></el-table-column>
          <el-table-column
            prop="userName"
            width="120"
            label="姓名">
            <template slot-scope="scope">
              <div class="userName">
                <el-avatar
                  :style="{
                    backgroundColor:
                      scope.row.userName.trim() === '管理员'
                        ? '#ad7ef4'
                        : '#4ea5f8'
                  }"
                  size="small">
                  {{
                    !!scope.row.userName
                      ? scope.row.userName.trim().charAt(0)
                      : ''
                  }}</el-avatar
                >
                <span class="name">{{ scope.row.userName.trim() }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="loginId"
            label="账号">
          </el-table-column>
          <el-table-column
            prop="list"
            width="100"
            label="角色">
            <template slot="header">
              <span style="margin-right: 4px">角色</span>
              <el-tooltip
                effect="dark"
                placement="bottom">
                <i
                  style="cursor: pointer"
                  class="el-icon-info"></i>
                <div slot="content">
                  <TipContent />
                </div>
              </el-tooltip>
            </template>
            <template slot-scope="scope">
              <span>{{
                scope.row.list.map((v) => v.roleName).join('、')
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="wxAccount"
            width="140"
            label="企业微信账户"></el-table-column>
          <el-table-column
            prop="mobile"
            label="联系方式"
            width="120px"></el-table-column>
          <el-table-column
            prop="email"
            width="120"
            label="邮箱e-mail">
          </el-table-column>
          <el-table-column
            prop="sysDepts"
            label="验证方式"
            width="160">
            <template slot-scope="scope">
              <span
                v-for="(v, i) in scope.row.identifyType"
                :key="i"
                >{{ i + 1 }}.{{ identifyTypeOptions[v] }}</span
              >
              <!-- <span>{{ identifyTypeOptions[scope.row.identifyType] }}</span> -->
            </template>
          </el-table-column>
          <el-table-column
            prop="userStatus"
            label="状态"
            width="100px">
            <template slot-scope="scope">
              <span
                :style="{
                  backgroundColor:
                    scope.row.userStatus == 1 ? '#50ca72' : '#b7b7b7'
                }"
                class="circle"></span>
              <span>{{ getStatusMap(scope.row.userStatus) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="createTime"
            label="创建时间"
            width="180"></el-table-column>
          <el-table-column
            prop="accountActivationDate"
            label="账号启用日期"
            width="180"></el-table-column>
          <el-table-column
            prop="accountExpirationDate"
            label="账号失效日期"
            width="180"></el-table-column>
          <el-table-column
            prop="action"
            fixed="right"
            label="操作"
            width="200">
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="setFlag('edit', scope.row)"
                >修改信息</el-button
              >
              <el-divider direction="vertical"></el-divider>
              <el-dropdown
                trigger="click"
                @command="(command) => handleCommand(command, scope.row)">
                <span
                  style="cursor: pointer"
                  class="el-dropdown-link">
                  下拉菜单<i class="el-icon-arrow-down el-icon--right"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="reset"
                    @click="setFlag('reset', scope.row)"
                    >重置密码</el-dropdown-item
                  >
                  <el-dropdown-item command="forbid">
                    {{
                      scope.row.userStatus == 0 ? '启用' : '停用'
                    }}</el-dropdown-item
                  >
                  <el-dropdown-item command="delete">删除</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.size"
          :total="total"
          :current-page="pagination.current"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          @prev-click="handleCurrentChange"
          @next-click="handleCurrentChange">
        </el-pagination>
      </div>

      <el-dialog
        v-dialogDrag
        :title="flag == 'create' ? '添加成员' : '编辑成员'"
        :visible="flag == 'create' || flag == 'edit'"
        width="1000px"
        @close="closeCreateModal"
        :close-on-click-modal="false">
        <el-form
          :model="createForm"
          label-width="100px"
          class="modalContainer"
          ref="createFormRef"
          :rules="rules">
          <div class="dialog-form-item">
            <el-form-item
              label="姓名"
              prop="userName">
              <el-input v-model="createForm.userName"></el-input>
            </el-form-item>
            <el-form-item
              label="账号"
              prop="loginId">
              <el-input
                :disabled="flag == 'edit'"
                v-model="createForm.loginId"
                placeholder="长度在 3 到 20 个字符，且不包含中文"></el-input>
            </el-form-item>
            <el-form-item
              v-if="flag == 'create'"
              label="密码"
              prop="password">
              <el-input
                :disabled="flag == 'edit'"
                v-model="createForm.password"
                :placeholder="rules.password[0].message"></el-input>
            </el-form-item>
            <el-form-item
              label="角色"
              prop="list">
              <el-select
                style="width: 100%"
                v-model="createForm.list"
                multiple
                placeholder="请选择角色">
                <el-option
                  v-for="item in roleList"
                  :key="item.roleId"
                  :label="item.roleName"
                  :value="item.roleId">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="账号启用日期"
              prop="date">
              <el-date-picker
                type="date"
                placeholder="请选择日期"
                v-model="createForm.accountActivationDate"
                value-format="yyyy-MM-dd"></el-date-picker>
            </el-form-item>
            <el-form-item
              label="账号失效日期"
              prop="date">
              <el-date-picker
                type="date"
                placeholder="请选择日期"
                v-model="createForm.accountExpirationDate"
                style="width: 100%"
                value-format="yyyy-MM-dd"></el-date-picker>
            </el-form-item>
            <el-form-item label="登录方式">
              <el-select
                style="width: 100%"
                v-model="createForm.identifyType"
                placeholder="请选择"
                multiple>
                <el-option
                  v-for="(label, index) in identifyTypeOptions"
                  :key="index"
                  :label="label"
                  :value="String(index)">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="企业微信账号">
              <el-input v-model="createForm.wxAccount"></el-input>
            </el-form-item>
            <el-form-item label="联系电话">
              <el-input v-model="createForm.mobile"></el-input>
            </el-form-item>
            <el-form-item label="邮箱E-mail">
              <el-input v-model="createForm.email"></el-input>
            </el-form-item>
          </div>
        </el-form>
        <div slot="footer">
          <el-button
            @click="flag = ''"
            size="mini"
            >取 消</el-button
          >
          <el-button
            type="primary"
            @click="createUser"
            size="mini"
            >保 存</el-button
          >
        </div>
      </el-dialog>

      <el-dialog
        v-dialogDrag
        title="重置密码"
        :visible="flag == 'reset'"
        width="500px"
        :close-on-click-modal="false"
        @close="flag = ''">
        <el-form
          :model="resetPasswordForm"
          size="mini"
          label-width="80px"
          class="modalContainer"
          :rules="resetRules"
          ref="resetFormRef">
          <el-form-item
            label="新密码"
            prop="first">
            <el-input
              v-model="resetPasswordForm.first"
              placeholder="请输入新密码"></el-input>
          </el-form-item>
          <el-form-item
            label="新密码"
            prop="second">
            <el-input
              v-model="resetPasswordForm.second"
              placeholder="再输入一次密码"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer">
          <el-button
            @click="flag = ''"
            size="mini"
            >取 消</el-button
          >
          <el-button
            type="secondary"
            @click="resetPassword"
            size="mini"
            >确 定</el-button
          >
        </div>
      </el-dialog>
    </div>
  </MainCard>
</template>

<script>
import {
  userQueryList as _userQueryList,
  UpdateUser as _UpdateUser,
  DeleteUser as _DeleteUser,
  AddUser as _AddUser,
  updateUserStatus as _updateUserStatus,
  resetPassWord as _resetPassWord,
  getRoleList as _getRoleList
} from '@/api/user'
import { querySysConfig as _querySysConfig } from '@/api/sys-config'
import TipContent from './TipContent.vue'
function defaultForm() {
  return {
    loginId: '',
    mobile: '',
    userName: '',
    startTime: '',
    endTime: ''
  }
}

function defaultCreateForm() {
  return {
    userName: '',
    loginId: '',
    password: '',
    mobile: '',
    email: '',
    list: [],
    identifyType: ['0'],
    wxAccount: ''
  }
}

export default {
  components: { TipContent },
  created() {
    this.getUserQueryList()
    this.getRoleQueryList()
  },
  mounted() {
    _querySysConfig().then((res) => {
      this.passwordLevel = res.data[1][0].configValue
      const level1 =
        /^(?![A-z0-9]+$)(?=.[^%&',;=?$\x22])(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9]).{8,20}$/
      const level1Msg = '长度在8—20之间，包括大、小写字母、数字、特殊符号4种'
      const level2 = /^(?!(?:\d+|[a-zA-Z]+)$)[\da-zA-Z]{8,20}$/
      const level2Msg = '长度在 8 到 20 个字符，并包含数字和字母'

      if (this.passwordLevel == 1) {
        this.rules.password[0].pattern = level1
        this.rules.password[0].message = level1Msg
        this.resetRules.first[0].pattern = level1
        this.resetRules.first[0].message = level1Msg
        this.resetRules.second[0].pattern = level1
        this.resetRules.second[0].message = level1Msg
      } else if (this.passwordLevel == 2) {
        this.rules.password[0].pattern = level2
        this.rules.password[0].message = level2Msg
        this.resetRules.first[0].pattern = level2
        this.resetRules.first[0].message = level2Msg
        this.resetRules.second[0].pattern = level2
        this.resetRules.second[0].message = level2Msg
      } else {
        this.rules.password[0].pattern = undefined
        this.rules.password[0].message = undefined
        this.resetRules.first[0].pattern = undefined
        this.resetRules.first[0].message = undefined
        this.resetRules.second[0].pattern = undefined
        this.resetRules.second[0].message = undefined
      }
    })
  },
  data() {
    return {
      breadcrumbs: [],
      formInline: defaultForm(), //搜索的表单
      //新建用户的表单和规则
      createForm: defaultCreateForm(),
      rules: {
        userName: [
          {
            trigger: 'blur',
            required: true,
            message: '请填写用户名'
          }
        ],
        loginId: [
          {
            pattern: /^[^\u4e00-\u9fa5]{3,20}$/,
            message: '长度在 3 到 20 个字符，且不包含中文',
            trigger: 'blur',
            required: true
          }
        ],
        password: [
          {
            pattern: /^(?!(?:\d+|[a-zA-Z]+)$)[\da-zA-Z]{8,20}$/,
            message: '长度在 8 到 20 个字符，并包含数字和字母',
            trigger: 'blur',
            required: true
          }
        ],
        list: [{ required: true, trigger: 'blur' }]
      },

      resetPasswordForm: {}, //重置密码表单
      resetRules: {
        first: [
          {
            pattern: /^(?!(?:\d+|[a-zA-Z]+)$)[\da-zA-Z]{8,20}$/,
            message: '长度在 8 到 20 个字符，并包含数字和字母',
            trigger: 'blur',
            required: true
          }
        ],
        second: [
          {
            pattern: /^(?!(?:\d+|[a-zA-Z]+)$)[\da-zA-Z]{8,20}$/,
            message: '长度在 8 到 20 个字符，并包含数字和字母',
            trigger: 'blur',
            required: true
          }
        ]
      },
      editForm: {}, //编辑表单
      //分页
      pagination: {
        current: 1,
        size: 10
      },
      tableData: [],
      total: 1,
      flag: '',
      row: '', //当前操作的行
      roleList: [], //角色列表
      identifyTypeOptions: ['帐密+图片验证码', '短信验证码', '帐密+短信验证码']
    }
  },
  methods: {
    onReset() {
      this.formInline = defaultForm()
    },

    getUserQueryList() {
      _userQueryList({ ...this.formInline, ...this.pagination }).then((res) => {
        if (res && 'data' in res) {
          for (const iterator of res.data.list) {
            iterator.identifyType = iterator.identifyType.split(',')
          }
          // console.log(res.data.list, 123456789000);
          this.tableData = res.data.list || []
          this.pagination.current = res.data.pageNum
          this.pagination.size = res.data.pageSize
          this.total = res.data.total
        }
      })
    },
    handleCommand(command, row) {
      if (command === 'reset') {
        this.setFlag('reset', row)
        return
      }
      if (command === 'forbid') {
        this.$confirm(
          `确定${row.userStatus == 0 ? '开启' : '停用'}这个账号吗?`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
          .then(() => {
            this.forbiddenOrOpenCount(row)
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消操作'
            })
          })
        return
      }
      if (command === 'delete') {
        this.$confirm('确定要删除这个账号吗?', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.deleteCount(row)
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消操作'
            })
          })
      }
    },
    setFlag(flag, row) {
      this.flag = flag
      this.row = row
      if (flag == 'edit') {
        this.createForm = {
          userName: row.userName,
          loginId: row.loginId,
          password: row.password,
          mobile: row.mobile,
          email: row.email,
          identifyType: row.identifyType,
          wxAccount: row.wxAccount,
          list: row.list.map((v) => v.roleId),
          accountActivationDate: row.accountActivationDate,
          accountExpirationDate: row.accountExpirationDate
        }
      }
    },

    //获取角色列表
    getRoleQueryList() {
      _getRoleList({
        page: 1,
        size: 999
      }).then((res) => {
        if (res && 'data' in res && Array.isArray(res.data.list)) {
          this.roleList = res.data.list
        }
      })
    },

    //新建用户
    createUser() {
      if (this.flag == 'edit') {
        this.editUser()
        return
      }
      this.$refs['createFormRef'].validate((valid) => {
        if (valid) {
          //特殊处理一下list
          let list = this.roleList.filter((v, i) => {
            return this.createForm.list.indexOf(v.roleId) != -1
          })
          // console.log(this.createForm.identifyType);
          let data = JSON.parse(JSON.stringify(this.createForm))
          data.identifyType = data.identifyType.toString()
          _AddUser({ ...data, userStatus: 1, list }).then((res) => {
            if (res && res.msg == 'success') {
              this.$message.success('创建用户成功!')
              this.flag = ''
              this.createForm = defaultCreateForm()
              this.getUserQueryList()
            } else {
              this.$message.error(res.msg)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    startReset() {
      this.formInline = {}
      this.getUserQueryList()
    },
    //重置密码
    resetPassword() {
      this.$refs['resetFormRef'].validate((valid) => {
        if (valid) {
          if (this.resetPasswordForm.first != this.resetPasswordForm.second) {
            return this.$message.error('两次密码不一致')
          }
          _resetPassWord({
            userId: this.row.userId,
            password: this.resetPasswordForm.first
          }).then((res) => {
            if (res && res.msg == 'success') {
              this.$message.success('修改密码成功!')
              this.flag = ''
              this.resetPasswordForm = { first: '', second: '' }
              this.getUserQueryList()
            } else {
              this.$message.error(res.msg)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    //编辑
    editUser() {
      this.$refs['createFormRef'].validate((valid) => {
        if (valid) {
          //特殊处理一下list
          let list = this.roleList.filter((v, i) => {
            return this.createForm.list.indexOf(v.roleId) != -1
          })
          let data = JSON.parse(JSON.stringify(this.createForm))
          data.identifyType = data.identifyType.toString()
          _UpdateUser({ ...this.row, ...data, list }).then((res) => {
            if (res && res.msg == 'success') {
              this.$message.success('用户编辑成功!')
              this.flag = ''
              this.row = ''
              this.createForm = defaultCreateForm()
              this.getUserQueryList()
            } else {
              this.$message.error(res.msg)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },

    //禁用或者开启账号
    forbiddenOrOpenCount(row) {
      _updateUserStatus({
        userId: row.userId,
        userStatus: row.userStatus == 1 ? 0 : 1
      }).then((res) => {
        if (res && res.msg == 'success') {
          this.$message.success('操作成功!')
          this.getUserQueryList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    //删除
    deleteCount(row) {
      _DeleteUser({ userId: row.userId }).then((res) => {
        if (res && res.msg == 'success') {
          this.$message.success('删除成功!')
          this.getUserQueryList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    closeCreateModal() {
      this.flag = ''
      this.createForm = defaultCreateForm()
    },

    getStatusMap(type) {
      const StatusMap = {
        1: '已启用',
        0: '禁用'
      }
      return StatusMap[type]
    },

    handleSizeChange(size) {
      this.pagination.size = size
      this.getUserQueryList()
    },
    handleCurrentChange(current) {
      this.pagination.current = current
      this.getUserQueryList()
    }
  }
}
</script>

<style lang="scss" scope>
.user-container {
  .header-search {
    display: flex;
    justify-content: space-between;
    margin: 10px 0;
    .search-form {
      display: flex;
      align-items: center;
      .title {
        white-space: nowrap;
        color: #666;
        font-size: 14px;
      }
      .el-select {
        margin-right: 10px;
      }
      .el-input-group__append,
      .el-input-group__prepend {
        background-color: #fff;
        border-radius: 0 9px 9px 0;
        &:hover {
          background-color: #5270dd;
          color: #fff;
        }
      }
      .el-icon-search {
        font-size: 16px;
      }
    }
  }
  .table-container {
    position: relative;
    width: 100%;
    padding: 10px 10px;
    background-color: white;
    .userName {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      .el-avatar {
        background-color: #6c88ef;
        width: 28px;
        height: 28px;
      }
      .name {
        margin-left: 6px;
      }
    }
    .el-tooltip .title {
      color: red !important;
    }
    .circle {
      width: 12px;
      height: 12px;
      display: inline-block;
      background-color: #50ca72;
      vertical-align: middle;
      border-radius: 100%;
      margin-right: 6px;
      margin-top: -1px;
    }
    .el-divider {
      margin: 0 12px;
    }
  }

  .modalContainer {
    padding: 0 30px 0 0;
    .el-form-item {
      margin-bottom: 15px;
    }
  }
  .dialog-form-item {
    display: flex;
    flex-wrap: wrap;
    .el-form-item {
      width: 50%;
      padding: 0 40px;
    }
  }
}
</style>
