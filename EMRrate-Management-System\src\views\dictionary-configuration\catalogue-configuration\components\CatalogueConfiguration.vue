<template>
  <div class="emr-container-main">
    <div class="emr-container-main" v-loading="loading">
      <div class="emr-container-button">
        <el-button
          type="primary"
          icon="el-icon-folder-add"
          size="mini"
          @click="addnewoperation(0)"
          >添加一级目录</el-button
        >
        <span>
          适用等级：
          <el-select
            v-model="querylevelCode"
            @change="getdocumentDirectoryConfiguration()"
            clearable
            size="mini"
          >
            <el-option
              v-for="item in levelCodeData"
              :key="item.levelName"
              :label="item.levelName"
              :value="item.levelCode"
            >
            </el-option>
          </el-select>
        </span>
      </div>

      <div class="emr-container-table-self-defined">
        <!-- 第一排标题 -->
        <div
          class="emr-container-table-title emr-container-table-self-defined-row"
        >
          <div class="son0"></div>
          <div class="son1">
            序号
            <Ruleprompt></Ruleprompt>
          </div>
          <div class="son2">
            目录编号
            <Ruleprompt></Ruleprompt>
          </div>
          <div class="son3">评价文档目录</div>
          <div class="son4">适用等级</div>
          <div class="son5">
            数据质量规则
            <el-tooltip placement="top">
              <div slot="content">
                <h2>电子病历评审中以数据质量作为主要评价的量化手段</h2>
                <br />
                -- 数据 标准化:项目中数据记录与字典的一致性<br /><br />
                -- 数据 完整性:数据项内容的完整情况 <br /><br />
                -- 数据 整合性:相关系统对应数据项目可对照或关联 <br /><br />
                -- 数据
                及时性:项目中时间相关项的完整性、逻辑合理性(流程时间分布) <br />
              </div>
              <i class="el-icon-info"></i>
            </el-tooltip>
          </div>
          <div class="son9">操作</div>
        </div>
        <!-- 第一级 -->
        <div v-for="(item1, index1) in tableData" :key="item1.id">
          <div class="emr-container-table-self-defined-row">
            <div class="son0">
              <svg-icon
                v-if="!Boolean(item1.id)"
                icon-class="new"
                style="font-size: 36px"
              />
              <i
                v-else-if="Boolean(item1.id) & item1.directionstate"
                class="el-icon-arrow-down"
                @click="item1.directionstate = false"
              >
              </i>
              <i
                v-else-if="Boolean(item1.id) & (item1.directionstate === false)"
                class="el-icon-arrow-right"
                @click="loadnextlevel(1, item1, index1)"
              ></i>
            </div>
            <div class="son1">
              <div>
                {{ item1.serialNum }}
              </div>
            </div>
            <div class="son2">
              <div v-if="item1.editstate">
                <el-input
                  :class="!Boolean(item1.id) ? 'noidinput' : ''"
                  v-model.trim="item1.directoryCode"
                  placeholder="请输入项目代码"
                >
                </el-input>
              </div>
              <div v-else>
                {{ item1.directoryCode }}
              </div>
            </div>
            <div class="son3">
              <div v-if="item1.editstate">
                <el-input
                  :class="!Boolean(item1.id) ? 'noidinput' : ''"
                  v-model.trim="item1.directoryName"
                  :placeholder="
                    item1.parentCode ? '' : '输入一级目录（工作角色）'
                  "
                ></el-input>
              </div>
              <div v-else>
                <b>{{ item1.directoryName }}</b>
              </div>
            </div>
            <div class="son4"></div>
            <div class="son5"></div>
            <div class="son9">
              <span v-if="!item1.editstate">
                <el-button
                  size="mini"
                  type="text"
                  @click="item1.editstate = true"
                >
                  编辑
                </el-button>
                <el-divider direction="vertical"></el-divider>
              </span>
              <span v-show="!item1.editstate">
                <el-popconfirm
                  title="此操作将删除该目录, 是否继续?"
                  @onConfirm="
                    deletedocumentDirectoryConfigurationbutton(1, item1, index1)
                  "
                >
                  <el-button slot="reference" size="mini" type="text">
                    删除
                  </el-button>
                </el-popconfirm>
                <el-divider direction="vertical"></el-divider>
              </span>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-plus"
                v-show="!item1.editstate"
                @click="addnewoperation(1, item1, index1)"
              >
                添加项目
              </el-button>
              <span v-show="item1.editstate">
                <el-button
                  size="mini"
                  type="primary"
                  @click="
                    updateandadd(1, item1, index1);
                    item1.editstate = false;
                  "
                >
                  保存
                </el-button>
                <el-divider direction="vertical"></el-divider>
              </span>
              <el-button
                v-show="item1.editstate"
                size="mini"
                @click="editstatechange(1, item1, index1)"
              >
                取消
              </el-button>
            </div>
          </div>
          <!-- 第二级 -->
          <div
            v-show="item1.directionstate"
            v-for="(item2, index2) in item1.secondtableData"
            :key="item2.id"
          >
            <div class="emr-container-table-self-defined-row">
              <div class="son0" style="text-align: center; padding-left: 20px">
                <svg-icon
                  v-if="!Boolean(item2.id)"
                  icon-class="new"
                  style="font-size: 36px"
                />
                <i
                  v-else-if="Boolean(item2.id) & item2.directionstate"
                  class="el-icon-arrow-down"
                  @click="item2.directionstate = false"
                ></i>
                <i
                  v-else-if="
                    Boolean(item2.id) & (item2.directionstate === false)
                  "
                  class="el-icon-arrow-right"
                  @click="loadnextlevel(2, item1, index1, item2, index2)"
                ></i>
              </div>
              <div class="son1">
                <div>
                  {{ item2.serialNum }}
                </div>
              </div>
              <div class="son2">
                <div v-if="item2.editstate">
                  <el-input
                    :class="!Boolean(item2.id) ? 'noidinput' : ''"
                    v-model.trim="item2.directoryCode"
                    placeholder="请输入项目代码"
                  ></el-input>
                </div>
                <div v-else>
                  {{ item2.directoryCode }}
                </div>
              </div>
              <div class="son3">
                <div v-if="item2.editstate">
                  <el-input
                    :class="!Boolean(item2.id) ? 'noidinput' : ''"
                    v-model.trim="item2.directoryName"
                    :placeholder="
                      item2.parentCode ? '' : '输入一级目录（工作角色）'
                    "
                  ></el-input>
                </div>
                <div v-else>
                  <span>{{ item2.directoryName }}</span>
                </div>
              </div>
              <div class="son4"></div>
              <div class="son5"></div>

              <div class="son9">
                <span v-show="!item2.editstate">
                  <el-button
                    size="mini"
                    type="text"
                    @click="item2.editstate = true"
                    >编辑</el-button
                  >
                  <el-divider direction="vertical"></el-divider>
                </span>
                <span v-show="!item2.editstate">
                  <el-popconfirm
                    title="此操作将删除该等级, 是否继续?"
                    @onConfirm="
                      deletedocumentDirectoryConfigurationbutton(
                        2,
                        item1,
                        index1,
                        item2,
                        index2
                      )
                    "
                  >
                    <el-button slot="reference" type="text" size="mini"
                      >删除</el-button
                    >
                  </el-popconfirm>
                  <el-divider direction="vertical"></el-divider>
                </span>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-plus"
                  v-show="!item2.editstate"
                  @click="addnewoperation(2, item1, index1, item2, index2)"
                  >添加子项</el-button
                >
                <span v-show="item2.editstate">
                  <el-button
                    size="mini"
                    type="primary"
                    @click="
                      updateandadd(2, item1, index1, item2, index2);
                      item2.editstate = false;
                    "
                    >保存</el-button
                  >
                  <el-divider direction="vertical"></el-divider>
                </span>
                <el-button
                  size="mini"
                  v-show="item2.editstate"
                  @click="editstatechange(2, item1, index1, item2, index2)"
                  >取消</el-button
                >
              </div>
            </div>
            <!-- 第三级 -->
            <div
              v-show="item2.directionstate"
              v-for="(item3, index3) in item2.thirdtableData"
              :key="item3.id"
            >
              <div class="emr-container-table-self-defined-row">
                <div
                  class="son0"
                  style="text-align: center; padding-left: 20px"
                >
                  <svg-icon
                    v-if="!Boolean(item3.id)"
                    icon-class="new"
                    style="font-size: 36px"
                  />
                  <i
                    v-else
                    class="el-icon-arrow-down"
                    style="color: transparent"
                  ></i>
                </div>
                <div class="son1">
                  <div v-if="item3.editstate">
                    <el-input
                      :class="!Boolean(item3.id) ? 'noidinput' : ''"
                      v-model.trim="item3.serialNum"
                      placeholder="请输入序号"
                    >
                    </el-input>
                  </div>
                  <div v-else>
                    {{ item3.serialNum }}
                  </div>
                </div>
                <div class="son2">
                  <div v-if="item3.editstate">
                    <el-input
                      :class="!Boolean(item3.id) ? 'noidinput' : ''"
                      v-model.trim="item3.directoryCode"
                      placeholder="请输入项目代码"
                    >
                    </el-input>
                  </div>
                  <div v-else>
                    {{ item3.directoryCode }}
                  </div>
                </div>
                <div class="son3">
                  <div v-if="item3.editstate">
                    <el-input
                      :class="!Boolean(item3.id) ? 'noidinput' : ''"
                      v-model.trim="item3.directoryName"
                      type="textarea"
                      :placeholder="
                        item3.parentCode ? '' : '输入一级目录（工作角色）'
                      "
                    >
                    </el-input>
                  </div>
                  <div v-else>
                    <span>{{ item3.directoryName }}</span>
                  </div>
                </div>
                <div class="son4">
                  <div>
                    <div v-if="item3.editstate">
                      <el-select v-model="item3.levelCode">
                        <el-option
                          v-for="item in levelCodeData"
                          :key="item.levelName"
                          :label="item.levelName"
                          :value="item.levelCode"
                        ></el-option>
                      </el-select>
                    </div>
                    <div v-else>{{ item3.levelCode }}级</div>
                  </div>
                </div>
                <div class="son5">
                  <div v-if="item3.editstate">
                    <el-select v-model="item3.emrRuleType">
                      <el-option
                        v-for="item in emrRuleTypeData"
                        :key="item"
                        :label="item"
                        :value="item"
                      ></el-option>
                    </el-select>
                  </div>
                  <div v-else>
                    {{ item3.emrRuleType }}
                  </div>
                </div>
                <div class="son9">
                  <span v-show="!item3.editstate">
                    <el-button
                      size="mini"
                      type="text"
                      @click="item3.editstate = true"
                      >编辑</el-button
                    >
                    <el-divider direction="vertical"></el-divider>
                  </span>
                  <el-popconfirm
                    title="此操作将删除该等级, 是否继续?"
                    @onConfirm="
                      deletedocumentDirectoryConfigurationbutton(
                        3,
                        item1,
                        index1,
                        item2,
                        index2,
                        item3,
                        index3
                      )
                    "
                  >
                    <el-button
                      slot="reference"
                      size="mini"
                      type="text"
                      v-show="!item3.editstate"
                      >删除</el-button
                    >
                  </el-popconfirm>
                  <span v-show="item3.editstate">
                    <el-button
                      size="mini"
                      type="primary"
                      @click="
                        updateandadd(
                          3,
                          item1,
                          index1,
                          item2,
                          index2,
                          item3,
                          index3
                        );
                        item3.editstate = false;
                      "
                      >保存</el-button
                    ><el-divider direction="vertical"></el-divider>
                  </span>
                  <el-button
                    size="mini"
                    v-show="item3.editstate"
                    @click="
                      editstatechange(
                        3,
                        item1,
                        index1,
                        item2,
                        index2,
                        item3,
                        index3
                      )
                    "
                    >取消</el-button
                  >
                </div>
              </div>
              <!-- 第三级 -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  querydocumentDirectoryConfiguration,
  deletedocumentDirectoryConfiguration,
  adddocumentDirectoryConfiguration,
  batchUpdate,
} from "@/api/document-management/catalogue-configuration";
import { querylevelDictionary } from "@/api/document-management/dictionary-configuration";
import Ruleprompt from "./ruleprompt";
export default {
  components: {
    Ruleprompt,
  },
  data() {
    return {
      tableData: [], // 表格数据
      loading: false,
      emrRuleTypeData: ["一致性", "完整性", "整合性", "及时性"],
      levelCodeData: [], //等级展示
      querylevelCode: "",
    };
  },
  created() {
    // 进入页面初始化查询
    this.getdocumentDirectoryConfiguration();
    // 查询等级
    querylevelDictionary({}).then((res) => {
      this.levelCodeData = res.data.list;
    });
  },
  methods: {
    // 查询列表最外层
    getdocumentDirectoryConfiguration() {
      this.loading = true;
      this.tableData = [];
      querydocumentDirectoryConfiguration({
        levelCode: this.querylevelCode,
      }).then((res) => {
        if (res.status === 0) {
          res.data.map((v) => {
            this.tableData.push(
              Object.assign(v, {
                editstate: false,
                directionstate: false,
                secondtableData: [],
              })
            );
          });
          this.loading = false;
        }
      });
    },
    // 打开子集
    loadnextlevel(type, item1, index1, item2, index2) {
      if (type === 1) {
        querydocumentDirectoryConfiguration({
          directoryCode: item1.directoryCode,
          levelCode: this.querylevelCode,
        }).then((res) => {
          if (res.status === 0) {
            let newtableData = [];
            res.data.map((v) => {
              newtableData.push(
                Object.assign(v, {
                  editstate: false,
                  directionstate: false,
                  thirdtableData: [],
                })
              );
            });
            this.$nextTick(() => {
              this.tableData[index1].secondtableData = newtableData;
            });
            this.tableData[index1].directionstate = true;
          }
        });
      } else if (type === 2) {
        querydocumentDirectoryConfiguration({
          levelCode: this.querylevelCode,
          directoryCode: item2.directoryCode,
        }).then((res) => {
          if (res.status === 0) {
            let newtableData = [];
            res.data.map((v) => {
              newtableData.push(
                Object.assign(v, { editstate: false, directionstate: false })
              );
            });
            this.$nextTick(() => {
              this.tableData[index1].secondtableData[index2].thirdtableData =
                newtableData;
            });
            this.tableData[index1].secondtableData[
              index2
            ].directionstate = true;
          }
        });
      }
    },
    // 不同新增操作，添加不同的目录
    addnewoperation(type, item1, index1, item2, index2) {
      let newobj = {
        directoryName: "",
        directoryCode: "",
        emrRuleType: "",
        levelCode: "",
        id: null,
        editstate: true,
      };
      if (type === 0) {
        this.tableData.unshift(newobj);
      } else if (type === 1) {
        this.tableData[index1].directionstate
          ? this.tableData[index1].secondtableData.unshift(newobj)
          : (this.tableData[index1].secondtableData = [newobj]);
        this.tableData[index1].directionstate = true;
      } else if (type === 2) {
        this.tableData[index1].secondtableData[index2].directionstate
          ? this.tableData[index1].secondtableData[
              index2
            ].thirdtableData.unshift(newobj)
          : (this.tableData[index1].secondtableData[index2].thirdtableData = [
              newobj,
            ]);
        this.tableData[index1].secondtableData[index2].directionstate = true;
      }
    },

    //提交保存和编辑操作
    async updateandadd(type, item1, index1, item2, index2, item3) {
      if ((type === 1) & !Boolean(item1.id)) {
        await this.adddocumentDirectoryConfiguration(item1);
        this.getdocumentDirectoryConfiguration();
      } else if ((type === 1) & Boolean(item1.id)) {
        await this.batchUpdate(item1);
      } else if ((type === 2) & !Boolean(item2.id)) {
        await this.adddocumentDirectoryConfiguration(item2);
        this.loadnextlevel(1, item1, index1);
      } else if ((type === 2) & Boolean(item2.id)) {
        await this.batchUpdate(item2);
      } else if ((type === 3) & !Boolean(item3.id)) {
        await this.adddocumentDirectoryConfiguration(item3);
        this.loadnextlevel(2, item1, index1, item2, index2);
      } else if ((type === 3) & Boolean(item3.id)) {
        await this.batchUpdate(item3);
      }
    },

    // 新增操作请求
    async adddocumentDirectoryConfiguration(item) {
      await adddocumentDirectoryConfiguration(item).then((res) => {
        if (res.status !== 0) {
          this.$message({
            message: res.msg,
            type: "error",
          });
          return;
        } else {
          this.$message({
            message: "新增成功",
            type: "success",
          });
        }
      });
    },

    // 修改操作请求
    async batchUpdate(item) {
      await batchUpdate([item]).then((res) => {
        if (res.status === 0) {
          this.$message({
            message: "保存成功!",
            type: "success",
          });
        } else {
          this.$message({
            message: res.msg,
            type: "error",
          });
        }
      });
    },

    // 改变编辑状态(取消)
    editstatechange(type, item1, index1, item2, index2, item3, index3) {
      if (type === 1) {
        if (this.tableData[index1].id) {
          querydocumentDirectoryConfiguration({
            levelCode: this.querylevelCode,
          }).then((res) => {
            if (res.status === 0) {
              this.tableData[index1].directoryName =
                res.data[index1].directoryName;
              this.tableData[index1].directoryCode =
                res.data[index1].directoryCode;
              this.tableData[index1].editstate = false;
            }
          });
        } else {
          this.tableData.splice(index1, 1);
        }
      } else if (type === 2) {
        if (this.tableData[index1].secondtableData[index2].id) {
          querydocumentDirectoryConfiguration({
            directoryCode: item1.directoryCode,
            levelCode: this.querylevelCode,
          }).then((res) => {
            if (res.status === 0) {
              this.tableData[index1].secondtableData[index2].directoryName =
                res.data[index2].directoryName;
              this.tableData[index1].secondtableData[index2].directoryCode =
                res.data[index2].directoryCode;
              this.tableData[index1].secondtableData[index2].editstate = false;
            }
          });
        } else {
          this.tableData[index1].secondtableData.splice(index2, 1);
        }
      } else if (type === 3) {
        if (
          this.tableData[index1].secondtableData[index2].thirdtableData[index3]
            .id
        ) {
          querydocumentDirectoryConfiguration({
            directoryCode: item2.directoryCode,
            levelCode: this.querylevelCode,
          }).then((res) => {
            if (res.status === 0) {
              this.tableData[index1].secondtableData[index2].thirdtableData[
                index3
              ].directoryName = res.data[index2].directoryName;
              this.tableData[index1].secondtableData[index2].thirdtableData[
                index3
              ].directoryCode = res.data[index2].directoryCode;
              this.tableData[index1].secondtableData[index2].thirdtableData[
                index3
              ].levelCode = res.data[index2].levelCode;
              this.tableData[index1].secondtableData[index2].thirdtableData[
                index3
              ].emrRuleType = res.data[index2].emrRuleType;
              this.tableData[index1].secondtableData[index2].thirdtableData[
                index3
              ].editstate = false;
            }
          });
        } else {
          this.tableData[index1].secondtableData[index2].thirdtableData.splice(
            index3,
            1
          );
        }
      }
    },

    // 删除操作
    async deletedocumentDirectoryConfigurationbutton(
      type,
      item1,
      index1,
      item2,
      index2,
      item3,
      index3
    ) {
      if (type === 1) {
        deletedocumentDirectoryConfiguration({ ids: item1.id }).then((res) => {
          if (res.status === 0) {
            this.$message({
              message: "删除成功!",
              type: "success",
            });
            this.tableData.splice(index1, 1);
          } else {
            this.$message({
              message: res.msg,
              type: "error",
            });
          }
        });
      } else if (type === 2) {
        deletedocumentDirectoryConfiguration({ ids: item2.id }).then((res) => {
          if (res.status === 0) {
            this.$message({
              message: "删除成功!",
              type: "success",
            });
            this.tableData[index1].secondtableData.splice(index2, 1);
          } else {
            this.$message({
              message: res.msg,
              type: "error",
            });
          }
        });
      } else if (type === 3) {
        deletedocumentDirectoryConfiguration({ ids: item3.id }).then((res) => {
          if (res.status === 0) {
            this.$message({
              message: "删除成功!",
              type: "success",
            });
            this.tableData[index1].secondtableData[
              index2
            ].thirdtableData.splice(index3, 1);
          } else {
            this.$message({
              message: res.msg,
              type: "error",
            });
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/emr-styles/emr-main-table.scss";
// .son0 {
//   width: 5% !important;
// }
.son1 {
  width: 10% !important;
}
.son2 {
  width: 15% !important;
}

.son3 {
  width: 15% !important;
}

.son4 {
  width: 15% !important;
}

.son5 {
  width: 15% !important;
}
.emr-container-button {
  > .el-button {
    margin-right: 20px;
  }
  > span {
    color: #555555;
    font-size: 14px;
  }
  .el-select {
    width: 100px;
  }
}
</style>
