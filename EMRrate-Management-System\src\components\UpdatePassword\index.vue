<template>
  <div class="update-pwd-container">
    <el-dialog
      v-dialogDrag
      :title="status ? '密码已过期，请修改密码' : '请修改密码'"
      :visible.sync="dialogVisible"
      :show-close="false"
      :close-on-click-modal="status ? false : true"
      width="500px">
      <el-form
        :model="ruleForm"
        status-icon
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm">
        <el-form-item
          label="旧密码"
          prop="oldPassword">
          <el-input
            type="password"
            v-model="ruleForm.oldPassword"></el-input>
        </el-form-item>
        <el-form-item
          label="新密码"
          prop="password">
          <el-input
            auto-complete="new-password"
            type="password"
            v-model="ruleForm.password"
            autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item
          label="确认密码"
          prop="checkPass">
          <el-input
            type="password"
            v-model="ruleForm.checkPass"
            autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <span
        slot="footer"
        class="dialog-footer">
        <el-button
          type="primary"
          @click="submitForm('ruleForm')"
          >提交</el-button
        >
        <el-button @click="resetForm('ruleForm')">重置</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { updateUserPasswordByOldPS } from '@/api/sys-config'
export default {
  data() {
    var validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'))
      } else {
        if (this.ruleForm.checkPass !== '') {
          this.$refs.ruleForm.validateField('checkPass')
        }
        callback()
      }
    }
    var validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.ruleForm.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      dialogVisible: false,
      rules: {
        password: [
          { required: true, validator: validatePass, trigger: 'blur' }
        ],
        checkPass: [
          { required: true, validator: validatePass2, trigger: 'blur' }
        ],
        oldPassword: [
          { required: true, message: '请输入旧密码', trigger: 'blur' }
        ]
      },
      ruleForm: {
        userId: '',
        oldPassword: '',
        checkPass: '',
        password: ''
      }
    }
  },
  props: {
    status: {
      type: Number
    }
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const data = {
            userId: this.$store.state.user.id,
            password: this.ruleForm.password,
            oldPassword: this.ruleForm.oldPassword
          }
          updateUserPasswordByOldPS(data).then(async (res) => {
            if (res.status == 0) {
              this.$message.success('修改密码成功')
              await this.$store.dispatch('user/logout')
              this.$router.push(`/login`)
              this.dialogVisible = false
            } else {
              this.$message.error(res.msg)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    openDialog() {
      this.dialogVisible = true
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>

<style lang="scss" scoped></style>
