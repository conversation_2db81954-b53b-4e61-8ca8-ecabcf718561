<template>
  <div
    :class="{ 'has-logo': true }"
    class="nav">
    <img
      class="img"
      src="@/assets/background/icon.png"
      alt="商标"
      @click="linkTo" />
    <!-- <div class="sideTop">
      <img
        class="img"
        :src="sideImg"
        alt="商标"
        @click="linkTo" />
      <div v-show="hospitalName">{{ hospitalName }}</div>
    </div> -->
    <div class="first-level-menu">
        <!-- :collapse="true" -->
      <el-menu
        :default-active="activeMenu"
        :background-color="'transparent'"
        :text-color="'black'"
        :unique-opened="false"
        mode="vertical">
        <sidebar-item
          v-for="route in permission_routes"
          :key="route.path"
          :item="route"
          :base-path="route.path" />
      </el-menu>
    </div>
    <div class="footer-fold">折叠</div>
  </div>
</template>

<script>
import Logo from '../../components/Logo.vue'
import { mapGetters } from 'vuex'
import SidebarItem from './SidebarItem'
import {
  getHospitalLogoPath as _getHospitalLogoPath,
  queryAllSysConfig as _queryAllSysConfig
} from '@/api/sys-config'
export default {
  data() {
    return {
      sideImg: '',
      hospitalName: ''
    }
  },
  components: { SidebarItem, Logo },
  mounted() {
    _getHospitalLogoPath({ logoType: 'system.logo.hospitalImg2' }).then(
      (res) => {
        this.sideImg = res.data
      }
    )
    _queryAllSysConfig().then((res) => {
      this.hospitalName = res.data.hospitalName
    })
    console.log(this.permission_routes)
  },
  computed: {
    ...mapGetters(['permission_routes']),
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    }
  },

  methods: {
    linkTo() {
      this.$router.push('/')
    }
  }
}
</script>

<style lang="scss" scoped>
.nav {
  .first-level-menu {
    height: calc(100vh - 200px);
    overflow-y: auto;
  }
}
.img {
  margin: 0 10px;
  margin-top: -10px;
  width: 180px;
  cursor: pointer;
  /* width: 70%; */
}
.sideTop {
  text-align: center;
  div {
    height: 30px;
    color: #fff;
    text-align: center;
    font-size: 20px;
    margin-bottom: 10px;
  }
}
</style>
