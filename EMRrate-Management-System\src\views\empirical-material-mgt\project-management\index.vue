<template>
  <div>
    <div class="project-header">
      <div>项目列表</div>
      <div>
        <div>
          <div
            :class="formInline.myProject === false ? 'active' : ''"
            @click="handleCommandmy(false)"
          >
            全部
            <div v-show="formInline.myProject === false"></div>
          </div>
          <div
            :class="formInline.myProject ? 'active' : ''"
            @click="handleCommandmy(true)"
          >
            我参与的
            <div v-show="formInline.myProject"></div>
          </div>
        </div>

        <div>
          <el-dropdown @command="handleCommandlevel">
            <span class="el-dropdown-link">
              评价等级<i class="el-icon-caret-bottom el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                :command="level.levelCode"
                v-for="level in levelList"
                :key="level.levelCode"
              >
                {{ level.levelName }}
              </el-dropdown-item>

              <el-dropdown-item :command="null" v-show="levelList.length > 1">
                全部
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-dropdown @command="handleCommandtime">
            <span class="el-dropdown-link">
              创建时间<i class="el-icon-caret-bottom el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                :command="time"
                v-for="time in timelist"
                :key="time"
                >{{ time }}年</el-dropdown-item
              >
              <el-dropdown-item command="" v-show="timelist.length > 1">
                全部
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>

          <svg-icon
            icon-class="prolist"
            v-if="prolist === 'list'"
            @click="prolist = 'table'"
          ></svg-icon>
          <svg-icon
            icon-class="protable"
            v-if="prolist === 'table'"
            @click="prolist = 'list'"
          ></svg-icon>
        </div>
      </div>
    </div>
    <div class="nullproject" v-show="projectList.length === 0">
      <div class="nullproject-container">
        <div>完善的质控规则，提高病历质量</div>
        <div>项目任务分配及进度 · 质量规则配置 · 文档预览导出</div>
        <div>
          <img src="./../../../assets/emrimg/icon_18.png" />
        </div>
        <div>
          <el-button
            icon="el-icon-plus"
            type="primary"
            @click="$store.commit('user/SET_ADDPROJECT', '实证材料管理')"
            >添加项目</el-button
          >
        </div>
      </div>
    </div>
    <div
      v-show="projectList.length > 0 && prolist === 'list'"
      class="project-list"
    >
      <div v-for="(item, index) in projectList" :key="index" class="card-col">
        <el-card
          class="min-card-size"
          :body-style="{ padding: '10px 20px' }"
          shadow="hover"
          @click.native="skipNext(item)"
        >
          <div><svg-icon icon-class="icon_10" />{{ item.levelName }}</div>
          <h5>{{ item.projectName }}</h5>
          <div>
            项目成员：
            <span class="membersnum">{{ item.memberCount }}人</span>

            <span v-show="item.memberCount > 0">
              (<span
                v-for="(member, index2) in item.members"
                :key="member.loginId"
                v-show="index2 < 3"
              >
                {{ member.userName }}
                <i v-show="item.memberCount > 2 && index2 < 2">、</i>
                <i v-show="item.memberCount === 2 && index2 < 1">、</i>
              </span>
              <span v-show="item.memberCount > 3">等</span>
              )
            </span>
          </div>
          <div>
            项目进度：
            <el-progress
              color="#38D695"
              :percentage="Math.trunc(item.progress * 100)"
            ></el-progress>
          </div>
          <div>
            <span>
              <span>{{ item.createBy }}</span>
              创建于{{ item.createTime.slice(0, 11) }}
            </span>
            <span @click.stop>
              <el-dropdown
                trigger="hover"
                @command="(command) => handleCommandmore(command, item)"
              >
                <span class="el-dropdown-link">
                  <svg-icon icon-class="shenglvhao" />
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="new">
                    <svg-icon icon-class="icon_newwin" />新标签页打开
                  </el-dropdown-item>
                  <el-dropdown-item command="ren">
                    <svg-icon icon-class="icon_eidt" />
                    重命名
                  </el-dropdown-item>
                  <el-dropdown-item command="members">
                    <svg-icon icon-class="icon_member" />成员管理
                  </el-dropdown-item>
                  <el-dropdown-item command="del" style="color: red">
                    <svg-icon icon-class="icon_del" />删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown></span
            >
          </div>
        </el-card>
      </div>
    </div>

    <MainCard v-show="projectList.length > 0 && prolist === 'table'">
      <el-table :data="projectList">
        <el-table-column label="评级等级" prop="roleName">
          <template slot-scope="scope">
            <svg-icon icon-class="icon_9" />{{ scope.row.levelName }}
          </template>
        </el-table-column>
        <el-table-column label="项目名称" prop="projectName"></el-table-column>
        <el-table-column label="项目成员" prop="projectName">
          <template slot-scope="scope">
            {{ scope.row.memberCount }}人
          </template>
        </el-table-column>
        <el-table-column label="项目进度" prop="projectName">
          <template slot-scope="scope">
            {{ scope.row.progress * 100 }}%
          </template>
        </el-table-column>
        <el-table-column label="创建人" prop="createBy"></el-table-column>
        <el-table-column label="创建时间" prop="createTime">
          <template slot-scope="scope">
            {{ scope.row.createTime.slice(0, 11) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" prop="projectName">
          <template slot-scope="scope">
            <el-button type="text" @click.native="skipNext(scope.row)">
              打开项目
            </el-button>
            <el-divider direction="vertical"></el-divider>
            <el-dropdown
              trigger="hover"
              @command="(command) => handleCommandmore(command, scope.row)"
            >
              <span class="el-dropdown-link">
                更多 <i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="new">
                  <svg-icon icon-class="icon_newwin" />新标签页打开
                </el-dropdown-item>
                <el-dropdown-item command="ren">
                  <svg-icon icon-class="icon_eidt" />
                  重命名
                </el-dropdown-item>
                <el-dropdown-item command="members">
                  <svg-icon icon-class="icon_member" />成员管理
                </el-dropdown-item>
                <el-dropdown-item command="del" style="color: red">
                  <svg-icon icon-class="icon_del" />删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </MainCard>
    <Addproject></Addproject>
    <Rechristenproject ref="Rechristenproject"></Rechristenproject>
    <Memberproject ref="Memberproject"></Memberproject>
  </div>
</template>

<script>
import Addproject from "../../document-management/project-management/addproject.vue";
import Rechristenproject from "../../document-management/project-management/rechristenproject.vue";
import Memberproject from "../../document-management/project-management/memberproject.vue";
import {
  deleteProjectById as _deleteProjectById,
  queryProject as _queryProject,
  queryProjectAdmin as _queryProjectAdmin,
  querylevelDictionary as _querylevelDictionary,
} from "@/api/document-management/project-management";
function defaultForm() {
  return {
    personInCharge: "",
    projectName: "",
    startTime: "",
    endTime: "",
    levelCode: "",
    myProject: false,
  };
}
export default {
  components: {
    Addproject,
    Rechristenproject,
    Memberproject,
  },
  data() {
    return {
      formInline: defaultForm(), //搜索的表单
      prolist: "list", //项目列表或者表格
      levelList: [],
      // personList: [],
      projectList: [],
      timelist: [], //从所有的列表中获取已有年份，方便后期筛查
    };
  },
  async created() {
    await _querylevelDictionary({ pageNum: 1, pageSize: 100 }).then((res) => {
      if (res && "data" in res) {
        this.levelList = res.data.list;
      }
    });
    this.queryProject("first");
    // await _queryProjectAdmin({ projectType: 0 }).then((res) => {
    //   if (res && "data" in res) {
    //     this.personList = res.data;
    //   }
    // });
  },
  methods: {
    // 查询项目
    async queryProject(data) {
      await _queryProject({ ...this.formInline, projectType: 1 }).then(
        (res) => {
          if (res && "data" in res) {
            this.projectList = res.data;
            //  第一次查询筛选时间
            if (data === "first") {
              this.projectList.forEach((item) => {
                if (!this.timelist.includes(item.createTime.slice(0, 4))) {
                  this.timelist.push(item.createTime.slice(0, 4));
                }
              });
            }
          } else {
            this.projectList = [];
          }
        }
      );
    },
    // 直接打开项目
    skipNext(item) {
      // 筛选出大权限列表
      const firstlist = this.$store.state.permission.routes
        .filter(
          (item) => this.$route.path.includes(item.path) && item.path !== "/"
        )[0]
        .children.filter((items) => this.$route.name !== items.name);
      if (firstlist.length > 0) {
        this.$router.push({
          name: firstlist[0].name,
          params: { ...item },
        });
        sessionStorage.setItem("projectactive", JSON.stringify(item));
      } else {
        this.$message.error("请联系管理员分配权限查看");
      }
    },
    //  切换是否是我参与的项目查询
    handleCommandmy(command) {
      if (command === this.formInline.myProject) {
        return;
      } else {
        this.formInline.myProject = command;
        this.queryProject();
      }
    },
    // 切换等级查询
    handleCommandlevel(command) {
      this.formInline.levelCode = command;
      this.queryProject();
    },
    // 切换时间查询项目
    handleCommandtime(command) {
      if (this.formInline.startTime.slice(0, 4) == command) {
        return;
      } else if (command != "") {
        this.formInline.startTime = command + "-01-01";
        this.formInline.endTime = command + "-12-31";
        this.queryProject();
      } else {
        this.formInline.startTime = "";
        this.formInline.endTime = "";
        this.getQueryList();
      }
    },
    // 更多操作
    handleCommandmore(command, item) {
      // 删除项目
      if (command === "del") {
        _deleteProjectById(item.id).then((res) => {
          if (res.status == 0) {
            this.queryProject();
          } else {
            this.$message.error(res.msg);
          }
        });
      }
      // 重命名项目
      else if (command === "ren") {
        this.$refs.Rechristenproject.openrechristen("重命名数据质量项目", item);
      }
      // 成员管理
      else if (command === "members") {
        this.$refs.Memberproject.openremember("数据质量项目成员管理", item);
      }
      // 新标签打开
      else if (command === "new") {
        const routeUrl = this.$router.resolve({
          path: "/redirect",
          query: { ...item },
        });
        window.open(routeUrl.href, "_blank");
      }
    },
  },
};
</script>

<style  scoped lang="scss">
.project-header {
  height: 80px;
  > div:nth-child(1) {
    font-size: 18px;
    color: #333333;
  }
  > div:nth-child(2) {
    > div:nth-child(1) {
      display: flex;
      .active {
        color: #5270dd;
      }
      div {
        cursor: pointer;
        padding-right: 10px;
        position: relative;
        div {
          background: #5270dd;
          height: 4px;
          width: 70%;
          position: relative;
          left: 15%;
          top: 4px;
        }
      }
    }
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #333333;
    align-content: center;

    .svg-icon {
      font-size: 18px;
      cursor: pointer;
    }
    // .svg-icon:nth-child(1) {
    //   font-size: 290px;
    //   color: red;
    // }
    // .svg-icon:nth-child(2) {
    //   font-size: 161px;
    //   margin-top: 200px;
    // }
    .el-dropdown {
      color: #333 !important;
    }
  }
}
.nullproject {
  min-height: calc(100vh - 170px);
  display: flex;
  justify-content: center;
  align-items: center;

  .nullproject-container {
    text-align: center;
    line-height: 40px;
    > div:nth-child(1) {
      font-weight: bold;
      font-size: 16px;
    }
    > div:nth-child(2) {
      font-size: 14px;
      color: #666666;
    }
    .el-button {
      border-radius: 30px;
      padding: 10px 30px;
    }
  }
}

.project-list {
  display: flex;
  align-items: flex-start;
  // justify-content: space-between;
  flex-flow: row wrap;
  align-content: flex-start;
  margin: 0 auto;
  .card-col {
    // width: 30%;
    min-width: 378px; /* 设置你需要的最小宽度 */
    margin-right: 2%;
    .min-card-size {
      border-radius: 10px;
      height: 172px; /* 设置你需要的最小高度 */
      width: 100%;
      border: 1px solid #e5e5e5;
      margin-bottom: 20px;
      cursor: pointer;
      .el-card__body {
        position: absolute;
        div {
          font-size: 14px;
        }
        > div:nth-child(1) {
          color: #5270dd;
          line-height: 34px;
          svg {
            font-size: 16px;
            margin-right: 5px;
          }
        }
        > h5:nth-child(2) {
          font-size: 15px;
          color: #000000;
          line-height: 28px;
          font-weight: bold;
        }
        > div:nth-child(3) {
          color: #888888;
          line-height: 20px;
          .membersnum {
            color: #000;
          }
        }
        > div:nth-child(4) {
          color: #888888;
          line-height: 28px;

          .el-progress {
            width: 200px;
            display: inline-block;
            margin-top: 10px;
            align-items: center;
          }
        }
        > div:nth-child(5) {
          color: #888888;
          line-height: 28px;
          position: relative;
          bottom: -16px;
          display: flex;
          justify-content: space-between;
          span span {
            color: #5270dd;
          }
          .el-dropdown {
            padding: 0px;
            height: 28px;
          }
        }
      }
    }
    .min-card-size:hover {
      border: 1px solid #a2b4f7;
    }
  }
}
.main-card {
  min-height: calc(100vh - 210px);
  margin-bottom: 40px;
}
::v-deep .el-table__header-wrapper {
  .has-gutter tr th {
    background: #fff;
  }
  .el-table__header .cell {
    color: #000 !important;
  }
}
.modalContainer {
  .el-form-item:nth-child(1) {
    display: block;
    .el-form-item__label {
      display: block;
    }
  }
}
::v-deep .el-dropdown-menu__item {
  svg {
    margin-right: 6px;
    font-size: 18px;
  }
}
</style>