<template>
  <div class="container">
    <!-- <vue-particles
      color="#fff"
      :particleOpacity="0.65"
      :particlesNumber="100"
      shapeType="circle"
      :particleSize="2"
      linesColor="#fff"
      :linesWidth="1"
      :lineLinked="true"
      :lineOpacity="0.4"
      :linesDistance="120"
      :moveSpeed="2"
      :hoverEffect="true"
      hoverMode="grab"
      :clickEffect="true"
      clickMode="push"
      class="lizi"
    >
    </vue-particles> -->
    <div class="logoContainer">
      <img :src="loginImg" alt="" width="300" />
    </div>
    <div class="title-container">
      <div>电子病历应用水平分级管理系统</div>
      <div>EMRrate Management System</div>
    </div>
    <div class="login-container" v-show="loginType === 1">
      <el-form
        ref="loginForm"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        auto-complete="on"
        label-position="left"
      >
        <div class="dt"></div>
        <!-- <div class="logoContainer">
          <img
            src="minIcon.png"
            alt="蓬安县妇幼保健院"
            width="80"
            height="80"
          />
            <div class="textPart">
            <div>蓬安县妇幼保健院</div>
            <div>Pengan maternal and child health care hospital</div>
          </div>
          <img
            :src="iconNC"
            alt="南充市中心医院"
            width="222"
            height="47"
          />
          <div class="textPart">
            <div>成都市第八人民医院</div>
          </div>
          <div class="textPart">
            <div>佳缘科技股份有限公司</div>
          </div>
        </div> -->
        <div class="flex-jc">
          <div class="tip">用户登录 <span>Log in</span></div>
          <el-button
            v-show="identifyType !== '2'"
            type="text"
            @click="loginChange"
            >{{ loginFlag ? "短信登录" : "账号密码登录" }}</el-button
          >
        </div>
        <el-col :span="24">
          <el-form-item prop="username">
            <span class="svg-container">
              <svg-icon icon-class="user" />
            </span>
            <el-input
              ref="username"
              v-model="loginForm.username"
              placeholder="用户名"
              name="username"
              type="text"
              tabindex="1"
              :disabled="identifyType == '2'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24" v-show="loginFlag">
          <el-form-item prop="password">
            <span class="svg-container">
              <svg-icon icon-class="password" />
            </span>
            <el-input
              :key="passwordType"
              ref="password"
              v-model="loginForm.password"
              :type="passwordType"
              placeholder="密码"
              name="password"
              tabindex="2"
              auto-complete="new-password"
              @keyup.enter.native="handleLogin"
            />
            <span class="show-pwd" @click="showPwd">
              <svg-icon
                :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'"
              />
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-show="identifyType !== '2' || !loginFlag">
          <el-form-item prop="verificationCode">
            <el-input
              placeholder="验证码"
              v-model="loginForm.verificationCode"
              type="text"
              name="verificationCode"
              tabindex="2"
              auto-complete="on"
              @keyup.enter.native="handleLogin"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <img
            :src="codeImage"
            v-show="identifyType !== '2' && loginFlag"
            style="
              margin-left: 20px;
              height: 42px;
              width: 120px;
              cursor: pointer;
            "
            @click="nextStep"
          />
          <el-button
            v-show="!loginFlag"
            style="margin-left: 20px"
            :disabled="time > 0"
            @click="sendMsg"
          >
            {{ time > 0 ? `${time}秒` : "发送短信" }}</el-button
          >
        </el-col>
        <div style="width: 100%; display: flex">
          <el-button
            :loading="loading"
            type="primary"
            class="button"
            @click.native.prevent="handleLogin"
            round
            >登 录</el-button
          >
        </div>
        <div style="width: 100%; display: flex">
          <el-link
            :loading="loading"
            class="button"
            @click.native.prevent="qywxhandleLogin"
            :underline="false"
          >
            <img :src="pic10" alt="" height="20px" width="20px" />
            企业微信</el-link
          >
          <el-link
            :loading="loading"
            class="button"
            @click.native.prevent="wxhandleLogin"
            :underline="false"
          >
            <img :src="pic11" alt="" height="20px" width="20px" />
            微信</el-link
          >
        </div>
      </el-form>
      <div
        class="copyright-tip"
        v-html="
          '技术支持：佳缘科技股份有限公司&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;联系电话：028-62122223&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;：V1.0'
        "
      ></div>
    </div>
    <div class="login-container" v-show="loginType === 2">
      <div class="qrCode" :style="{ height: tipFlag ? '450px' : '400px' }">
        <div style="margin-top: 4px">
          <el-link
            icon="el-icon-back"
            @click.native.prevent="backLogin"
            :underline="false"
          >
            返回</el-link
          >
          <span style="margin-left: 70px"> 企业微信扫码 </span>
        </div>
        <el-image :src="qyQRurl" fit="cover" loading>
          <div slot="error" class="image-slot">
            <i class="el-icon-loading"></i> 加载中...
          </div>
        </el-image>
        <div class="success" v-show="tipFlag">
          <el-button type="success" icon="el-icon-check" circle></el-button>
          扫描成功，请点击确认登录
        </div>
      </div>
    </div>
    <div class="login-container" v-show="loginType === 3">
      <div class="wxqrCode">
        <div style="margin-top: 10px">
          <el-link
            icon="el-icon-back"
            @click.native.prevent="backLogin"
            :underline="false"
          >
            返回</el-link
          >
          <span style="margin-left: 80px"> 微信扫码 </span>
        </div>
        <div
          style="margin-left: 20px; margin-top: 10px"
          id="qrcode"
          ref="qrcode"
        ></div>
      </div>
    </div>
  </div>
</template>

<script>
import Logo from "@/layout/components/Logo.vue";
import pic10 from "@/assets/sysPic/10.png";
import pic11 from "@/assets/sysPic/11.png";
import pic12 from "@/assets/sysPic/12.png";
import iconNC from "@/assets/background/dashboard-logoNC.png";
// import { queryAllSysConfig } from "@/api/sys-config"
import autoLogout from "@/mixins/autoLogout";
import {
  getQywxInfo as _getQywxInfo,
  getScanResult as _getScanResult,
  qywxLogin as _qywxLogin,
  getQrCode as _getQrCode,
  bindAccount as _bindAccount,
  getAuthUrl as _getAuthUrl,
  weixinLogin as _weixinLogin,
  getIdentifyInfo as _getIdentifyInfo,
  sendPin as _sendPin,
  getImagePin as _getImagePin,
} from "@/api/user";
import { getHospitalLogoPath as _getHospitalLogoPath } from "@/api/sys-config";
import { setToken } from "@/utils/auth";
import QRCode from "qrcodejs2";
import axios from "axios";
import { version } from "moment";
export default {
  name: "Login",
  components: {
    Logo,
  },
  mixins: [autoLogout],
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!validUsername(value)) {
        callback(new Error("请输入正确的用户名！"));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (value.length < 6) {
        callback(new Error("密码不能少于六个字符！"));
      } else {
        callback();
      }
    };
    return {
      loginForm: {
        username: "",
        password: "",
        verificationCode: "",
        phoneNumber: "",
      },
      loginRules: {
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
        verificationCode: [
          { required: true, message: "请输入验证码", trigger: "blur" },
        ],
      },
      loading: false,
      passwordType: "password",
      redirect: undefined,
      loginType: 1,
      qyQRurl: "",
      wxQRurl: "",

      pic10,
      pic11,
      pic12,
      loginImg: "",
      iconNC,
      timer: "",
      qyState: "",
      identifyType: "0",
      codeImage: "",
      time: "",
      uuid: "",
      flag: true,
      loginFlag: true,
      tipFlag: false,
      autoLogoutTime: "",
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
  },
  created() {
    _getImagePin().then((res) => {
      this.uuid = res.data.uuid;
      this.codeImage = res.data.codeImage;
      // console.log(res)
    });
     _getHospitalLogoPath({ logoType: "system.logo.hospitalImg1" }).then(
      (res) => {
        console.log(res.data);
        this.loginImg = res.data;
      }
    );
  },
  methods: {
    showPwd() {
      if (this.passwordType === "password") {
        this.passwordType = "";
      } else {
        this.passwordType = "password";
      }
      this.$nextTick(() => {
        this.$refs.password.focus();
      });
    },
    handleLogin() {
      this.loading = true;
      const data = {
        ...this.loginForm,
        identifyType: this.identifyType,
        uuid: this.uuid,
        authenticationType: this.identifyType === "2" ? "1" : "0",
      };
      this.$store
        .dispatch("user/login", data)
        .then(() => {
          this.$router.push({ path: "/" });
          this.loading = false;
          // 计时自动登出
          this.enableAutoLogout();
        })
        .catch((v) => {
          this.loading = false;
          if (v == "ADD_ACCOUNT_CODE") {
            this.loginFlag = !this.loginFlag;
            this.identifyType = "2";
            this.loginForm.verificationCode = "";
          } else if (v == "ADD_ACCOUNT_PASSWORD") {
            this.loginFlag = !this.loginFlag;
            this.identifyType = "2";
            this.loginForm.verificationCode = "";
          } else if (
            v == "CHOOSE_ACCOUNT_PASSWORD" ||
            v == "CHOOSE_ACCOUNT_CODE"
          ) {
            this.loginFlag = !this.loginFlag;
            this.loginForm.verificationCode = "";
          }
        });
    },
    wxhandleLogin() {
      this.loginType = 3;
      _getAuthUrl().then((res) => {
        // console.log(res)
        this.$refs.qrcode.innerHTML = "";
        if (res.data) {
          new QRCode(this.$refs.qrcode, {
            width: 320,
            height: 320,
            text: res.data.authUrl,
          });
          this.timer = setInterval(() => {
            _weixinLogin({ state: res.data.state }).then((result) => {
              if (result.status === 0) {
                clearInterval(this.timer);
                if (result.data.msg == "UNBOUND_ACCOUNT") {
                  this.$prompt("该微信尚未绑定账号，请先绑定账号", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                  })
                    .then(({ value }) => {
                      const data = {
                        userAccount: value,
                        userName: result.data.name,
                        wxAccount: result.data.wxAccount,
                      };
                      _bindAccount(data).then((item) => {
                        // console.log(item)
                        if (item.msg == "success") {
                          setToken(item.data);
                          this.$router.push({ path: "/" });
                        } else {
                          this.$message.error(item.msg);
                          this.loginType = 1;
                        }
                      });
                    })
                    .catch(() => {
                      this.$message({
                        type: "info",
                        message: "取消输入",
                      });
                    });
                } else {
                  setToken(result.data.token);
                  this.$router.push({ path: "/" });
                }
              } else if (result.msg == "QRCODE_SCAN_ERR") {
                clearInterval(this.timer);
                // this.$message.error("二维码过期，请重新扫码");
                // this.wxhandleLogin();
                if (this.flag) {
                  this.flag = false;
                  this.$confirm("二维码已过期, 是否刷新?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "返回",
                    type: "warning",
                  })
                    .then(() => {
                      this.wxhandleLogin();
                    })
                    .catch(() => {
                      this.backLogin();
                    });
                }
              }
            });
          }, 1000);
        }
      });
    },

    // 企业微信登录
    qywxhandleLogin() {
      this.loginType = 2;
      this.qyQRurl = "";
      this.tipFlag = false;
      _getQywxInfo().then((res) => {
        // this.qyQRurl = res.data.qrCodeUrl;
        this.qyState = res.data.state;
        if (res.data.qrCodeUrl) {
          this.loopArray({ key: res.data.key });
          this.timer = setInterval(() => {
            return this.loopArray({ key: res.data.key });
            // console.log(123);
          }, 20000);
        }
        _getQrCode({ key: res.data.key }).then((res) => {
          this.qyQRurl = "data:application/pdf;base64," + res.data;
        });
      });
    },
    loopArray(v) {
      _getScanResult(v).then((res) => {
        if (res.msg == "QRCODE_SCAN_ERR") {
          clearInterval(this.timer);
          if (this.flag) {
            this.flag = false;
            this.$confirm("二维码已过期, 是否刷新?", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "返回",
              type: "warning",
            })
              .then(() => {
                this.qywxhandleLogin();
              })
              .catch(() => {
                this.backLogin();
              });
          }
        } else if (res.msg == "QRCODE_SCAN_ING") {
          clearInterval(this.timer);
          this.tipFlag = true;
          this.flag = true;
          this.timer = setInterval(() => {
            this.loopArray(v);
          }, 1000);
        } else if (res.status == 0) {
          if (this.flag) {
            this.flag = false;
            const data = {
              code: res.data,
              state: this.qyState,
            };
            _qywxLogin(data).then((result) => {
              if (result.status == 0) {
                if (result.data.msg == "UNBOUND_ACCOUNT") {
                  this.$prompt("该企业微信尚未绑定账号，请先绑定账号", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                  })
                    .then(({ value }) => {
                      const data = {
                        userAccount: value,
                        userName: result.data.name,
                        wxAccount: result.data.wxAccount,
                      };
                      _bindAccount(data).then((item) => {
                        // console.log(item)
                        if (item.msg == "success") {
                          setToken(item.data);
                          this.$router.push({ path: "/" });
                        } else {
                          this.$message.error(item.msg);
                          this.loginType = 1;
                        }
                      });
                    })
                    .catch(() => {
                      this.$message({
                        type: "info",
                        message: "取消输入",
                      });
                    });
                } else {
                  setToken(result.data.token);
                  this.$router.push({ path: "/" });
                }
              } else {
                this.$message.error(result.msg);
                this.loginType = 1;
              }
            });
            clearInterval(this.timer);
          }
        } else if (res.msg == "扫码错误") {
          this.backLogin();
          this.$message("取消登录");
        }
      });
    },
    backLogin() {
      this.loginType = 1;
      this.flag = true;
      clearInterval(this.timer);
    },

    nextStep() {
      _getImagePin().then((res) => {
        this.uuid = res.data.uuid;
        this.codeImage = res.data.codeImage;
      });
    },
    sendMsg() {
      _sendPin({ loginId: this.loginForm.username }).then((res) => {
        if (res.status == 0) {
          this.time = 60;
          this.timer = setInterval(() => {
            if (this.time > 0) {
              this.time -= 1;
            } else {
              clearInterval(this.timer);
            }
          }, 1000);
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    loginChange() {
      this.loginFlag = !this.loginFlag;
      if (this.identifyType == "0") {
        this.identifyType = "1";
      } else {
        this.identifyType = "0";
      }
    },
  },
  beforeDestroy() {
    clearInterval(this.timer);
  },
};
</script>

<style lang="scss">
$bg: #ccc;
$cursor: #ccc;
.container {
  .el-input {
    display: inline-block;
    width: 80%;
    input {
      background: transparent;
      border: 0px;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
    }
  }
  .el-form-item {
    border: 1px solid rgba(0, 0, 0, 0.1);
    background: white;
    margin-bottom: 20px;
    .el-form-item__content {
      line-height: 22px;
    }
  }
}
.phone {
  .el-input {
    width: 100%;
  }
}
</style>

<style lang="scss" scoped>
$bg: #2ca0bb;
$dark_gray: rgba(0, 0, 0, 0.2);
$light_gray: #eee;

.container {
  min-height: 100%;
  width: 100%;
  background-image: linear-gradient(to bottom, #1688af, #2ca0bb);
  overflow: hidden;
  .lizi {
    height: 100vh;
  }
  .tip {
    margin-left: -36px;
    margin-bottom: 20px;
    font-size: 21px;

    color: rgba(0, 0, 0, 0.6);
    span {
      color: rgba(0, 0, 0, 0.2);
      font-size: 16px;
    }
  }

  .login-container {
    position: absolute;
    top: 30%;
    left: calc(50% - 250px);
    width: 520px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    .logoContainer {
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      // height: 100px;
      margin-bottom: 15px;
      display: flex;
      align-items: flex-end;
      padding-bottom: 15px;
      justify-content: space-around;

      .textPart {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        & > :nth-child(1) {
          font-size: 30px;
          letter-spacing: 2px;
        }
        & > :nth-child(2) {
          padding: 5px 0;
          font-size: 10px;
        }
      }
    }
    .login-form {
      position: relative;
      padding: 20px 55px 20px 55px;
      margin: 0 10px;
      background-color: #fcfcfc;
      border-radius: 10px;
      box-shadow: 0px 0px 10px rgb(0 0 0 / 35%);
      .dt {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 7px;
        border-top-left-radius: 7px;
        border-top-right-radius: 7px;
        background: linear-gradient(90deg, #b0d2ff, #fcc4e2);
      }
      .svg-container {
        padding: 6px 5px 6px 15px;
        color: $dark_gray;
        vertical-align: middle;
        width: 30px;
        display: inline-block;
      }
      .el-button {
        font-size: 15px;
      }
    }

    .copyright-tip {
      font-size: 13px;
      color: white;
      text-align: center;
      padding: 10px 0;
    }
  }

  .title-container {
    position: absolute;
    top: 17%;
    left: calc(50% - 270px);
    width: 560px;
    color: white;
    text-align: center;
    font-weight: 600px;
    :nth-child(1) {
      font-size: 40px;
      letter-spacing: 0px;
    }
    :nth-child(2) {
      font-size: 15px;
      letter-spacing: 4px;
      margin-top: 5px;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 12px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }

  .button {
    width: 100%;
    margin-bottom: 15px;
    margin-top: 5px;
    align-self: center;
    // background-image: linear-gradient(to left, #1688af, #2ca0bb);
  }
  .qrCode {
    width: 400px;
    // height: 400px;
    padding: 0 20px 0 20px;
    margin-left: 60px;
    background-color: #fff;
    font-size: 20px;
    line-height: 30px;
    // font-weight: bold;
  }
  .wxqrCode {
    width: 400px;
    height: 400px;
    padding: 0 20px 0 20px;
    margin-left: 60px;
    background-color: #fff;
    font-size: 20px;
    line-height: 30px;
    // font-weight: bold;
  }
  .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .yzm-container {
    padding: 6px 0px 6px 6px;
    color: $dark_gray;
    vertical-align: middle;
    width: 50px;
    display: inline-block;
  }
}
.flex-jc {
  display: flex;
  justify-content: space-between;
}
.logoContainer {
  position: absolute;
  top: 0%;
  left: 40px;
  // height: 100px;
  margin-bottom: 15px;
  margin-top: 20px;
  display: flex;
  align-items: flex-end;
  padding-bottom: 15px;
  justify-content: space-around;
}
.success {
  background-color: #fff;
  text-align: center;
}
</style>
