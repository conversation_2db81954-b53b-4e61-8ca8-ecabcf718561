<template>
  <div class="source-mgt-container">
    <h1 class="source-mgt-title">文档目录配置</h1>
    <div class="source-mgt-main">
      <div class="source-mgt-table">
        <el-table
          :data="tableData"
          ref="sourceMgtTable"
          style="width: 100%"
          border
          v-loading="loading"
          :header-cell-style="{ background: '#F5F7FA', color: '#606266' }"
        >
          <el-table-column prop="serialNum" label="序号" width="100">
            <template slot-scope="scope">
              <div
                v-if="scope.row.serialNum"
                :class="
                  scope.row.directoryCode.includes('.') ? 'right' : 'left'
                "
              >
                {{ scope.row.serialNum }}、
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="directoryName"
            label="目录名称"
            min-width="100"
          >
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.directoryName"
                placeholder="请输入目录名称"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column
            prop="directoryCode"
            label="目录编码"
            min-width="100"
          >
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.directoryCode"
                placeholder="请输入目录编码"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="levelCode" label="关联等级" min-width="50">
            <template slot-scope="scope">
              <div v-if="scope.row.levelCode >= 3">
                <el-select v-model="scope.row.levelCode">
                  <el-option
                    v-for="item in levelCodeData"
                    :key="item.levelName"
                    :label="item.levelName"
                    :value="item.levelCode"
                  ></el-option>
                </el-select>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="emrRuleType" label="关联类型" min-width="150">
            <template slot-scope="scope">
              <div v-if="scope.row.emrRuleType">
                <el-radio
                  v-model="scope.row.emrRuleType"
                  v-for="item in emrRuleTypeData"
                  :key="item"
                  :label="item"
                  >{{ item }}</el-radio
                >
              </div>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="220">
            <template slot="header" slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                icon="el-icon-plus"
                @click="handleShowDialog(0, scope, 1)"
                >新增一级</el-button
              >
            </template>
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                icon="el-icon-plus"
                :disabled="!scope.row.serialNum"
                @click="handleShowDialog(scope.$index, scope.row, 1)"
                >新增子级</el-button
              >
              <el-button
                size="mini"
                type="danger"
                icon="el-icon-delete"
                @click="
                  deletedocumentDirectoryConfiguration(scope.$index, scope.row)
                "
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-button @click="compare" type="primary"> 保存 </el-button>
        <!-- 新增/编辑数据源 -->
        <AddOrEditDataSource
          ref="addOrEditDataSource"
          :row="row"
          :levelCodeData="levelCodeData"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {
  queryAlldocumentDirectoryConfiguration,
  deletedocumentDirectoryConfiguration,
  batchSavedocumentDirectoryConfiguration,
} from "@/api/document-management/catalogue-configuration";
import { querylevelDictionary } from "@/api/document-management/dictionary-configuration";
import AddOrEditDataSource from "./components/AddOrEditDataSource.vue";
export default {
  data() {
    return {
      tableData: [], // 表格数据
      totalNum: 1,
      loading: false,
      row: {}, // 点击编辑或结构设置时整行的数据
      emrRuleTypeData: ["一致性", "完整性", "整合性", "及时性"],
      levelCodeData: [],
    };
  },
  components: {
    AddOrEditDataSource,
  },
  created() {
    // 进入页面初始化查询
    this.queryAlldocumentDirectoryConfiguration();
    querylevelDictionary({}).then((res) => {
      this.levelCodeData = res.data.list;
    });
  },
  beforeRouteLeave(to, from, next) {
    queryAlldocumentDirectoryConfiguration({}).then((res) => {
      if (JSON.stringify(this.tableData) === JSON.stringify(res.data)) {
        next();
      } else {
        this.savebox();
      }
    });
  },
  methods: {
    // 查询列表
    queryAlldocumentDirectoryConfiguration() {
      this.loading = true;
      queryAlldocumentDirectoryConfiguration({}).then((res) => {
        if (res.status === 0) {
          this.tableData = res.data;
          this.loading = false;
        }
      });
    },
    // 删除
    deletedocumentDirectoryConfiguration(index, row) {
      this.$confirm("此操作将删除该目录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deletedocumentDirectoryConfiguration({ ids: row.id }).then((res) => {
            this.loading = true;
            if (res.status === 0) {
              this.$message({
                message: "删除成功!",
                type: "success",
              });
              this.queryAlldocumentDirectoryConfiguration();
            } else {
              this.$message({
                message: res.msg,
                type: "error",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },

    // 新增子级
    handleShowDialog(index, row) {
      if (row.directoryName) {
        this.row = JSON.parse(JSON.stringify(row));
      } else {
        this.row = JSON.parse(JSON.stringify({}));
      }
      this.$refs.addOrEditDataSource.dialogFormVisible = true;
    },

    compare() {
      batchSavedocumentDirectoryConfiguration(this.tableData).then((res) => {
        this.loading = true;
        if (res.status === 0) {
          this.$message({
            message: "保存成功!",
            type: "success",
          });
          this.queryAlldocumentDirectoryConfiguration();
        } else {
          this.$message({
            message: res.msg,
            type: "error",
          });
        }
      });
    },

    //保存弹框
    savebox() {
      this.$confirm("存在未保存数据, 是否保存?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          batchSavedocumentDirectoryConfiguration(this.tableData).then(
            (res) => {
              this.loading = true;
              if (res.status === 0) {
                this.$message({
                  message: "保存成功!",
                  type: "success",
                });
                this.queryAlldocumentDirectoryConfiguration();
              } else {
                this.$message({
                  message: res.msg,
                  type: "error",
                });
              }
            }
          );
        })
        .catch(() => {
          this.queryAlldocumentDirectoryConfiguration();
          this.$message({
            type: "info",
            message: "已取消保存",
          });
        });
    },
  },
};
</script>

<style scoped lang="scss">
@import "@/styles/emr-styles/emr-main-table.scss";

.source-mgt-container {
  margin-bottom: 40px;
  .source-mgt-main {
    // padding-left: 10px;
    .source-mgt-table {
      margin-top: 10px;
    }
    .source-mgt-dialog {
      .mgt-dialog-upload {
        margin-left: 50px;
      }
    }
    .source-mgt-pag {
      margin-top: 10px;
    }
  }
}
.left {
  text-align: left;
  padding-left: 10px;
}
.right {
  text-align: right;
  padding-right: 10px;
}
</style>
