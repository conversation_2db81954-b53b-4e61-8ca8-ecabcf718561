import request from '@/utils/request'

/* 1. 规则配置 */

// 获取/查询规则配置列表
export function queryRuleConfig(data) {
  return request({
    url: '/dataquality/firstPageList',
    method: 'post',
    data
  })
}

// 获取规则配置列表（配置界面）
export function queryConfigPage(data) {
  return request({
    url: '/dataquality/ruleList',
    method: 'post',
    data
  })
}

// 获取当前系统和数据库的表
export function getTable(params) {
  return request({
    url: '/dataquality/getTable',
    method: 'get',
    params
  })
}

// 获取复制规则名
export function getCopiedSerialNumber(params) {
  return request({
    url: '/dataquality/getCopiedSerialNumber',
    method: 'get',
    params
  })
}

// 获取表对应的字段
export function getTableField(params) {
  return request({
    url: '/dataquality/getTableField',
    method: 'get',
    params
  })
}
// 获取字典中的表名
export function getDictTable(params) {
  return request({
    url: '/dataquality/getDictTable',
    method: 'get',
    params
  })
}

// 获取字典表字段
export function getDictTableFiled(params) {
  return request({
    url: '/dataquality/getDictTableFiled',
    method: 'get',
    params
  })
}
// 新增规则配置
export function addRule(data) {
  return request({
    url: '/dataquality/addRule',
    method: 'post',
    data
  })
}
// 编辑规则配置
export function updateRule(data) {
  return request({
    url: '/dataquality/updateRule',
    method: 'post',
    data
  })
}
// 根据目前的配置获取检核规则SQL
export function getCheckSql(data) {
  return request({
    url: '/dataquality/getCheckSql',
    method: 'post',
    data
  })
}
// 删除规则配置
export function deleteRule(params) {
  return request({
    url: '/dataquality/delRule',
    method: 'delete',
    params
  })
}
// 修改规则状态
export function updateRuleStatus(data) {
  return request({
    url: '/dataquality/updateRuleStatus',
    method: 'post',
    data
  })
}


