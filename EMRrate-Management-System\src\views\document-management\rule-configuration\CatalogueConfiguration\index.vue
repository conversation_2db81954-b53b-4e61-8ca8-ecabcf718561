<template>
  <div class="all">
    <!-- 头部选择等级 -->
    <div class="leveltop">
      <div>
        <svg-icon icon-class="dengji" /> 评价等级：
        <el-select
          @change="getLeftTreeList(queryLeftTreelevelCode)"
          v-model="queryLeftTreelevelCode"
          placeholder="请选择"
          size="mini"
        >
          <el-option
            v-for="item in levelCodeData"
            :key="item.levelName"
            :label="item.levelName"
            :value="item.levelCode"
          >
          </el-option>
        </el-select>
      </div>
    </div>
    <!-- 内容主体 -->
    <div class="emr-container-rule">
      <!-- 左侧树状 -->
      <div class="emr-container-left">
        <div>
          <span> 文档目录</span>
          <span
            class="aboutme"
            v-if="
              selectedProject.personInCharge.includes($store.state.user.loginId)
            "
          >
            是否和我相关
            <el-switch
              @change="getLeftTreeList(queryLeftTreelevelCode)"
              v-model="aboutme"
              active-color="#4969de"
              inactive-color="#aaa"
            >
            </el-switch>
          </span>
        </div>
        <div>
          <i
            @click="El_treeExpand($refs.tree, 0)"
            class="el-icon-arrow-down"
          ></i>
          <i @click="El_treeExpand($refs.tree, 1)" class="el-icon-arrow-up"></i>
          <!-- <el-button type="primary" @click="El_treeExpand($refs.tree)">展开/折叠</el-button> -->
        </div>
        <div class="treebox">
          <el-tree
            :data="treeData"
            default-expand-all
            node-key="label"
            ref="tree"
            highlight-current
            @node-click="handleClick"
          >
            <template #default="props">
              <el-tooltip
                v-if="props.node.label.length > 17"
                class="item"
                effect="dark"
                :content="props.node.label"
                placement="right"
              >
                <span v-if="props.data.taskStatus === '2'">
                  {{ props.node.label.substring(0, 12) }}...
                  <svg-icon
                    v-show="props.data.taskStatus === '2'"
                    icon-class="icon_success"
                    style="font-size: 24px"
                  ></svg-icon>
                </span>
                <span v-else>
                  {{ props.node.label.substring(0, 14) }}...
                  <svg-icon
                    v-show="props.data.taskStatus === '2'"
                    icon-class="icon_success"
                    style="font-size: 24px"
                  ></svg-icon>
                </span>
              </el-tooltip>
              <span v-else>
                {{ props.node.label }}
                <!-- {{props.data.taskStatus }} -->
                <svg-icon
                  v-show="props.data.taskStatus === '2'"
                  icon-class="icon_success"
                  style="font-size: 24px"
                ></svg-icon>
              </span>
            </template>
          </el-tree>
        </div>
      </div>
      <!-- 右侧表格信息 -->
      <div class="emr-container-right">
        <MainCard>
          <div class="container-main">
            <div>{{ queryRightTable.emrRuleType }}</div>
            <div v-if="queryRightTable.emrRuleType">
              考察对应评价项目中关键数据项内容与字典数据内容的{{
                queryRightTable.emrRuleType
              }}

              <el-tooltip
                placement="bottom"
                v-if="queryRightTable.emrRuleType === '一致性'"
              >
                <div slot="content">
                  <h2>数据一致性</h2>
                  <br />
                  评价要求：考察对应评价项目中关键数据项内容与字典数据内容的一致性。
                  <br />
                  计算方法：以数据字典项目为基准内容值，考察实际数据记录中与
                  <br />
                  <br />
                  <h3>
                    一致性系数=数据记录对应的项目中与字典内容一致的记录数/数据记录项的总记录数
                  </h3>
                </div>
                <i class="el-icon-question"></i>
              </el-tooltip>
              <el-tooltip
                placement="bottom"
                v-if="queryRightTable.emrRuleType === '及时性'"
              >
                <div slot="content">
                  <h2>数据及时性</h2>
                  <br />
                  根据列出时间项目清单内容进行判断，主要考察的时间项目清单以及这些项目之间的时间顺
                  序、时间间隔逻辑关系。
                  <br />
                  <br />
                  <h3>
                    数据及时性系数
                    =数据记录内容符合逻辑关系时间项数量/考察记录时间项目总数量
                  </h3>
                </div>
                <i class="el-icon-question"></i>
              </el-tooltip>
            </div>
            <div v-if="queryRightTable.emrRuleType">
              <el-button
                size="mini"
                @click="
                  $refs.Addruleconfiguration.opendialogFormVisible(
                    0,
                    queryRightTable,
                    tableData
                  )
                "
                >添加项目</el-button
              >
              <el-button
                size="mini"
                @click="$refs.TestAllSQL.dialogFormVisible = true"
                >查询当前页面所有sql</el-button
              >
            </div>
            <el-table
              v-if="queryRightTable.emrRuleType"
              ref="ruletable"
              :data="tableData.documentRuleConfigurationList"
              style="width: 100%"
              :header-cell-style="{ background: '#fff', color: '#606266' }"
            >
              <el-table-column
                prop="requiredProject"
                label="要求项目"
                min-width="110px"
              >
              </el-table-column>
              <el-table-column
                prop="hospitalProject"
                label="医院项目"
                min-width="110px"
              >
              </el-table-column>
              <el-table-column
                prop="hospitalProject"
                label="表/字段名"
                min-width="110px"
              >
                <template slot-scope="scope">
                  <el-button
                    v-if="
                      scope.row.tableFieldName1 || scope.row.tableFieldName2
                    "
                    @click="
                      $refs.Addruleconfiguration.opendialogFormVisible(
                        1,
                        scope.row,
                        tableData,
                        queryRightTable.emrRuleType
                      )
                    "
                    size="mini"
                  >
                    <svg-icon icon-class="icon_scale3" /> 打开</el-button
                  >
                  <span
                    v-else
                    class="shezhi"
                    @click="
                      $refs.Addruleconfiguration.opendialogFormVisible(
                        1,
                        scope.row,
                        tableData,
                        queryRightTable.emrRuleType
                      )
                    "
                    >未配置 <i class="el-icon-arrow-right"></i
                  ></span>
                </template>
              </el-table-column>
              <el-table-column
                prop="hospitalProject"
                label="记录数 (SQL)"
                min-width="110px"
              >
                <template slot-scope="scope">
                  <span v-show="scope.row.needStatistics == '0'">
                    <el-button
                      v-if="
                        scope.row.recordsSql || scope.row.conditionalRecordsSql
                      "
                      @click="
                        $refs.Addruleconfiguration.opendialogFormVisible(
                          2,
                          scope.row,
                          tableData,
                          queryRightTable.emrRuleType
                        )
                      "
                      size="mini"
                    >
                      <svg-icon icon-class="icon_scale3" /> 打开</el-button
                    >
                    <span
                      v-else
                      class="shezhi"
                      @click="
                        $refs.Addruleconfiguration.opendialogFormVisible(
                          2,
                          scope.row,
                          tableData,
                          queryRightTable.emrRuleType
                        )
                      "
                      >未配置 <i class="el-icon-arrow-right"></i
                    ></span>
                  </span>
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" width="210px">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    @click="
                      $refs.Addruleconfiguration.opendialogFormVisible(
                        9,
                        scope.row,
                        tableData,
                        queryRightTable.emrRuleType
                      )
                    "
                    >编辑</el-button
                  >
                  <el-button
                    size="mini"
                    type="text"
                    @click="
                      deletedocumentDirectoryConfiguration(
                        scope.$index,
                        scope.row
                      )
                    "
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <div class="complete" v-show="queryRightTable.emrRuleType">
              <el-button @click="complete()">完成</el-button>
            </div>
          </div>
        </MainCard>
      </div>
      <!-- 新增和编辑 -->
      <Addruleconfiguration
        ref="Addruleconfiguration"
        :queryRightTable="queryRightTable"
        :selectedProject="selectedProject"
      ></Addruleconfiguration>
      <!-- 测试全部 -->
      <TestAllSQL
        ref="TestAllSQL"
        :queryRightTable="queryRightTable"
        :headerobj="{
          headerName1: tableData.headerName1,
          headerName2: tableData.headerName2,
        }"
        :selectedProject="selectedProject"
      ></TestAllSQL>
    </div>
  </div>
</template>

<script>
import { queryAllow } from "@/api/document-management/dictionary-configuration";
import { queryDirectoryTreerule } from "@/api/document-management/catalogue-configuration";
import {
  querydocumentRuleConfiguration,
  savedocumentRuleConfiguration,
  markComplete,
} from "@/api/document-management/rule-configuration";
import Addruleconfiguration from "./components/addruleconfiguration.vue";
import TestAllSQL from "./components/testAllSQL.vue";

export default {
  props: {
    selectedProject: {
      type: Object, // 根据实际情况调整类型
      required: true,
    },
  },
  components: {
    Addruleconfiguration,
    TestAllSQL,
  },

  data() {
    return {
      levelCodeData: [], //等级
      treeData: [], //左侧树状信息
      queryLeftTreelevelCode: this.selectedProject.levelCode, //已选择等级
      queryRightTable: {
        // 查询右侧表数据
        directoryCode: "",
        directoryName: "",
        emrRuleType: "",
      },
      tableData: { documentRuleConfigurationList: [] }, //右侧表格绑定数据
      ceshitree: [],
      aboutme: true,
    };
  },
  watch: {
    selectedProject(newValue, oldValue) {
      this.queryRightTable = {
        // 查询右侧表数据
        directoryCode: "",
        directoryName: "",
        emrRuleType: "",
      };
      this.tableData = { documentRuleConfigurationList: [] };
      queryAllow({}).then((res) => {
        const filteredData = res.data.list.filter(
          (item) => parseInt(item.levelCode) <= this.selectedProject.levelCode
        );
        this.levelCodeData = filteredData;
        this.queryLeftTreelevelCode = this.selectedProject.levelCode;
      });
      this.getLeftTreeList(this.queryLeftTreelevelCode);
    },
  },
  async created() {
    // 初始化  等级下拉框
    await queryAllow({}).then((res) => {
      const filteredData = res.data.list.filter(
        (item) => parseInt(item.levelCode) <= this.selectedProject.levelCode
      );
      this.levelCodeData = filteredData;
    });
    this.getLeftTreeList(this.queryLeftTreelevelCode);
  },
  methods: {
    // 获取左侧树列表数据
    getLeftTreeList(val) {
      this.treeData = []; // 清空左侧树数据
      this.tableData = {}; // 将表格数据清空
      queryDirectoryTreerule({
        configType: 0,
        levelCode: val,
        needNotAllocationTask: false,
        projectId: this.selectedProject.id,
        userAccount: this.aboutme ? this.$store.state.user.loginId : "",
      }).then((res) => {
        // 格式化数组信息
        this.treeData = this.directoryredefine(res.data);
      });
    },

    // 质量文档目录重定义
    directoryredefine(arr) {
      const newData = arr.map((item) => {
        return {
          label: item["directoryName"],
          directoryCode: item["directoryCode"],
          directoryName: item["directoryName"],
          emrRuleType: item["emrRuleType"],
          issubmit: false,
          id: item["directoryName"] + item["directoryCode"],
          children: item.secondLevels.map((item2) => {
            return {
              label: item2["directoryCode"] + item2["directoryName"],
              directoryCode: item2["directoryCode"],
              directoryName: item2["directoryName"],
              emrRuleType: item2["emrRuleType"],
              id: item2["directoryName"] + item2["directoryCode"],
              issubmit: false,
              children: Object.keys(item2.thirdLevels).map((i) => {
                if (item2.thirdLevels[i].length === 1) {
                  return {
                    label:
                      i + "  :  " + item2.thirdLevels[i][0]["directoryName"],
                    issubmit: true,
                    directoryCode: item2.thirdLevels[i][0]["directoryCode"],
                    directoryName: item2.thirdLevels[i][0]["directoryName"],
                    emrRuleType: item2.thirdLevels[i][0]["emrRuleType"],
                    personInCharge: item2.thirdLevels[i][0]["personInCharge"],
                    taskStatus: item2.thirdLevels[i][0]["taskStatus"],
                    id:
                      item2.thirdLevels[i][0]["directoryName"] +
                      item2.thirdLevels[i][0]["directoryCode"] +
                      item2.thirdLevels[i][0]["emrRuleType"],
                  };
                } else if (item2.thirdLevels[i].length > 1) {
                  return {
                    label: i,
                    issubmit: false,
                    id: item2["directoryName"] + item2["directoryCode"] + i,
                    children: item2.thirdLevels[i].map((item3) => {
                      return {
                        label: item3["directoryName"],
                        issubmit: true,
                        directoryCode: item3["directoryCode"],
                        directoryName: item3["directoryName"],
                        emrRuleType: item3["emrRuleType"],
                        personInCharge: item3["personInCharge"],
                        taskStatus: item3["taskStatus"],
                        id:
                          item3["directoryName"] +
                          item3["directoryCode"] +
                          item3["emrRuleType"],
                      };
                    }),
                  };
                }
              }),
            };
          }),
        };
      });
      return newData;
    },
    // 获取右侧表格信息
    querydocumentRuleConfiguration(obj) {
      if (obj) {
        this.queryRightTable = {
          directoryCode: obj.directoryCode,
          directoryName: obj.directoryName,
          emrRuleType: obj.emrRuleType,
        };
      }
      querydocumentRuleConfiguration({
        ...this.queryRightTable,
        projectId: this.selectedProject.id,
      }).then((res) => {
        if (res.status === 0) {
          this.tableData = res.data;
        } else {
          this.$message({
            message: res.msg,
            type: "error",
          });
        }
      });
    },
    // 点击树形结构 表 或 view
    handleClick(data1, data2, data3) {
      if (data1.issubmit) {
        this.ceshitree = [data1];
        this.querydocumentRuleConfiguration(data1);
      }
    },
    // 删除
    deletedocumentDirectoryConfiguration(index, row) {
      this.$confirm("此操作将删除该规则, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.tableData.documentRuleConfigurationList.splice(index, 1);
          savedocumentRuleConfiguration({
            ...this.tableData,
            projectId: this.selectedProject.id,
          }).then((res) => {
            if (res.status === 0) {
              this.$message({
                message: "删除成功!",
                type: "success",
              });
              this.querydocumentRuleConfiguration();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 标记完成
    async complete() {
      let activetree = this.$refs.tree.getCurrentNode();
      await markComplete({
        ...this.queryRightTable,
        projectId: this.selectedProject.id,
        configType: 0,
        userAccount: this.$store.state.user.loginId,
      }).then((res) => {
        if (res.status === 0) {
          this.$message({
            message: "标记完成成功!",
            type: "success",
          });
          this.backactivetree(this.treeData, activetree);
        }
      });
    },
    backactivetree(datas, activetree) {
      datas.forEach((item) => {
        if (item.id === activetree.id) {
          item.taskStatus = "2";
        } else if (item.children && item.children.length) {
          this.backactivetree(item.children, activetree);
        }
      });
    },
    collapseNode(node) {
      node.expanded = false;
      if (node.childNodes) {
        node.childNodes.forEach((childNode) => {
          this.collapseNode(childNode);
        });
      }
    },
    expandNode(node) {
      node.expanded = true;
      if (node.childNodes) {
        node.childNodes.forEach((childNode) => {
          this.expandNode(childNode);
        });
      }
    },
    El_treeExpand(ref, v) {
      if (!ref) {
        this.$modal.alertWarning("未传入el-tree实例");
        return;
      }
      ref.store.root.childNodes.forEach((node, index) => {
        if (v) {
          this.collapseNode(node);
        } else {
          this.expandNode(node);
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
@import "@/styles/emr-styles/emr-main-table.scss";

.leveltop {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  svg {
    color: #5783e6;
  }
  .el-select {
    width: 100px;
  }
  .attachment {
    color: #5270dd;
  }
}
.el-tree {
  overflow: hidden;
  font-size: 14px;
}
.emr-container-rule {
  width: 100%;
  display: flex;
  padding-top: 20px;
  justify-content: space-around;
  .emr-container-left {
    width: 20%;
    min-width: 310px;
    border-right: 1px solid #dbdde1;
    padding-right: 10px;
    > div:nth-child(1) {
      display: flex;
      justify-content: space-between;
      span:nth-child(1) {
        font-weight: bold;
        color: #4969de;
      }
      .aboutme {
        color: #aaa;
        font-size: 14px;
      }
    }
    > div:nth-child(2) {
      margin: 10px 0px;
      i {
        border: 1px solid #cdcdcd;
        padding: 4px;
        background: #ffffff;
        border-radius: 3px;
        margin-right: 10px;
        cursor: pointer;
      }
    }
    > div:nth-child(3) {
      height: 70vh;
      overflow: scroll;
      background: transparent;
      background-repeat: 10px;
      padding: 10px;
      .el-tree {
        background: transparent;
      }
    }
  }
  .emr-container-right {
    flex: 1;
    // background: #fff;
    margin-left: 20px;
    .container-main {
      > div:nth-child(1) {
        font-weight: bold;
        font-size: 20px;
        color: #000000;
        line-height: 40px;
      }
      > div:nth-child(2) {
        font-size: 14px;
        color: #888888;
        line-height: 26px;
      }
      > div:nth-child(3) {
        line-height: 50px;
        .el-button {
          border: 1px solid #6984ea;
          color: #5270dd;
        }
      }
      // 修改UI样式
      .has-gutter tr th {
        background: #fff;
      }
    }
  }
}
::v-deep
  .el-tree--highlight-current
  .el-tree-node.is-current
  > .el-tree-node__content {
  background: #d1dbff;
  border-radius: 5px;
}

.shezhi {
  color: #e75463;
  cursor: pointer;
}
.complete {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 10px;
}
.aboutme {
  margin-left: 20px;
  color: #aaa;
  font-size: 14px;
}
</style>
