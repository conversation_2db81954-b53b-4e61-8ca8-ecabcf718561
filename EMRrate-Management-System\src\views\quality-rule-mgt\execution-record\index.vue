<template>
  <div class="execution-record-container">
    <h1 class="execution-record-title">执行记录</h1>
    <div class="execution-record-header">
      <el-form :model="queryData" ref="ruleForm" inline>
        <el-form-item label="系统及数据源" prop="dbNm">
          <el-select
            v-model="queryData.dbNm"
            placeholder="请选择"
            @change="handlerSysAndDbChange"
            clearable
          >
            <el-option label="全部" :value="null"></el-option>
            <el-option
              v-for="item in SysAndDbData"
              :key="item.dataSourceId"
              :label="item.sysAndDb"
              :value="item.dbName"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="检测表或视图" prop="checkRuleTableOrView">
          <el-autocomplete
            ref="abc"
            v-model="queryData.checkRuleTableOrView"
            :fetch-suggestions="querySearchAsync"
            value-key="tableOrViewName"
            placeholder="请输入内容"
            :disabled="isTableOrViewDisabled"
            @select="handlerTableOrViewSelect"
            clearable
            @click.native="handlerTableOrViewClick"
          ></el-autocomplete>
        </el-form-item>

        <el-form-item label="检测字段" prop="checkRuleColumn">
          <el-select
            v-model="queryData.checkRuleColumn"
            placeholder="请选择"
            :disabled="isDisabled"
            @click.native="handlerclick"
            filterable
          >
            <el-option label="全部" value=""></el-option>
            <el-option
              v-for="item in checkRuleColumnData"
              :key="item.id"
              :label="item.fieldName"
              :value="item.fieldName"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="执行规则名称" prop="checkRuleName">
          <el-input
            v-model="queryData.checkRuleName"
            placeholder="请输入系统或数据库名称"
          ></el-input>
        </el-form-item>
        <br />
        <el-form-item label="开始时间" prop="startDt">
          <el-date-picker
            type="date"
            placeholder="请选择开始日期"
            v-model="queryData.startDt"
            style="width: 100%"
            value-format="yyyy-MM-dd"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" prop="endDt">
          <el-date-picker
            type="date"
            placeholder="请选择结束时间"
            v-model="queryData.endDt"
            style="width: 100%"
            value-format="yyyy-MM-dd"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <div style="margin-left: 10px">
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="searchExecutionRecord"
              >搜索</el-button
            >
          </div>
        </el-form-item>
        <el-form-item>
          <el-button @click="resetForm('ruleForm')">重置</el-button>
        </el-form-item>
      </el-form>
      <div></div>
    </div>
    <div class="execution-record-main">
      <div class="execution-record-table">
        <el-table
          :data="tableData"
          style="width: 100%"
          border
          v-loading="loading"
          :header-cell-style="{ background: '#F5F7FA', color: '#606266' }"
        >
          <el-table-column prop="sysName" label="执行系统"></el-table-column>
          <el-table-column prop="dbNm" label="执行数据库"></el-table-column>
          <el-table-column
            prop="checkRuleName"
            label="执行规行名称"
          ></el-table-column>
          <el-table-column
            prop="checkRuleTableOrView"
            label="检测表名"
          ></el-table-column>
          <el-table-column
            prop="checkRuleColumn"
            label="检测字段名"
          ></el-table-column>
          <el-table-column prop="taskState" label="执行状态">
            <template slot-scope="scope">
              <span v-if="scope.row.taskState === '00'">执行中</span>
              <span v-else-if="scope.row.taskState === '11'">成功</span>
              <span v-else-if="scope.row.taskState === '99'">失败</span>
            </template>
          </el-table-column>
          <el-table-column prop="taskResult" label="执行结果">
            <template slot-scope="scope">
              <el-popover placement="left" width="400" trigger="hover">
                <span>{{ scope.row.taskResult }}</span>
                <el-button type="text" size="small" slot="reference"
                  >查看详情</el-button
                >
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column
            prop="startDt"
            label="任务开始时间"
          ></el-table-column>
          <el-table-column prop="endDt" label="任务结束时间"></el-table-column>
        </el-table>
      </div>
      <div class="execution-record-pag">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryData.pageNum"
          :page-sizes="[5, 10, 15, 20]"
          :page-size="queryData.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalNum"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import tableViewAndField from "@/mixins/tableViewAndField"
import {
  queryRecord,
  querySysAndDb,
} from "@/api/qualityRuleMgt/executionRecord"
import { getTable } from "@/api/qualityRuleMgt/ruleConfig"
export default {
  mixins: [tableViewAndField],
  data() {
    return {
      queryData: {
        // 查询数据
        checkRuleId: 0, // 	规则ID
        checkRuleName: "", // 检核规则名称
        checkRuleTableOrView: "", // 	检查表或视图
        checkRuleColumn: "", // 检核字段
        dbNm: "", // 数据库名称
        pageNum: 1,
        pageSize: 10,
        startDt: "", // 开始时间
        endDt: "", // 结束时间
        sysCode: "", // 检核系统代码
        sysName: "", // 检核系统名称
      },
      dataSourceId: "",
      isTableOrViewDisabled: true,
      SysAndDbData: [], // 系统及数据源数据
      tableData: [], // 表格数据
      totalNum: 1,
      loading: false,
    }
  },
  created() {
    // 初始化查询规则执行记录列表
    this.queryRecordList()
    this.querySysAndDbList()
  },
  watch: {
    queryData: {
      handler(newVal) {
        if (!newVal.checkRuleTableOrView) {
          this.isDisabled = true
          this.queryData.checkRuleColumn = ""
        }
        if (!newVal.dbNm) {
          this.isDisabled = true
          this.isTableOrViewDisabled = true
          this.queryData.checkRuleTableOrView = ""
          this.queryData.checkRuleColumn = ""
        }
      },
      deep: true,
    },
  },
  methods: {
    handlerTableOrViewClick() {
      if (this.isTableOrViewDisabled) {
        this.$message({
          type: "warning",
          message: "请先选择系统及数据源",
        })
      }
    },
    handlerSysAndDbChange(e) {
      if (e) {
        let curSelectItem = this.SysAndDbData.find((item) => {
          return item.dbName === e
        })
        this.queryData.checkRuleTableOrView = ""
        this.dataSourceId = curSelectItem.dataSourceId
        this.queryData.sysCode = curSelectItem.sysCode
        this.queryData.sysName = curSelectItem.sysName
        this.isTableOrViewDisabled = false
        this.queryCheckRuleColumnData.dataSourceId = curSelectItem.dataSourceId
      } else {
        this.isTableOrViewDisabled = true
        this.queryData.sysCode = ""
        this.queryData.sysName = ""
      }
    },
    // 搜索规则执行记录列表
    searchExecutionRecord() {
      this.queryRecordList()
    },
    // 查询规则执行记录列表
    queryRecordList() {
      this.loading = true
      queryRecord(this.queryData).then((res) => {
        if (res.status === 0) {
          this.tableData = res.data.list
          this.totalNum = res.data.total
          this.loading = false
        } else {
          this.loading = false
          this.$message({
            message: res.msg,
            type: "error",
          })
        }
      })
    },
    // 异步搜索 当前系统和数据库的表
    querySearchAsync(queryString, cb) {
      this.checkRuleTableOrViewData = []
      getTable({
        dataSourceId: this.dataSourceId,
        tableName: queryString,
      }).then((res) => {
        if (res.status === 0) {
          res.data.forEach((element, index) => {
            this.checkRuleTableOrViewData.push({
              id: index,
              tableOrViewName: element,
            })
          })
          cb(this.checkRuleTableOrViewData)
        }
      })
    },
    // 查询系统及数据源列表
    querySysAndDbList() {
      querySysAndDb().then((res) => {
        if (res.status === 0) {
          this.SysAndDbData = res.data
          console.log(this.SysAndDbData)
        }
      })
    },
    // 先选择表或视图
    handlerclick() {
      if (this.isDisabled) {
        this.$message({
          type: "warning",
          message: "请先选择检测表或视图内容",
        })
        this.$refs["abc"].focus()
      }
    },
    // 重置
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.queryRecordList()
    },
    // 当当前系统和数据库的表选择时
    handlerTableOrViewSelect(val) {
      this.isDisabled = false
      this.queryCheckRuleColumnData.tableName = val.tableOrViewName
      this.getCheckRuleColumnData()
    },
    // 改变页数
    handleCurrentChange(val) {
      this.queryData.pageNum = val
      this.queryRecordList()
    },
    // 改变页面显示条数
    handleSizeChange(val) {
      this.queryData.pageSize = val
      this.queryRecordList()
    },
  },
}
</script>

<style lang="scss" scoped>
.execution-record-container {
  margin-bottom: 40px;
  .execution-record-header {
    display: flex;
    margin: 10px 0;
    min-width: 1200px;
    .search {
      margin: 0 20px;
    }
  }
  .execution-record-main {
    // padding-left: 10px;
    .execution-record-table {
      margin-top: 10px;
    }
    .execution-record-dialog {
      .mgt-dialog-upload {
        margin-left: 50px;
      }
    }
    .execution-record-pag {
      margin-top: 10px;
    }
  }
}
</style>
