<template>
  <div
    class="container"
    :style="`height:${height ? height : 128}px;width:${width ? width : 128}px;`"
  >
    <div v-if="!src" class="upload" @click="$emit('click')">
      <i
        class="el-icon-plus icon"
        :style="iconFontSize ? `font-size:${iconFontSize}px` : ''"
      ></i>
      <slot></slot>
    </div>
    <div v-else class="imgContainer">
      <el-image :src="src" alt="图片" class="cusImg" />
      <span class="item-action-list">
        <i class="el-icon-zoom-in" @click="dialogVisible = true"></i>
        <i class="el-icon-delete" @click="$emit('update:src', null)"></i>
      </span>
    </div>
    <el-dialog v-dialogDrag :visible.sync="dialogVisible" title="浏览" top="10vh">
      <img :src="src" alt="图片" width="100%" />
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: "uploadUI",
  props: ["height", "width", "iconFontSize", "src"],
  data() {
    return {
      dialogVisible: false,
    };
  },
  
};
</script>
<style lang="scss" scoped>
.container {
  border-radius: 5px;
  overflow: hidden;
  .upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 100%;
    width: 100%;
    &:hover {
      border-color: #409eff;
    }
    .icon {
      font-size: 25px;
      color: #8c939d;
      display: block;
    }
  }

  .imgContainer {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
    position: relative;
  }
  .cusImg {
    width: 100%;
  }

  .item-action-list {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0px;
    top: 0px;
    cursor: default;
    color: rgb(255, 255, 255);
    opacity: 0;
    font-size: 20px;
    background-color: rgba(0, 0, 0, 0.5);
    transition: opacity 0.3s ease 0s;
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 0 20%;
    &:hover {
      opacity: 1;
    }
    > i {
      cursor: pointer;
    }
  }
}
</style>