export async function getFile() {
    return new Promise((resolve, reject) => {
        var input = document.createElement('input')
        input.type = 'file';
        input.multiple = false;
        input.accept=".xlsx,.xls";
        input.click();
        input.addEventListener('change', function (event) {
            if (event.target.files && event.target.files.length) {
                resolve(event.target.files);
            } else {
                reject(new Error("用户未选择文件！"))
            }
        })
    })

}
