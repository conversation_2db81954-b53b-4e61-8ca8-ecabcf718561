<template>
  <div>
    <template v-if="formData.checkRuleType === 'YZX'">
      <!-- 一致性 -->
      <el-form-item label="主表或视图" prop="checkRuleTableOrView">
        <el-autocomplete
          ref="checkRuleTableOrView"
          v-model="formData.checkRuleTableOrView"
          :fetch-suggestions="querySearchAsync"
          @select="handlerTableOrViewSelect($event, 'checkRuleTableOrView')"
          value-key="tableOrViewName"
          placeholder="请输入内容"
          clearable
        ></el-autocomplete>
      </el-form-item>
      <el-form-item label="副表或视图" prop="stCheckRuleTableOrView">
        <el-autocomplete
          ref="stCheckRuleTableOrView"
          v-model="formData.stCheckRuleTableOrView"
          @select="handlerTableOrViewSelect($event, 'stCheckRuleTableOrView')"
          :fetch-suggestions="querySearchAsync"
          value-key="tableOrViewName"
          placeholder="请输入内容"
          clearable
        ></el-autocomplete>
      </el-form-item>
      <el-form-item label="副表WHERE条件" prop="checkRuleWhere">
        <el-input type="textarea" v-model="formData.checkRuleWhere"></el-input>
      </el-form-item>
      <el-form-item label="检测字段" required>
        <el-row>
          <el-col :span="8">
            <div style="text-align: center">
              <h4>主键表字段</h4>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="text-align: center">
              <h4>副键表字段</h4>
            </div>
          </el-col>
        </el-row>
        <el-row
          v-for="(item, index) in formData.primaryForeignFileds"
          :key="index"
          :gutter="10"
        >
          <el-col :span="8">
            <el-form-item>
              <el-input
                :disabled="isCheckRuleColumnDisabled"
                @click.native="handlerclick('checkRuleColumn')"
                v-model="item.primaryKeyTableField"
              >
                <el-button slot="append">
                  <SelectFieldList
                    type="checkRuleColumn"
                    selectType="radio"
                    :index="index"
                    @click.native.stop="handlerclick('checkRuleColumn')"
                    :isDisabled="isCheckRuleColumnDisabled"
                    :checkRuleColumnData="checkRuleColumnData"
                    @backfillSelectedData="backfillSelectedData"
                  />
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-input
                :disabled="isStCheckRuleColumnDisabled"
                @click.native="handlerclick('stCheckRuleColumn')"
                v-model="item.secondaryKeyTableField"
              >
                <el-button slot="append">
                  <SelectFieldList
                    type="stCheckRuleColumn"
                    selectType="radio"
                    :index="index"
                    @click.native.stop="handlerclick('stCheckRuleColumn')"
                    :isDisabled="isStCheckRuleColumnDisabled"
                    :checkRuleColumnData="stCheckRuleColumnData"
                    @backfillSelectedData="backfillSelectedData"
                  />
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="padding-left: 20px; margin-bottom: 10px">
            <el-button
              type="primary"
              v-if="
                formData.primaryForeignFileds.length - 1 === index ||
                formData.primaryForeignFileds.length === 0
              "
              @click="addOrdeleteField()"
              >新增</el-button
            >
            <el-button
              v-if="formData.primaryForeignFileds.length > 1"
              type="danger"
              @click="addOrdeleteField(index)"
              >删除</el-button
            >
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="问题明细显示字段" prop="pbSubsidiaryColumns">
        <el-input
          :disabled="isDisabled"
          @click.native="handlerclick"
          v-model="formData.pbSubsidiaryColumns"
        >
          <el-button slot="append">
            <SelectFieldList
              type="pbSubsidiaryColumns"
              selectType="checkBox"
              @click.native.stop="handlerclick"
              :isDisabled="isDisabled"
              :checkRuleColumnData="stCheckRuleColumnData"
              @backfillSelectedData="backfillSelectedData"
            >
            </SelectFieldList>
          </el-button>
        </el-input>
      </el-form-item>
      <slot></slot>
    </template>
    <!-- 自定义一致性 -->
    <template v-if="formData.checkRuleType === 'ZDYYZX'">
      <CustomCheckRuleType :formData.sync="formData" />
    </template>
  </div>
</template>
<script>
import tableViewAndField from "@/mixins/tableViewAndField"
import CustomCheckRuleType from "@/components/CustomCheckRuleType/index.vue"
import SelectFieldList from "./common/SelectFieldList.vue"
import { getTableField } from "@/api/qualityRuleMgt/ruleConfig"
export default {
  data() {
    return {
      isCheckRuleColumnDisabled: true,
      isStCheckRuleColumnDisabled: true,
      stCheckRuleColumnData: [], // 副键表对应的字段数据
    }
  },
  components: {
    CustomCheckRuleType,
    SelectFieldList,
  },
  mixins: [tableViewAndField],
  props: {
    formData: {
      type: Object,
    },
    dataSourceId: {
      type: Number,
    },
  },
  mounted() {
    new Promise((rsl) => {
      this.queryCheckRuleColumnData.tableName =
        this.formData.checkRuleTableOrView
      this.queryCheckRuleColumnData.dataSourceId = this.dataSourceId
      this.getCheckRuleColumnData("checkRuleTableOrView", rsl)
    }).then(() => {
      this.queryCheckRuleColumnData.tableName =
        this.formData.stCheckRuleTableOrView
      this.getCheckRuleColumnData("stCheckRuleTableOrView")
    })
  },
  watch: {
    formData: {
      handler(val) {
        // 只要表单有值变化就清空获取的SQL数据
        this.$emit("clearCheckSQLData")
        // 主表或视图有值或没值时
        if (val.checkRuleTableOrView) {
          this.isDisabled = false
          this.isCheckRuleColumnDisabled = false
        } else {
          this.clearPartData("checkRuleTableOrView")
        }
        // 副表或视图有值或没值时
        if (val.stCheckRuleTableOrView) {
          this.isStCheckRuleColumnDisabled = false
        } else {
          this.clearPartData("stCheckRuleTableOrView")
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    // 主副表或视图为空时
    clearPartData(type) {
      if (type === "checkRuleTableOrView") {
        this.isCheckRuleColumnDisabled = true
        this.checkRuleColumnData = []
        this.formData.checkRuleColumn = ""
        this.formData.primaryForeignFileds.forEach((item) => {
          item.primaryKeyTableField = ""
        })
      } else if (type === "stCheckRuleTableOrView") {
        this.isDisabled = true
        this.isStCheckRuleColumnDisabled = true
        this.stCheckRuleColumnData = []
        this.formData.stCheckRuleColumn = ""
        this.formData.pbSubsidiaryColumns = ""
        this.formData.primaryForeignFileds.forEach((item) => {
          item.secondaryKeyTableField = ""
        })
      }
    },
    // 先选择表或视图
    handlerclick(type) {
      if (this.isCheckRuleColumnDisabled && type === "checkRuleColumn") {
        this.$message({
          type: "warning",
          message: "请先选择主表或视图",
        })
        this.$refs["checkRuleTableOrView"].focus()
      } else if (
        this.isStCheckRuleColumnDisabled &&
        type === "stCheckRuleColumn"
      ) {
        this.$message({
          type: "warning",
          message: "请先选择副表或视图",
        })
        this.$refs["stCheckRuleTableOrView"].focus()
      } else if (this.isDisabled) {
        this.$message({
          type: "warning",
          message: "请先选择副表或视图",
        })
        this.$refs["stCheckRuleTableOrView"].focus()
      }
    },
    // 新增或删除主副键检测字段
    addOrdeleteField(index) {
      if (index === undefined) {
        this.formData.primaryForeignFileds.push({
          primaryKeyTableField: "",
          secondaryKeyTableField: "",
        })
        return
      }
      this.formData.primaryForeignFileds.splice(index, 1)
    },
    //当主表或视图或副表或视图选择时
    handlerTableOrViewSelect(e, type) {
      if (type === "checkRuleTableOrView") {
        this.queryCheckRuleColumnData.tableName = e.tableOrViewName
        this.queryCheckRuleColumnData.dataSourceId = this.dataSourceId
        this.getCheckRuleColumnData(type)
      } else if (type === "stCheckRuleTableOrView") {
        this.queryCheckRuleColumnData.tableName = e.tableOrViewName
        this.queryCheckRuleColumnData.dataSourceId = this.dataSourceId
        this.getCheckRuleColumnData(type)
      }
    },
    // 获取表对应的字段
    getCheckRuleColumnData(type, rsl) {
      getTableField(this.queryCheckRuleColumnData).then((res) => {
        if (res.status === 0) {
          let checkRuleColumnDataArr = res.data.list
          checkRuleColumnDataArr.forEach((element, index) => {
            element.id = index
          })
          if (type === "checkRuleTableOrView") {
            this.checkRuleColumnData = checkRuleColumnDataArr
          } else {
            this.stCheckRuleColumnData = checkRuleColumnDataArr
          }
        }
        rsl && rsl()
      })
    },
    backfillSelectedData(val, type, index) {
      if (type === "checkRuleColumn") {
        this.formData.primaryForeignFileds[index].primaryKeyTableField =
          val.join(",")
      } else if (type === "stCheckRuleColumn") {
        this.formData.primaryForeignFileds[index].secondaryKeyTableField =
          val.join(",")
      } else if (type === "pbSubsidiaryColumns") {
        this.formData.pbSubsidiaryColumns = val.join(",")
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.selected {
  margin-bottom: 20px;
}
.el-form-item .el-form-item {
  display: flex;
  flex-direction: column;
  text-align: center;
}
</style>
