<template>
  <MainCard>
    <div class="code-value-type">
      <div class="value-type-header">
        <HeaderSearch>
          <template v-slot:left>
            <el-button
              type="primary"
              @click="handlerAddClick"
              >新增码值类型</el-button
            >
          </template>
          <template v-slot:right>
            <el-form
              :model="queryData"
              ref="ruleForm"
              :inline="true">
              <el-form-item
                label="类型编码"
                prop="typeCode">
                <el-input
                  v-model="queryData.typeCode"
                  placeholder="请输入类型编码"></el-input>
              </el-form-item>
              <el-form-item
                label="类型名称"
                prop="typeName">
                <el-input
                  v-model="queryData.typeName"
                  placeholder="请输入类型名称"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  @click="searchCodeValueType"
                  icon="el-icon-search"
                  >搜索</el-button
                >
              </el-form-item>
              <el-form-item>
                <el-button
                  type="danger"
                  @click="batchDeleteCodeValueType"
                  >批量删除</el-button
                >
              </el-form-item>
              <el-form-item>
                <el-button @click="resetForm('ruleForm')">重置</el-button>
              </el-form-item>
            </el-form>
          </template>
        </HeaderSearch>
      </div>
      <div class="value-type-main">
        <div class="value-type-table">
          <el-table
            :data="tableData"
            ref="collectionTaskTable"
            style="width: 100%"
            @selection-change="handleSelectionChange"
            v-loading="loading"
            :header-cell-style="{ background: '#fff', color: '#606266' }">
            <el-table-column
              type="selection"
              width="55">
            </el-table-column>
            <el-table-column
              prop="typeCode"
              label="类型编码">
              <template slot-scope="scope">
                <el-link
                  type="primary"
                  @click="
                    $router.push({
                      name: 'Code-value-content',
                      params: { typeCode: scope.row.typeCode }
                    })
                  "
                  >{{ scope.row.typeCode }}</el-link
                >
              </template>
            </el-table-column>
            <el-table-column
              prop="typeName"
              label="类型名称">
            </el-table-column>
            <el-table-column
              prop="typeDesc"
              label="类型描述">
            </el-table-column>
            <el-table-column
              prop="createTime"
              label="创建时间">
            </el-table-column>
            <el-table-column
              prop="modifyTime"
              label="修改时间">
            </el-table-column>
            <el-table-column
              label="操作"
              width="120">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  @click="handleShowDialog(scope.$index, scope.row)"
                  >编辑</el-button
                >
                <el-divider
                  style="margin: 0 2px"
                  direction="vertical"></el-divider>
                <el-button
                  type="text"
                  @click="deleteCodeValueType(scope.$index, scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="value-type-pag">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="queryData.pageNum"
            :page-sizes="[5, 10, 15, 20]"
            :page-size="queryData.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalNum">
          </el-pagination>
        </div>
      </div>
    </div>
    <!-- 新增/编辑码值类型 -->
    <AddOrEditCodeValueType
      ref="addOrEditCodeValueType"
      :btnType="btnType"
      :row="row" />
  </MainCard>
</template>

<script>
import {
  queryCodeValueType,
  deleteCodeValueType
} from '@/api/codeValueMgt/codeValueType'
import AddOrEditCodeValueType from './components/AddOrEditCodeValueType.vue'
export default {
  components: {
    AddOrEditCodeValueType
  },
  data() {
    return {
      queryData: {
        // 查询数据
        typeCode: '', // 类型编码
        typeName: '', // 类型名称
        pageNum: 1,
        pageSize: 10
      },
      btnType: 1, // 1 代表新增 0 代表编辑
      tableData: [], // 表格数据
      totalNum: 1,
      loading: false,
      row: {}, // 表格整行的数据
      codeValueTypeIds: [] // 需要删除的码值类型ID
    }
  },
  created() {
    // 进入页面初始化查询
    this.queryCodeValueTypeList()
  },
  methods: {
    // 查询码值类型列表
    queryCodeValueTypeList() {
      this.loading = true
      queryCodeValueType(this.queryData).then((res) => {
        if (res.status !== 0) {
          this.$message({
            type: 'error',
            message: res.msg
          })
          this.loading = false
          return
        }
        this.tableData = res.data.list
        this.totalNum = res.data.total
        this.loading = false
      })
    },
    // 新增
    handlerAddClick() {
      this.btnType = 1
      this.$refs.addOrEditCodeValueType.dialogFormVisible = true
    },
    // 搜索码值类型列表
    searchCodeValueType() {
      this.queryCodeValueTypeList()
    },
    // 改变页面显示条数
    handleSizeChange(val) {
      this.queryData.pageSize = val
      this.queryCodeValueTypeList()
    },
    // 重置
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.queryCodeValueTypeList()
    },
    // 改变页数
    handleCurrentChange(val) {
      this.queryData.pageNum = val
      this.queryCodeValueTypeList()
    },
    // 选择项发生变化时
    handleSelectionChange(val) {
      // console.log(val)
      this.codeValueTypeIds = val.map((item) => {
        return item.id
      })
    },
    // 删除单条码值类型
    deleteCodeValueType(index, row) {
      this.$confirm('此操作将删除码值类型, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteCodeValueType({ ids: row.id }).then((res) => {
            if (res.status === 0) {
              this.$message({
                type: 'success',
                message: '删除成功'
              })
              this.queryCodeValueTypeList()
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 批量删除码值类型
    batchDeleteCodeValueType() {
      if (this.codeValueTypeIds.length > 0) {
        this.$confirm('此操作将删除码值类型, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            deleteCodeValueType({ ids: this.codeValueTypeIds.join() }).then(
              (res) => {
                if (res.status === 0) {
                  this.$message({
                    type: 'success',
                    message: '删除成功'
                  })
                  this.queryCodeValueTypeList()
                }
              }
            )
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            })
          })
      } else {
        this.$message({
          type: 'info',
          message: '请勾选需要删除的码值类型'
        })
      }
    },
    // 打开编辑码值类型diolog
    handleShowDialog(index, row) {
      this.btnType = 0
      this.row = { ...row }
      this.$refs.addOrEditCodeValueType.dialogFormVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.code-value-type {
  .value-type-main {
    .value-type-pag {
      margin-top: 10px;
    }
  }
}
</style>
