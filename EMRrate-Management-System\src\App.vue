<template>
  <div id="app">
    <!-- <TestLogo /> -->
    <router-view />
    <UpdatePassword :status="status" ref="updatePassword" />
  </div>
</template>
<script>
import { checkUserInfoStatus } from "@/api/sys-config";
import UpdatePassword from "@/components/UpdatePassword";
import Test<PERSON>ogo from "./layout/components/TestLogo.vue";
import store from "@/store";
import { getToken, removeToken } from "@/utils/auth"; // get token from cookie
export default {
  name: "App",
  data() {
    return {
      status: 1, // 1密码已过期状态 0密码即将过期状态
    };
  },
  components: {
    UpdatePassword,
    TestLogo,
  },
  watch: {
    $route: {
      handler(newVal) {
        if (
          newVal.path !== "/login" &&
          newVal.path !== "/singleLogin" &&
          newVal.path !== "/system-settings/license-mgt"
        ) {
          checkUserInfoStatus().then((res) => {
            if (res.code === 511) {
              let isjump = false;
              store.getters.permission_routes.forEach((item) => {
                if (item.path === "/system-settings") {
                  item.children.forEach((items) => {
                    if (items.path === "license-mgt") {
                      isjump = true;
                      return;
                    }
                  });
                  return;
                }
              });
              if (isjump) {
                this.$router.push({
                  name: "license-mgt",
                });
                this.$message({
                  message: res.message + ",请上传授权文件",
                  type: "warning",
                  duration: 5 * 1000,
                });
              } else {
                this.$alert(res.message + ",请联系信息科上传授权文件", "提示", {
                  confirmButtonText: "确定",
                  type: "warning",
                  showClose: false,
                  callback: () => {
                    removeToken();
                    this.$router.push({
                      name: "login",
                    });
                  },
                });
              }
            } else if (res.status == 2009) {
              this.$confirm(`${res.msg}`, "提示", {
                confirmButtonText: "立即修改",
                cancelButtonText: "取消",
                type: "warning",
              }).then(() => {
                this.status = 0;
                this.$refs.updatePassword.dialogVisible = true;
              });
            } else if (res.status == 2008) {
              this.status = 1;
              this.$message({
                message: res.msg,
                type: "warning",
                duration: 5 * 1000,
              });
              this.$refs.updatePassword.dialogVisible = true;
            }
          });
        }
      },
    },
  },
  created() {
    window.addEventListener("beforeunload", () => {
      sessionStorage.setItem(
        "projectMsg",
        JSON.stringify(this.$store.state.user.projectMsg)
      );
    });
  },
};
</script>
