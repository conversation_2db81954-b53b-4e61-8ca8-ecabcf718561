import request from '@/utils/request'

/* 1. 规则执行 */

// 获取所有规则的系统和数据库
export function getAllSystem(params) {
  return request({
    url: '/ruleExecution/getAllSystem',
    method: 'get',
    params
  })
}
// 获取当前系统和数据库的表
export function getRuleExecutionTable(data) {
  return request({
    url: '/ruleExecution/getTable',
    method: 'post',
    data
  })
}
// 获取表对应的字段
export function getRuleExecutionTableField(data) {
  return request({
    url: '/ruleExecution/getTableField',
    method: 'post',
    data
  })
}
// 执行当前系统下所有规则
export function exeAllRuleBySys(data) {
  return request({
    url: '/ruleExecution/exeAllRuleBySys',
    method: 'post',
    data
  })
}
// 执行一条规则
export function exeOneRule(data) {
  return request({
    url: '/ruleExecution/exeOneRule',
    method: 'post',
    data
  })
}
// 执行一条规则
export function configRule(data) {
  return request({
    url: '/ruleExecution/configRule',
    method: 'post',
    data
  })
}
// 根据系统，以及其他条件查询对应的规则
export function getRuleRunInfoList(data) {
  return request({
    url: '/ruleExecution/ruleRunInfoList',
    method: 'post',
    data
  })
}
