<template>
  <h1 class="title">
    {{ title }}
    <div class="more">
      <slot></slot>
    </div>
  </h1>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
    },
  },
};
</script>>
<style scoped lang="scss">
.title {
  font-size: 19px;
  border-bottom: 2px solid #e5e7ea;
  padding-bottom: 5px;
  margin: 20px 0;
  color: #000000;
  position: relative;
  .more {
    position: absolute;
    right: 25px;
    top: 0;
    font-size: 14px;
    font-weight: normal;
    cursor: pointer;
  }
}
</style>