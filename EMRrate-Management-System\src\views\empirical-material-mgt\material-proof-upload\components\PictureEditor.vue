<template>
  <div
    class="picture-editor"
    v-loading="thumbnaillistststate"
    :element-loading-text="
      (thumbnaillist.length === 0) & (afterrequest === true)
        ? '暂无图片，请先添加图片'
        : '图片加载中，请稍后'
    "
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(255, 255, 255, 0.8)"
  >
    <div class="picture-thumbnail">
      <div
        v-for="(item, index) in thumbnaillist"
        :key="index"
        class="picture-thumbnail-item"
      >
        <div class="imagebox">
          <el-image
            :src="'data:image/png;base64,' + item.base64Str"
            style="width: 226px; max-width: 236px; max-height: 144px"
          ></el-image>
        </div>
        <div class="ordinalindex">{{ item.ordinalindex }}</div>
        <div class="operation_show1">
          <el-button
            @click="clickimage(item, index)"
            icon="el-icon-edit"
            type=""
            size="mini"
            >编辑</el-button
          >
        </div>
        <!-- <div class="operation_show2">
          <el-button @click="getImages(item)" type="" size="mini"
            ><svg-icon icon-class="icon_scale"></svg-icon
          ></el-button>
        </div> -->
      </div>
    </div>
    <el-dialog
      :visible.sync="dialogTableVisible"
      :show-close="false"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
    </el-dialog>
  </div>
</template>

<script>
// 引入样式
import ScreenShot from "js-web-screen-shot";
import {
  queryAllflowpath,
  getImages,
  uploadMultipleFiles,
} from "@/api/empirical-material-mgt";
export default {
  props: {
    selectedProject: {
      type: Object, // 根据实际情况调整类型
      required: true,
    },
    fatherMethod: {
      type: Function,
      default: null,
    },
  },
  data() {
    return {
      // 整个编辑图片弹出框
      dialogTableVisible: false,
      // 缩率图加载提示
      thumbnaillistststate: false,
      // 请求之后无图片
      afterrequest: false,
      // 编辑图片名字
      imgname: "",
      editpath: "",
      // 缩率图列表
      thumbnaillist: [],
      // 编辑图片实例
      instance: null,
      // 修改图片提交表单
      submitform: {
        directoryCode: "",
        directoryName: "",
        evaluationContentId: "",
        materialFlowPathId: "",
      },
      srcList: [],
      queryAllflowpathdata: {},
    };
  },
  methods: {
    // 点击左侧树获取图片缩率图
    async queryAllflowpath(data) {
      this.queryAllflowpathdata = JSON.parse(JSON.stringify(data));
      this.thumbnaillist = [];
      this.afterrequest = false;
      this.thumbnaillistststate = true;
      await queryAllflowpath({
        directoryCode: data.directoryCode,
        directoryName: data.directoryName,
        projectId: this.selectedProject.id,
      }).then((res) => {
        // 重新排列数组，方便展示
        if (res.data.length > 0) {
          res.data.forEach((element) => {
            element.imagesBase64List.forEach((item, index) => {
              this.thumbnaillist.push({
                ...element,
                ...item,
                ordinalindex:
                  element.serialNum +
                  (element.imagesBase64List.length >= 1
                    ? "(" + Number(index + 1) + ")"
                    : ""),
              });
            });
            this.thumbnaillistststate = false;
          });
        } else {
          this.afterrequest = true;
        }
      });
    },
    // 点击图片展开
    async clickimage(data, index) {
      this.submitform = {
        directoryCode: data.directoryCode,
        directoryName: data.directoryName,
        evaluationContentId: data.evaluationContentId,
        materialFlowPathId: data.id,
      };
      this.editpath = "";
      this.dialogTableVisible = true;
      this.imgname = data.fileName;
      await getImages({
        ...this.submitform,
        fileName: data.fileName,
        projectId: this.selectedProject.id,
      }).then((res) => {
        let blob = new Blob([res], { type: "image/jpeg/png" });
        this.editpath = window.URL.createObjectURL(blob);
        let img = new Image();
        img.src = this.editpath;
        img.onload = async () => {
          // 宽度大于分辨率，高度小于分辨率:同比例缩小宽度
          if (
            img.width > window.innerWidth &&
            img.height < window.innerHeight
          ) {
            img.height = img.height * (window.innerWidth / img.width);
            img.width = window.innerWidth;
          }
          // 宽度小于分辨率，高度大于分辨率:同比例缩小高度
          else if (
            img.width < window.innerWidth &&
            img.height > window.innerHeight
          ) {
            img.width = img.width * (window.innerHeight / img.height);
            img.height = innerHeight;
          }
          // 宽度大于分辨率，高度大于分辨率:同比例缩小高度
          else if (
            img.width > window.innerWidth &&
            img.height > window.innerHeight
          ) {
            // img.height = img.height * (window.innerHeight / img.height);
            let suoxiao =
              window.innerHeight / img.height < window.innerWidth / img.width
                ? window.innerHeight / img.height
                : window.innerWidth / img.width;
            img.height = img.height * suoxiao;
            img.width = img.width * suoxiao;
          }
        };
        setTimeout(() => {
          let positiontop =
            window.innerHeight > img.height
              ? (window.innerHeight - img.height) / 2
              : 0;
          let positionleft =
            window.innerWidth > img.width
              ? (window.innerWidth - img.width) / 2
              : 0;
          const config = {
            canvasWidth: img.width, //调整画布尺寸
            canvasHeight: img.height, //调整画布尺寸
            level: 9999, // 层级
            enableWebRtc: false, // 是否启用webrtc，值为false则使用html2canvas来截图
            loadCrossImg: false, // 跨域
            imgSrc: this.editpath,
            // cutBoxBdColor: "#A78BFA",//调整裁剪框颜色
            maxUndoNum: 30, //调整最大可撤销次数
            noScroll: true, //截图容器可滚动
            imgAutoFit: true, //开启图片自适应
            useRatioArrow: false, //是否使用等比例箭头
            showScreenData: true, //显示截图内容
            wrcWindowMode: false, //启用窗口截图模式
            toolPosition: "right", //调整工具栏展示位置
            position: { top: positiontop, left: positionleft }, //调整容器位置
            maskColor: { r: 0, g: 0, b: 0, a: 0.6 }, //调整蒙层颜色
            cropBoxInfo: { x: 0, y: 0, w: img.width, h: img.height }, //初始化裁剪框
            completeCallback: ({ base64, cutInfo }) => {
              this.uploadImg(base64);
            },
            closeCallback: this.closeFn,
            saveCallback: (code, msg) => {
              this.dialogTableVisible = false;
            },
          };
          const screenShotHandler = new ScreenShot(config);
        }, 200);
      });

      //
    },
    closeFn() {
      this.dialogTableVisible = false;
    },
    // 保存图片
    uploadImg(baseimg) {
      // const base64String = this.instance.toDataURL();
      const data = window.atob(baseimg.split(",")[1]);
      const ia = new Uint8Array(data.length);
      for (let i = 0; i < data.length; i++) {
        ia[i] = data.charCodeAt(i);
      }
      // 将图片转换成二进制形式发送给后台
      const blob = new Blob([ia], { type: "image/png" });
      // 获取上传需要的参数
      // 调用上传接口
      this.fileData = new FormData();
      const data1 = {
        ...this.submitform,
        projectId: this.selectedProject.id,
      };
      Object.keys(data1).forEach((key) => {
        const value = data1[key];
        if (Array.isArray(value)) {
          value.forEach((subValue, i) =>
            this.fileData.append(key + `[${i}]`, subValue)
          );
        } else {
          this.fileData.append(key, data1[key]);
        }
      });
      let newFile = new File([blob], this.imgname, {
        type: "png",
      }); //创建出来也是不可编辑的file对象
      this.fileData.append("files", newFile);
      uploadMultipleFiles(this.fileData)
        .then((res) => {
          if (res.status === 0) {
            this.$message({
              message: "图片编辑成功!",
              type: "success",
            });
            this.dialogTableVisible = false;
            this.queryAllflowpath(this.queryAllflowpathdata);
            this.fatherMethod();
          } else {
            this.$message({
              message: res.msg,
            });
          }
        })
        .catch((err) => {
          this.$message({
            message: err.msg,
          });
        });
    },
  },
};
</script>

<style scoped lang="scss">
.picture-editor {
  .picture-thumbnail {
    margin-bottom: 10px;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-gap: 16px;
    .picture-thumbnail-item {
      width: 240px;
      margin-bottom: 20px;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      position: relative;
      .imagebox {
        height: 144px;
        .el-image {
          // margin: 0px 10px;
          cursor: pointer;
          background: #ffffff;
          border-radius: 9px;
          border: 1px solid #dcdcdf;
        }
      }

      .ordinalindex {
        line-height: 20px;
        text-align: center;
      }
      .el-image:hover {
        opacity: 0.3;
        background: #2c3148;
      }
      .operation_show1 {
        display: none;
        position: absolute;
        top: 10px;
        left: 30px;
        .el-button {
          background: #313944;
          border: 0px solid transparent;
          padding: 8px 10px;
        }
      }
      .operation_show2 {
        display: none;
        position: absolute;
        top: 10px;
        right: 30px;
        .el-button {
          background: #ffffff;
          border: 0px solid transparent;
          padding: 8px 10px;
          color: #555555;
        }
      }
    }

    .picture-thumbnail-item:hover .operation_show1 {
      display: block;
    }
    .picture-thumbnail-item:hover .operation_show2 {
      display: block;
    }
  }
}
.tui-image-editor-container {
  height: 80vh !important;
  // width: calc(100% - 64px);
}
.el-dialog .el-dialog__body {
  padding: 0px !important;
  // display: inline;
}

.drawing-container {
  height: 900px;
}
::v-deep .el-dialog {
  height: 0px;
  display: none !important;
  overflow: scroll !important;
}
</style>