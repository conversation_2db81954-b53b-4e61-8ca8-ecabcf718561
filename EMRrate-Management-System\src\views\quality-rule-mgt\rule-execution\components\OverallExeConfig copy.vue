<template>
  <div class="overall-exe-config">
    <el-dialog v-dialogDrag
      :title="isWholeConfig ? '整体执行设置' : '执行设置'"
      :visible.sync="dialogFormVisible"
      @closed="handlerClose"
      @open="handlerOpen"
      width="36%"
      :close-on-click-modal="false"
    >
      <el-form :model="formData" ref="ruleForm" :label-width="formLabelWidth">
        <el-form-item label="系统名称">
          {{ formData.sysName }}
        </el-form-item>
        <el-form-item label="数据库名称">
          {{ formData.dbNm }}
        </el-form-item>
        <el-form-item label="执行方式" prop="jobExeType">
          <el-select v-model="formData.jobExeType" placeholder="请选择执行方式">
            <el-option label="手动执行" value="0"></el-option>
            <el-option label="定时调度" value="1"></el-option>
          </el-select>
        </el-form-item>

        <!-- 定时调度时 -->
        <template v-if="formData.jobExeType === '1'">
          <!-- 定时调度时各种频次的公共部分/每分/每天 start-->
          <el-form-item label="执行频率" prop="jobExeRate">
            <el-select
              @change="handlerSelectChange"
              v-model="formData.jobExeRate"
              placeholder="请选择执行频率"
            >
              <el-option label="每分" value="1"></el-option>
              <el-option label="每小时" value="2"></el-option>
              <el-option label="每天" value="3"></el-option>
              <el-option label="每周" value="4"></el-option>
              <el-option label="每月" value="5"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="开始日期"
            prop="jobStartDate"
            style="width: 30%; display: inline-block"
          >
            <el-date-picker
              v-model="formData.jobStartDate"
              type="date"
              placeholder="请选择开始日期"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item
            label="结束日期"
            prop="jobEndDate"
            style="width: 30%; display: inline-block; margin-left: 104px"
          >
            <el-date-picker
              v-model="formData.jobEndDate"
              type="date"
              placeholder="请选择结束日期"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>

          <el-form-item label="起始时间" prop="jobStartTime">
            <el-time-picker
              placeholder="请选择起始选择时间"
              v-model="formData.jobStartTime"
              value-format="HH:mm:ss"
              style="width: 100%"
            ></el-time-picker>
          </el-form-item>
          <!-- 定时调度时各种频次的公共部分/每分/每天 end-->

          <!-- 每小时 start-->
          <template v-if="formData.jobExeRate === '2'">
            <el-form-item label="间隔时间" prop="jobSpan">
              <el-input
                v-model="formData.jobSpan"
                placeholder="单位：小时(h)"
              ></el-input>
            </el-form-item>
          </template>
          <!-- 每小时 end-->

          <!-- 每周 start-->
          <template v-if="formData.jobExeRate === '4'">
            <el-form-item label="间隔时间" prop="jobSpan">
              <el-input
                v-model="formData.jobSpan"
                placeholder="单位：小时(h)"
              ></el-input>
            </el-form-item>
            <el-form-item label="周执行星期" ref="week" prop="weekCheckList">
              <el-checkbox-group v-model="weekCheckList">
                <el-checkbox label="2">星期一</el-checkbox>
                <el-checkbox label="3">星期二</el-checkbox>
                <el-checkbox label="4">星期三</el-checkbox>
                <el-checkbox label="5">星期四</el-checkbox>
                <el-checkbox label="6">星期五</el-checkbox>
                <el-checkbox label="7">星期六</el-checkbox>
                <el-checkbox label="1">星期日</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </template>
          <!-- 每周 end-->

          <!-- 每月 start-->
          <template v-if="formData.jobExeRate === '5'">'

            <el-form-item label="执行的月份" prop="monthCheckList">
              <el-checkbox-group v-model="monthCheckList">
                <el-checkbox label="1">一月</el-checkbox>
                <el-checkbox label="2">二月</el-checkbox>
                <el-checkbox label="3">三月</el-checkbox>
                <el-checkbox label="4">四月</el-checkbox>
                <el-checkbox label="5">五月</el-checkbox>
                <el-checkbox label="6">六月</el-checkbox>
                <el-checkbox label="7">七月</el-checkbox>
                <el-checkbox label="8">八月</el-checkbox>
                <el-checkbox label="9">九月</el-checkbox>
                <el-checkbox label="10">十月</el-checkbox>
                <el-checkbox label="11">十一月</el-checkbox>
                <el-checkbox label="12">十二月</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="执行日期">
              <el-radio-group
                v-model="formData.chkType"
                @change="handlerRadioChange"
                style="display: flex; align-items: center"
              >
                <el-radio label="1">
                  日期：
                  <el-input
                    style="width: 70px"
                    v-model="dayOfMonthValue"
                    :disabled="isDisable1"
                  ></el-input>
                </el-radio>
                <el-radio label="2">月末</el-radio>
                <el-radio label="3">
                  星期：
                  <el-select
                    style="width: 100px; display: inline-block"
                    :disabled="isDisable2"
                    v-model="formData.weekTime"
                    placeholder="请选周次"
                  >
                    <el-option
                      v-for="item in weekTimeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                  <el-select
                    :disabled="isDisable3"
                    style="width: 100px; display: inline-block"
                    v-model="formData.dayOfWeek2"
                    placeholder="请选星期"
                  >
                    <el-option
                      v-for="item in dayOfWeekOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </template>
          <!-- 每月 end-->
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="configRule">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { configRule } from "@/api/qualityRuleMgt/ruleExecution"
export default {
  data() {
    return {
      dialogFormVisible: false, // 弹框状态
      formLabelWidth: "120px",
      isShowLoading: false, // 是否显示loading
      formData: {
        // 表单数据
        checkRuleId: "", // 规则编码
        checkRuleName: "", // 规则名称
        checkRuleStatus: "", // 检核规则状态 0：启用 1：未启用
        chkMonth: "", // 执行的月份
        chkType: "", // 月的执行方式
        dayOfMonth: "", // 	月的执行日期
        dayOfWeek: "", // 周执行星期
        dayOfWeek2: "", // 月的执行星期
        jobEndDate: "", // 结束日期
        jobExeRate: "", // 执行频率
        jobExeType: "", // 执行方式
        jobId: "", // 任务ID
        jobSpan: "", // 间隔时间
        jobStartDate: "", // 开始日期
        overallSettingFlag: "", // 整体执行标志：0：单独设置 1：整体设置
        sysCode: "", // 检核系统代码
        sysName: "", // 检核系统名称
        weekTime: "", // 月的执行周次
        whetherCovered: "", // 是否覆盖：0：不覆盖 1：覆盖
      },

      weekTimeOptions: [
        {
          value: "1",
          label: "第一周",
        },
        {
          value: "2",
          label: "第二周",
        },
        {
          value: "3",
          label: "第三周",
        },
        {
          value: "4",
          label: "第四周",
        },
      ],
      dayOfWeekOptions: [
        {
          value: "2",
          label: "星期一",
        },
        {
          value: "3",
          label: "星期二",
        },
        {
          value: "4",
          label: "星期三",
        },
        {
          value: "5",
          label: "星期四",
        },
        {
          value: "6",
          label: "星期五",
        },
        {
          value: "7",
          label: "星期六",
        },
        {
          value: "1",
          label: "星期日",
        },
      ],

      dataValue: [], // 日期组件选项值
      weekCheckList: [], // 周执行星期复选框数据
      monthCheckList: [], // 月执行月份复选框数据
      dayOfMonthValue: "",

      radio: "", // 月执行月份复选框数据
      isDisable1: true,
      isDisable2: true,
      isDisable3: true,
    }
  },
  props: {
    sysName: {
      type: String,
    },
    dbNm: {
      type: String,
    },
    sysCode: {
      type: String,
    },
    isWholeConfig: {
      type: Boolean,
    },
    isCover: {
      type: Boolean,
    },
    checkRuleId: {
      type: Number,
    },
  },
  watch: {
    // 监听表单数据
    formData: {
      handler(newVal, oldVal) {
        if (newVal.jobExeType === "0") {
          // 选择手动执行时重置其他隐藏的表单值
          this.formData.jobExeRate = "1" // 执行频次默认分钟
          this.formData.jobStartDate = ""
          this.formData.jobEndDate = ""
          this.formData.jobStartTime = ""
          this.formData.jobSpan = ""
          this.formData.dayOfWeek = ""
          this.formData.chkMonth = ""
          this.formData.dayOfMonth = ""
          this.formData.chkType = ""
          this.formData.dayOfWeek2 = ""
          this.formData.weekTime = ""
        }
      },
      deep: true,
      immediate: true,
    },
    isCover: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.formData.whetherCovered = 1
        } else {
          this.formData.whetherCovered = 0
        }
      },
      immediate: true,
    },
    isWholeConfig: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.formData.overallSettingFlag = 1
        } else {
          this.formData.overallSettingFlag = 0
          this.formData.checkRuleId = this.checkRuleId
        }
      },
      immediate: true,
    },
    // 监听周执行星期复选框数据
    weekCheckList: {
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          // 重置的情况
          this.formData.dayOfWeek = ""
        } else {
          this.formData.dayOfWeek = newVal.join() // 转化成字符串
        }
      },
      deep: true,
      immediate: true,
    },
    // 监听月执行的月份复选框数据
    monthCheckList: {
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          // 重置的情况
          this.formData.chkMonth = ""
        } else {
          this.formData.chkMonth = newVal.join() // 转化成字符串
        }
      },
      deep: true,
      immediate: true,
    },
    // 监听月执行的日期 日期选项数据
    dayOfMonthValue: {
      handler(newVal, oldVal) {
        this.formData.dayOfMonth = newVal
      },
      immediate: true,
    },
  },
  methods: {
    // 当diolog打开时渲染表格数据及下拉数据
    handlerOpen() {
      this.formData.dbNm = this.dbNm
      this.formData.sysCode = this.sysCode
      this.formData.sysName = this.sysName
    },
    // 执行频次选项框发生变化时重置部分表单
    handlerSelectChange() {
      this.formData.jobSpan = ""
      this.formData.dayOfWeek = ""
      this.formData.chkMonth = ""
      this.formData.dayOfMonth = ""
      this.formData.chkType = ""
      this.formData.dayOfWeek2 = ""
      this.formData.weekTime = ""
      this.formData.jobSpan = ""
      this.weekCheckList = []
      this.monthCheckList = []
    },

    // 处理月执行日期单选框改变时
    handlerRadioChange(e) {
      if (e === "2") {
        this.formData.dayOfMonth = "L"
        this.formData.weekTime = ""
        this.formData.dayOfWeek2 = ""
        this.dayOfMonthValue = ""
        this.isDisable1 = true
        this.isDisable2 = true
        this.isDisable3 = true
      } else if (e === "1") {
        this.formData.dayOfMonth = ""
        this.formData.weekTime = ""
        this.formData.dayOfWeek2 = ""
        this.isDisable1 = false
        this.isDisable2 = true
        this.isDisable3 = true
      } else {
        this.formData.dayOfMonth = ""
        this.dayOfMonthValue = ""
        this.isDisable1 = true
        this.isDisable2 = false
        this.isDisable3 = false
      }
    },
    // 执行时间设置
    configRule() {
      configRule(this.formData).then((res) => {
        // console.log(this)
        if (res.status == 0) {
          this.$message({
            message: "设置成功!",
            type: "success",
          })
        } else {
          this.$message({
            message: "设置失败!",
            type: "error",
          })
        }
        this.dialogFormVisible = false
        this.$options.parent.getRuleRunInfoList()
      })
    },
    // 处理dialog关闭前
    handlerClose() {
      this.$refs.ruleForm.resetFields() // 重置表单
      this.weekCheckList = [] // 重置清空周执行星期复选框数据
      this.monthCheckList = [] // 重置清空周执行星期复选框数据
      this.formData.weekTime = "" // 重置月执行星期周次
      this.formData.dayOfWeek2 = "" // 重置月执行星期
      this.formData.checkRuleId = "" // id
    },
  },
}
</script>

<style lang="scss" scoped>
.overall-exe-config {
  margin-right: 10px;
}
.el-form {
  margin-right: 20px;
}
.el-select {
  display: block;
}
::v-deep .el-dialog {
  // dialog的定位
  top: -60px;
  .el-dialog__body {
    // height: 74vh;
    overflow: auto;
  }
}
</style>
