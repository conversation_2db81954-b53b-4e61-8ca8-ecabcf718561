<template>
  <div class="bar-chart" :style="{ height: height }">
    <div ref="canvas" />
  </div>
</template>

<script>
import * as echarts from "echarts/core";
import { <PERSON><PERSON><PERSON> } from "echarts/charts";
import { TooltipComponent, GridComponent } from "echarts/components";
import { CanvasRenderer } from "echarts/renderers";
echarts.use([TooltipComponent, GridComponent, BarChart, CanvasRenderer]);
const options = {
  tooltip: {},
  grid: {
    left: 30,
    right: 30,
    top: 25,
    bottom: 35,
  },
  xAxis: {
    type: "category",
    splitLine: {
      show: false,
    },
    axisTick: {
      show: false,
    },
    axisLine: {
      show: false,
    },
    axisLabel: {
      // fontWeight: "bold",
      fontWeight: 600,
      fontSize: 15,
      color: "#000000",
    },
    data: [],
  },
  yAxis: {
    show: true,
    type: "value",
    minInterval: 1,
  },
  series: [
    {
      name: "数量",
      type: "bar",
      itemStyle: {
        color: "#6ECFBD",
      },
      label: {
        show: true,
        position: "top",
        color: "#63cabe",
        fontSize: 16,
        fontWeight: 600,
      },
      barWidth: 35,
      data: [],
    },
  ],
};
export default {
  props: {
    height: {
      type: String,
      default: "210px",
    },
    chartData: Array,
  },
  data() {
    return {
      chart: null,
    };
  },
  mounted() {
    this.chart = echarts.init(this.$refs.canvas);
    this.chart.showLoading();
    if (this.chartData.length) {
      options.xAxis.data = this.chartData.map((v) => v["STANDARD_NAME"]);
      options.series[0].data = this.chartData.map((v) => {
        return {
          value: v["NUM"],
          ...v,
        };
      });
    }
    this.chart.hideLoading();
    //如果数据太少柱状图会居中
    if (options.xAxis.data.length < 5) {
      for (let i = 0; i < 5 - options.xAxis.data.length; i++) {
        options.xAxis.data.push("");
      }
    }
    this.chart && this.chart.setOption(options);
    window.addEventListener("resize", () => {
      this.chart && this.$refs.canvas && this.chart.resize();
    });
    this.chart.on("click", (params) => {
      // console.log("params",params)
      this.$emit("click", params.data);
    });
  },
  beforeDestroy() {
    this.chart && this.chart.dispose();
  },
  watch: {
    chartData(newData, oldData) {
      // console.log("newData",newData)
      options.xAxis.data = newData.map((v) => v["STANDARD_NAME"]);
      options.series[0].data = newData.map((v) => {
        return {
          value: v["NUM"],
          ...v,
        };
      });
      //如果数据太少柱状图会居中
      if (options.xAxis.data.length < 5) {
        for (let i = 0; i < 5 - options.xAxis.data.length; i++) {
          options.xAxis.data.push("");
        }
      }
      this.chart.hideLoading();
      this.chart && this.chart.setOption(options);
    },
  },
};
</script>

<style scoped lang="scss">
.bar-chart {
  > div {
    width: 100%;
    height: 100%;
  }
}
</style>
