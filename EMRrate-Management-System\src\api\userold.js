import request from '@/utils/request'

/************************ APP ************************/
export function login(data) {
  return request({
    url: '/user/doLogin',
    method: 'post',
    data
  })
}

export function singleLogin(token) {
  return request({
    url: '/user/doSsoLogin',
    method: 'post',
    params:{
      token
    }
  })
}


export function logout(token) {
  return request({
    url: '/user/loginOut',
    method: 'put',
    data:{
      token
    }
  })
}
export function getInfo(token) {
  return request({
    url: '/user/getUserInfo',
    method: 'get',
    params: { token }
  })
}

/************************ 用户管理 ************************/
//获取用户列表
export function userQueryList(data) {
  return request({
    url: '/user/queryList',
    method: 'post',
    data
  })
}
//修改用户
export function UpdateUser(data) {
  return request({
    url: '/user/updateUser',
    method: 'post',
    data
  })
}
//删除用户
export function DeleteUser(data) {
  return request({
    url: '/user/deleteUser',
    method: 'delete',
    data
  })
}
//添加用户
export function AddUser(data) {
  return request({
    url: '/user/add',
    method: 'post',
    data
  })
}
//修改用户状态
export function updateUserStatus(data) {
  return request({
    url: '/user/updateUserStatus',
    method: 'post',
    data
  })
}
//修改密码
export function resetPassWord(data){
  return request({
    url: '/user/updateUserPassword',
    method: 'post',
    data
  })
}

/************************ 角色权限管理 ************************/
//获取角色列表
export function getRoleList(params) {
  return request({
    url: '/sysRole/list',
    method: 'get',
    params
  })
}
//添加角色
export function addRole(data) {
  return request({
    url: '/sysRole/add',
    method: 'post',
    data
  })
}
//删除角色
export function deleteRole(params) {
  return request({
    url: `/sysRole/deleteById/${params.roleId}`,
    method: 'delete',
  })
}
//更新角色
export function updateRole(data) {
  return request({
    url: '/sysRole/updateRole',
    method: 'post',
    data
  })
}

/************************ 树状路径权限管理 ************************/
//获取Permissio列表
export function getPermissioList() {
  return request({
    url: '/sysPermission/list',
    method: 'get',
  })
}