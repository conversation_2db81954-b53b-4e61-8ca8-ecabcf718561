import request from '@/utils/request'

/* 1. 系统码值类型 */

// 获取/查询系统码值类型
export function queryCodeValueType(data) {
  return request({
    url: '/sysCodetabType/query',
    method: 'post',
    data
  })
}
// 新增系统码值类型
export function addCodeValueType(data) {
  return request({
    url: '/sysCodetabType/add',
    method: 'post',
    data
  })
}
// 更新系统码值类型
export function updateCodeValueType(data) {
  return request({
    url: '/sysCodetabType/modify',
    method: 'post',
    data
  })
}
// 删除系统码值类型
export function deleteCodeValueType(params) {
  return request({
    url: '/sysCodetabType/delete',
    method: 'delete',
    params
  })
}
