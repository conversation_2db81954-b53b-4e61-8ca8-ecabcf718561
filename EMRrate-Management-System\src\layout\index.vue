<template>
  <div class="layout-contianer">
    <div class="sidebar">
      <Sidebar
        @getCurMenuItem="
          (val) => {
            this.curMenuItem = val;
          }
        "
      />
    </div>
    <div class="app-main-box">
      <Navbar
        class="navbar"
        v-show="this.$route.matched[0].path != '/home-page'"
        :curMenuItem="curMenuItem"
        v-if="
          firstLevelTitle !== '实证材料管理' &&
          firstLevelTitle !== '数据质量文档'
        "
      />
      <Navbarspcia
        class="navbar"
        v-show="this.$route.matched[0].path != '/home-page'"
        :curMenuItem="curMenuItem"
        v-if="
          firstLevelTitle === '实证材料管理' ||
          firstLevelTitle === '数据质量文档'
        "
      />
      <AppMain class="main" />
    </div>
  </div>
</template>

<script>
import { Navbar, Sidebar, AppMain, Navbarspcia } from "./components";

export default {
  name: "Layout",
  components: {
    Navbar,
    Sidebar,
    AppMain,
    Navbarspcia,
  },
  data() {
    return {
      curMenuItem: {},
    };
  },
  // mixins: [ResizeMixin],

  computed: {
    sidebar() {
      return this.$store.state.app.sidebar;
    },
    device() {
      return this.$store.state.app.device;
    },
    fixedHeader() {
      return this.$store.state.settings.fixedHeader;
    },

    firstLevelTitle() {
      return this.curMenuItem.meta?.title;
    },
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === "mobile",
      };
    },
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch("app/closeSideBar", { withoutAnimation: false });
    },
  },
};
</script>

<style lang="scss" scoped>
.layout-contianer {
  display: flex;
  .sidebar {
    height: 100vh;
    background: #f6f8ff;
  }
  .app-main-box {
    flex: 1;
    height: 100vh;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    .main {
      flex: 1;
    }
  }
}
</style>
