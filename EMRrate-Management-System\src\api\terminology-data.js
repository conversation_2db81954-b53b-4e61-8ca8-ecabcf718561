import request from '@/utils/request'

/*****************术语编辑 ***********************/
//查询术语编辑列表
export function queryTermList(data) {
    return request({
        url: '/termdata/queryTermList',
        method: 'post',
        data
    })
}
//字段列表内容获取
export function getDataByTermId(data) {
    return request({
        url: '/termdata/getDataByTermId',
        method: 'post',
        data
    })
}
//获取字段列表header
export function getTableFields(termId ) {
    return request({
        url: `/termdata/getTableFields?termId=${termId}`,
        method: 'get',
    })
}
//新增修改删除  字段列表
export function dealData(data) {
    return request({
        url: '/termdata/dealData',
        method: 'post',
        data
    })
}


/**************** 术语审批 ****************/
//查询术语审批列表
export function getTermApproveList(data) {
    return request({
        url: '/termapproval/getTermApproveList',
        method: 'post',
        data
    })
}
//审批详细内容列表
export function getDetailList(data) {
    return request({
        url: '/termapproval/getDetailList',
        method: 'post',
        data
    })
}
//术语审批，变更审批保存
export function dealTermData(data) {
    return request({
        url: '/termapproval/dealTermData',
        method: 'put',
        data
    })
}

//downloadExcel
export function downloadExcel(params) {
    return request({
        url: `/termdata/downloadExcel`,
        method: 'get',
        responseType:'blob',//下载文件的时候加上，否则会乱码。
        params
    })
}
//excelImport
export function excelImport(data,termId) {
    return request({
        url: `/termdata/excelImport?termId=${termId}`,
        method: 'post',
        data
    })
}

//术语数据内容导出
export function exportTermData(data) {
    return request({
        url: `/termdata/exportTermData`,
        method: 'post',
        responseType:'blob',//下载文件的时候加上，否则会乱码。
        data
    })
}



/**************** 术语审批 ****************/
//查询术语发布列表
export function queryTermPublish(data) {
    return request({
        url: '/termpublish/queryTermPublish',
        method: 'post',
        data
    })
}
//发布术语
export function publishTermDate(data) {
    return request({
        url: '/termpublish/publishTermDate',
        method: 'put',
        data
    })
}

//获取发布术语详细信息
export function getPublishDetail(data) {
    return request({
        url: '/termpublish/getPublishDetail',
        method: 'post',
        data
    })
}
