<template>
  <div class="rule-config-container">
    <h1 class="rule-config-title">规则配置</h1>
    <div class="rule-config-header">
      <el-form :model="queryData" ref="ruleForm" inline>
        <el-form-item label="系统或数据库名称" prop="sysOrDbname">
          <el-input
            v-model="queryData.sysOrDbname"
            placeholder="请输入系统或数据库名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="最近更新时间">
          <el-row>
            <el-col :span="11">
              <el-form-item prop="startDt">
                <el-date-picker
                  type="date"
                  placeholder="开始日期"
                  v-model="queryData.startDt"
                  style="width: 100%"
                  value-format="yyyy-MM-dd"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col class="line" :span="2">-</el-col>
            <el-col :span="11">
              <el-form-item prop="endDt">
                <el-date-picker
                  style="width: 100%"
                  value-format="yyyy-MM-dd"
                  type="date"
                  placeholder="结束日期"
                  v-model="queryData.endDt"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>
        <!-- <el-form-item label="执行方式" prop="jobExeType">
          <el-select
            v-model="queryData.jobExeType"
            placeholder="请选择执行方式"
          >
            <el-option label="全部" value=""></el-option>
            <el-option label="手动执行" value="0"></el-option>
            <el-option label="定时调度" value="1"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-button
            type="primary"
            @click="searchDataSource"
            icon="el-icon-search"
            >搜索</el-button
          >
        </el-form-item>
        <el-form-item>
          <el-button @click="resetForm('ruleForm')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="rule-config-main">
      <div class="rule-config-table">
        <el-table
          :data="tableData"
          style="width: 100%"
          border
          v-loading="loading"
          :header-cell-style="{ background: '#F5F7FA', color: '#606266' }"
        >
          <el-table-column prop="sysCode" label="系统编码"> </el-table-column>
          <el-table-column prop="sysName" label="系统名称"> </el-table-column>
          <el-table-column prop="databaseName" label="数据库名称">
          </el-table-column>
          <el-table-column prop="ruleNum" label="质量管理规则数">
          </el-table-column>
          <el-table-column prop="updateTime" label="最近更新时间">
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                icon="el-icon-edit-outline"
                @click="handleShowDialog(scope.$index, scope.row, 1)"
                >规则配置</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="rule-config-pag">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryData.pageNum"
          :page-sizes="[5, 10, 15, 20]"
          :page-size="queryData.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalNum"
        >
        </el-pagination>
      </div>
      <!-- 配置任务组 -->
      <RuleConfig ref="configureTaskGroup" :row="row" />
    </div>
  </div>
</template>

<script>
import { queryRuleConfig } from "@/api/qualityRuleMgt/ruleConfig"
import RuleConfig from "./components/RuleConfig.vue"
export default {
  components: {
    RuleConfig,
  },
  data() {
    return {
      queryData: {
        // 查询数据
        sysOrDbname: "", // 系统或数据库名称
        startDt: "",
        endDt: "",
        pageNum: 1,
        pageSize: 10,
        jobExeType: "", // 执行方式：0：手动执行，1：定时调度
      },
      tableData: [], // 表格数据
      totalNum: 1,
      loading: false,
      dataSourceIds: [], // 需要删除的数据源的ID
      row: {}, // 点击编辑时整行的数据
    }
  },
  created() {
    // 进入页面初始化查询
    this.getRuleConfigList()
  },
  methods: {
    // 获取规则配置列表
    getRuleConfigList() {
      this.loading = true
      queryRuleConfig(this.queryData).then((res) => {
        if (res.status === 0) {
          this.tableData = res.data.list
          this.totalNum = res.data.total
          this.loading = false
        } else {
          this.$message({
            type: "error",
            message: res.msg,
          })
          this.loading = false
        }
      })
    },
    // 查询搜索规则配置列表
    searchDataSource() {
      this.getRuleConfigList()
    },
    handleShowDialog(index, row) {
      this.$refs.configureTaskGroup.dialogFormVisible = true
      this.row = row
    },
    // 重置
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.getRuleConfigList()
    },
    // 改变页面显示条数
    handleSizeChange(val) {
      this.queryData.pageSize = val
      this.getRuleConfigList()
    },
    // 改变页数
    handleCurrentChange(val) {
      this.queryData.pageNum = val
      this.getRuleConfigList()
    },
  },
}
</script>

<style lang="scss" scoped>
.rule-config-container {
  margin-bottom: 40px;
  .rule-config-header {
    display: flex;
    margin: 10px 0;
    min-width: 1200px;
    .line {
      text-align: center;
    }
  }
  .rule-config-main {
    .rule-config-table {
      margin-top: -20px;
    }
    .rule-config-dialog {
      .mgt-dialog-upload {
        margin-left: 50px;
      }
    }
    .rule-config-pag {
      margin-top: 10px;
    }
  }
}
</style>
