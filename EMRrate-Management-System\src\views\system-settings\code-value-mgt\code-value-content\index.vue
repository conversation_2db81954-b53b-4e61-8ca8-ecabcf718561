<template>
  <MainCard>
    <div class="code-value-content">
      <div class="value-content-header">
        <HeaderSearch>
          <template v-slot:left>
            <el-button
              type="primary"
              @click="handlerAddClick"
              >新增码值内容</el-button
            >
          </template>
          <template v-slot:right>
            <el-form
              :model="queryData"
              ref="ruleForm"
              :inline="true">
              <el-form-item
                label="类型编码"
                prop="typeCode">
                <el-input
                  v-model="queryData.typeCode"
                  placeholder="请输入类型编码"></el-input>
              </el-form-item>
              <el-form-item
                label="键名"
                prop="contentKey">
                <el-input
                  v-model="queryData.contentKey"
                  placeholder="请输入键名"></el-input>
              </el-form-item>
              <el-form-item>
                <div style="margin-left: 10px">
                  <el-button
                    type="primary"
                    @click="searchCodeValueContent"
                    icon="el-icon-search"
                    >搜索</el-button
                  >
                </div>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="danger"
                  @click="batchDeleteCodeValueContent"
                  >批量删除</el-button
                >
              </el-form-item>
              <el-form-item>
                <el-button @click="resetForm('ruleForm')">重置</el-button>
              </el-form-item>
            </el-form>
          </template>
        </HeaderSearch>
        <AddOrEditCodeValContent
          :btnType="btnType"
          ref="addOrEditCodeValContent"
          :row="row" />
      </div>
      <div class="value-content-main">
        <div class="value-content-table">
          <el-table
            :data="tableData"
            ref="valueContentTable"
            style="width: 100%"
            @selection-change="handleSelectionChange"
            v-loading="loading"
            :header-cell-style="{ background: '#fff', color: '#606266' }">
            <el-table-column
              type="selection"
              width="55">
            </el-table-column>
            <el-table-column
              prop="typeCode"
              label="编码">
            </el-table-column>
            <el-table-column
              prop="contentKey"
              label="键">
            </el-table-column>
            <el-table-column
              prop="contentValue"
              label="值">
            </el-table-column>
            <el-table-column
              prop="contentDesc"
              label="描述">
            </el-table-column>
            <el-table-column
              prop="contentSeq"
              label="排序">
            </el-table-column>
            <el-table-column
              prop="data1"
              label="备用1">
            </el-table-column>
            <el-table-column
              prop="data2"
              label="备用2">
            </el-table-column>
            <el-table-column
              prop="data3"
              label="备用3">
            </el-table-column>
            <el-table-column
              prop="data4"
              label="备用4">
            </el-table-column>
            <el-table-column
              prop="data5"
              label="备用5">
            </el-table-column>
            <el-table-column
              label="操作"
              width="120">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  @click="handleShowDialog(scope.$index, scope.row)"
                  >编辑</el-button
                >
                <el-divider
                  style="margin: 0 2px"
                  direction="vertical"></el-divider>
                <el-button
                  type="text"
                  @click="deleteCodeValueContent(scope.$index, scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="value-content-pag">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="queryData.pageNum"
            :page-sizes="[5, 10, 15, 20]"
            :page-size="queryData.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalNum">
          </el-pagination>
        </div>
      </div>
    </div>
  </MainCard>
</template>

<script>
import {
  queryCodeValueContent,
  deleteCodeValueContent
} from '@/api/codeValueMgt/codeValueContent'
import AddOrEditCodeValContent from './components/AddOrEditCodeValContent.vue'
export default {
  components: {
    AddOrEditCodeValContent
  },
  data() {
    return {
      queryData: {
        // 查询数据
        typeCode: '', // 类型编码
        contentKey: '', // 	键
        pageNum: 1,
        pageSize: 10
      },
      btnType: 1, // 1 代表新增 0 代表编辑
      tableData: [], // 表格数据
      totalNum: 1,
      loading: false,
      row: {}, // 表格整行的数据
      codeValueContentIds: [] // 需要删除的码值内容ID
    }
  },
  created() {
    if (this.$route.params.hasOwnProperty('typeCode')) {
      // 码值类型页面跳转而来
      this.queryData.typeCode = this.$route.params.typeCode
    }
    this.queryCodeValueContentList()
  },
  methods: {
    // 查询码值类型列表
    queryCodeValueContentList() {
      this.loading = true
      queryCodeValueContent(this.queryData).then((res) => {
        if (res.status !== 0) {
          this.$message({
            type: 'error',
            message: res.msg
          })
          this.loading = false
          return
        }
        this.tableData = res.data.list
        this.totalNum = res.data.total
        this.loading = false
      })
    },
    // 新增
    handlerAddClick() {
      this.btnType = 1
      this.$refs.addOrEditCodeValContent.dialogFormVisible = true
    },
    // 重置
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.queryData.typeCode = ''
      this.queryCodeValueContentList()
    },
    // 搜索码值类型列表
    searchCodeValueContent() {
      this.queryCodeValueContentList()
    },
    // 改变页面显示条数
    handleSizeChange(val) {
      this.queryData.pageSize = val
      this.queryCodeValueContentList()
    },
    // 改变页数
    handleCurrentChange(val) {
      this.queryData.pageNum = val
      this.queryCodeValueContentList()
    },
    // 选择项发生变化时
    handleSelectionChange(val) {
      // console.log(val)
      this.codeValueContentIds = val.map((item) => {
        return item.id
      })
    },
    // 删除单条码值类型
    deleteCodeValueContent(index, row) {
      this.$confirm('此操作将删除码值类型, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteCodeValueContent({ ids: row.id }).then((res) => {
            if (res.status !== 0) {
              this.$message({
                type: 'error',
                message: res.msg
              })
              return
            }
            this.$message({
              type: 'success',
              message: '删除成功'
            })
            this.queryCodeValueContentList()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 批量删除码值类型
    batchDeleteCodeValueContent() {
      if (this.codeValueContentIds.length > 0) {
        this.$confirm('此操作将删除码值类型, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            deleteCodeValueContent({
              ids: this.codeValueContentIds.join()
            }).then((res) => {
              if (res.status !== 0) {
                this.$message({
                  type: 'error',
                  message: res.msg
                })
                return
              }
              this.$message({
                type: 'success',
                message: '删除成功'
              })
              this.queryCodeValueContentList()
            })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            })
          })
      } else {
        this.$message({
          type: 'info',
          message: '请勾选需要删除的码值类型'
        })
      }
    },
    // 打开编辑码值类型diolog
    handleShowDialog(index, row) {
      this.btnType = 0
      this.row = { ...row }
      this.$refs.addOrEditCodeValContent.dialogFormVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.code-value-content {
  .header-search {
    display: flex;
    justify-content: space-between;
    margin: 10px 0;
  }
  .value-content-main {
    .value-content-pag {
      margin-top: 10px;
    }
  }
}
</style>
