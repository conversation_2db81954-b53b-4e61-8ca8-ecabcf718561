import request from '@/utils/request'
import qs from 'qs'
//查询实证材料目录
export function queryempiricalmaterial(data) {
  return request({
    url: '/emm/empiricalmaterial/query',
    method: 'post',
    data
  })
}

//新增实证材料目录
export function addempiricalmaterial(data) {
  return request({
    url: '/emm/empiricalmaterial/add',
    method: 'post',
    data
  })
}

// 删除实证材料目录
export function deleteempiricalmaterial(params) {
  return request({
    url: '/emm/empiricalmaterial/delete',
    method: 'delete',
    params
  })
}

// 批量更新实证材料目录(根据ID只是更新，没有新增)
export function batchUpdateempiricalmaterial(data) {
  return request({
    url: '/emm/empiricalmaterial/batchUpdate',
    method: 'post',
    data
  })
}

// 新增实证材料内容
export function saveevaluationcontent(data) {
  return request({
    url: '/emm/empiricalmaterial/evaluationcontent/save',
    method: 'post',
    data
  })
}

// 查询实证材料内容
export function queryevaluationcontent(data) {
  return request({
    url: '/emm/empiricalmaterial/evaluationcontent/query',
    method: 'post',
    data
  })
}


// 查询用户及任务情况
export function getUserListAndTasks(params) {
  return request({
    url: '/emm/empiricalmaterial/taskallocation/getUserListAndTasks',
    method: 'get',
    params
  })
}


// 查询右侧树形,可以指定文档等级
export function queryDirectoryTree(params) {
  return request({
    url: '/emm/empiricalmaterial/taskallocation/queryDirectoryTree',
    method: 'get',
    params
  })
}

// 根据用户账号查询其负责的材料任务+为未配的（材料任务分配及进度页面使用）
export function getTasktree(params) {
  return request({
    url: '/emm/empiricalmaterial/taskallocation/getTask',
    method: 'get',
    params
  })
}


// 保存材料任务配置(勾选多少传多少，不传父目录，只传最后一级目录)
export function savetaskallocation(data) {
  return request({
    url: '/emm/empiricalmaterial/taskallocation/save',
    method: 'post',
    data
  })
}

// 查询实证材料证明
export function queryflowpath(data) {
  return request({
    url: '/emm/empiricalmaterial/flowpath/query',
    method: 'post',
    data
  })
}

// 新增实证材料证明
export function addflowpath(data) {
  return request({
    url: '/emm/empiricalmaterial/flowpath/add',
    method: 'post',
    data
  })
}

// 更新实证材料证明
export function updateflowpath(data) {
  return request({
    url: '/emm/empiricalmaterial/flowpath/update',
    method: 'post',
    data
  })
}

// 保存实证材料证明
export function saveflowpath(data) {
  return request({
    url: '/emm/empiricalmaterial/flowpath/save',
    method: 'post',
    data
  })
}

// 标记完成实证材料证明
export function markComplete(data) {
  return request({
    url: '/emm/empiricalmaterial/flowpath/markComplete',
    method: 'post',
    data
  })
}


// 删除实证材料证明
export function deleteflowpath(data) {
  return request({
    url: '/emm/empiricalmaterial/flowpath/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 获取图片
export function getImages(params) {
  return request({
    url: '/emm/empiricalmaterial/flowpath/getImages',
    method: 'get',
    responseType: 'blob',
    params
  })
}

// 查询实证材料证明图片
export function queryAllflowpath(data) {
  return request({
    url: '/emm/empiricalmaterial/flowpath/queryAll',
    method: 'post',
    data
  })
}

// 上传实证材料（支持同时上传多个文件）
export function uploadMultipleFiles(data) {
  return request({
    url: '/emm/empiricalmaterial/flowpath/uploadMultipleFiles',
    method: 'post',
    data
  })
}

// 预览文档
export function previewWord(data) {
  return request({
    url: '/emm/empiricalmaterial/export/previewWord',
    method: 'post',
    data
  })
}
// *********************************************************下载文档，并获取进度*******************************************

// 导出文档（异步执行）
export function exportAsyncmgt(data) {
  return request({
    url: '/emm/empiricalmaterial/export/exportAsync',
    method: 'post',
    data
  })
}


// 异步导出文档进度（-1表示失败，1表示完成）,建议5秒调一次
export function exportProcessmgt(params) {
  return request({
    url: '/emm/empiricalmaterial/export/exportProcess',
    method: 'get',
    params
  })
}


// 下载异步导出的文档
export function downloadDocmgt(params) {
  return request({
    url: '/emm/empiricalmaterial/export/downloadDoc',
    method: 'get',
    responseType: 'blob',
    params
  })
}


// 查询实证材料导出记录
export function queryempiricalmaterialrecord(data) {
  return request({
    url: '/emm/empiricalmaterial/record/query',
    method: 'post',
    data
  })
}

// 删除文档
export function deleteempiricalmaterialRecord(params) {
  return request({
    url: '/emm/empiricalmaterial/record/delete/' + params,
    method: 'DELETE',
  })
}
