<template>
  <el-dialog
    v-dialogDrag
    :visible.sync="dialogFormVisible"
    @closed="handlerClose"
    :close-on-click-modal="false"
    width="36vw"
    :show-close="false"
  >
    <div class="dialog-new-title">
      <h2>{{ formData.directoryName }}</h2>
      <i class="el-icon-close" @click="handlerClose()"></i>
    </div>
    <div class="dialog-label-row">
      <span class="label-item">
        <img src="../../../../assets/emrimg/icon_14.png" alt="" />
        {{ label1 }}</span
      >
      <span class="label-item" v-if="this.formData.evaluationCategory === '0'&type === 3">
        基本项</span
      ><span class="label-item" v-if="this.formData.evaluationCategory === '1'&type === 3">
        选择项</span
      >
    </div>
    <el-form :model="formData" label-width="110px" ref="ruleForm">
      <el-form-item label="业务项目：" v-show="type === 2">
        <el-input
          v-model="formData.businessProject"
          type="textarea"
          placeholder="请输入业务项目……"
          autosize
        ></el-input>
      </el-form-item>
      <el-form-item label="项目序号：" v-show="type === 2">
        <el-input
          v-model="formData.projectNum"
          placeholder="请输入项目序号……"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="主要评价内容："
        prop="levelCode"
        class="evaluationcontent"
        v-show="type === 3"
      >
        <div
          v-for="(item, index) in evaluationContents"
          :key="index"
          class="evaluationcontentiniput"
        >
          ({{ index + 1 }})
          <el-input
            class="input-with-select"
            placeholder="请输入评价细则……"
            type="textarea"
            autosize
            v-model="formData.evaluationContents[index].evaluationContent"
            @change="
              changeevaluationContents(
                formData.evaluationContents[index].evaluationContent,
                index
              )
            "
          >
            <el-button
              slot="append"
              icon="el-icon-delete"
              @click="clearrow(formData.evaluationContents[index])"
            ></el-button
          ></el-input>
        </div>
        <el-tooltip
          class="item"
          effect="dark"
          content="点击 添加细则"
          placement="top-end"
        >
          <el-button
            class="addicon"
            style="padding: 6px"
            @click="addevaluationcontent"
          >
            <svg-icon icon-class="icon_add1" style="font-size: 16px"
          /></el-button>
        </el-tooltip>
        <el-tooltip
          class="item"
          effect="dark"
          content="点击 删除细则"
          placement="top-end"
        >
          <el-button
            class="addicon"
            style="padding: 6px"
            @click="deleteevaluationcontent"
          >
          <i class="el-icon-delete" style="font-size: 16px"></i>
           </el-button>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="备注：" prop="levelCode" v-show="type === 3">
        <el-input
          v-model="formData.remarksDesc"
          type="textarea"
          autosize
          placeholder="请输入备注……"
        ></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogFormVisible = false">取 消</el-button>
      <el-button type="primary" @click="submitForm()">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  saveevaluationcontent,
  batchUpdateempiricalmaterial,
} from "@/api/empirical-material-mgt";
export default {
  data() {
    return {
      dialogFormVisible: false, // 弹框状态
      // 表单数据
      formData: {
        // 表单数据
        directoryName: "",
        directoryCode: "",
        evaluationCategory: "0",
        businessProject: "",
        projectNum:'',
        evaluationContents: [],
        remarksDesc: "",
      },
      evaluationContents: [],
      label1: "",
      type: "",
      item1: "",
      item2: "",
      index1: "",
      index2: "",
    };
  },
  methods: {
    // 处理dialog打开时
    async handlerOpen(type, item1, index1, item2, index2, item3, index3) {
      this.dialogFormVisible = true;
      this.type = type;
      if (type === 3) {
        this.index1 = index1;
        this.index2 = index2;
        this.item1 = item1;
        this.item2 = item2;
        this.label1 = item1.directoryName;
        this.formData = JSON.parse(JSON.stringify(item3));
        this.evaluationContents =
          item3.evaluationContents.length > 0
            ? JSON.parse(JSON.stringify(item3.evaluationContents))
            : [{ evaluationContent: "" }];
        this.formData.evaluationContents =
          item3.evaluationContents.length > 0
            ? JSON.parse(JSON.stringify(item3.evaluationContents))
            : [{ evaluationContent: "" }];
      } else if (type === 2) {
        this.index1 = index1;
        this.item1 = item1;
        this.label1 = item1.directoryName;
        this.formData = JSON.parse(JSON.stringify(item2));
      }
    },

    // 添加细则
    addevaluationcontent() {
      this.evaluationContents.push({ evaluationContent: "" });
      this.formData.evaluationContents.push({ evaluationContent: "" });
    },
    deleteevaluationcontent() {
      this.evaluationContents.pop();
      this.formData.evaluationContents.pop();
    },
    // 处理dialog关闭后
    handlerClose() {
      this.dialogFormVisible = false;
      // 表单数据
      this.formData = {
        // 表单数据
        directoryName: "",
        directoryCode: "",
        evaluationCategory: "0",
        businessProject: "",
        projectNum:'',
        evaluationContents: [],
        remarksDesc: "",
      };
      this.evaluationContents = [];
      this.label1 = "";
    },

    changeevaluationContents(item, index) {
      this.evaluationContents[index].evaluationContent = item;
    },
    // 删除单条评价内容
    clearrow(data) {
      this.evaluationContents.splice(this.evaluationContents.indexOf(data), 1);
      this.formData.evaluationContents.splice(
        this.formData.evaluationContents.indexOf(data),
        1
      );
    },
    // 保存
    submitForm() {
      console.log(this.type);
      if (this.type === 3) {
        saveevaluationcontent(this.formData).then((res) => {
          if (res.status === 0) {
            this.$message({
              message: "修改成功",
              type: "success",
            });
            this.dialogFormVisible = false;
            this.$parent.$parent.loadnextlevel(
              2,
              this.item1,
              this.index1,
              this.item2,
              this.index2
            );
          } else {
            this.$message({
              message: res.msg,
              type: "error",
            });
          }
        });
      } else if (this.type === 2) {
        batchUpdateempiricalmaterial([this.formData]).then((res) => {
          if (res.status === 0) {
            this.$message({
              message: "保存成功!",
              type: "success",
            });
            this.dialogFormVisible = false;
            this.$parent.$parent.loadnextlevel(1, this.item1, this.index1);
          } else {
            this.$message({
              message: res.msg,
              type: "error",
            });
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/emr-styles/emr-dialog.scss";
::v-deep .el-dialog__body {
  // display: flex;
  height: 100%;
  padding: 0px;
  padding: 20px 30px 20px 40px;
  .dialog-new-title {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
  .dialog-label-row {
    margin-top: 10px;
    margin-bottom: 20px;
    .label-item {
      background: #dee4fa;
      border-radius: 4px;
      text-align: center;
      padding: 4px 6px;
      margin-right: 10px;
      color: #333333;
      img {
        vertical-align: middle;
      }
    }
  }
  .evaluationcontent {
    min-height: 100px;
    position: relative;
    .addicon {
      position: relative;
      bottom: 0px;
      left: -45px;
    }
    .evaluationcontentiniput {
      display: flex;
    }
  }
  // 输入框样式修改
  .el-textarea__inner {
    border: 1px solid transparent;
  }

  .el-textarea__inner:hover {
    border-radius: 9px;
    border: 1px solid #dcdfe6;
  }
  .el-input__inner {
    border-radius: 9px;
    border: 1px solid transparent;
  }
  .el-input__inner:hover {
    border-radius: 9px;
    border: 1px solid #dcdfe6;
  }
  .input-with-select {
    border: 1px solid transparent;
    .el-input-group__append {
      background: transparent;
      border: 1px solid transparent;
    }
  }
  .input-with-select:hover {
    border: 1px solid #dcdfe6;
    border-radius: 9px;
    .el-input__inner {
      border: 1px solid transparent;
    }
  }
}
</style>
