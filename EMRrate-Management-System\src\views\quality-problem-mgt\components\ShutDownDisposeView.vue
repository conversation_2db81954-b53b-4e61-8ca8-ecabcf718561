<template>
  <div class="operation-container">
    <el-dialog
      v-dialogDrag
      :title="curBtnType"
      :visible.sync="dialogFormVisible"
      @open="handlerOpen"
      width="64%"
      :close-on-click-modal="false"
    >
      <div class="operation-main">
        <div class="tips">
          <div>
            <b>问题名称：</b>
            <span>{{ row.dataQltyQstnNm }}</span>
          </div>
          <div>
            <b>问题产生系统：</b>
            <span>{{ row.qstnPrdusSysNm }}</span>
          </div>
          <div>
            <b>规则名称：</b>
            <span>{{ row.checkRuleName }}</span>
          </div>
          <div>
            <b>规则大类：</b>
            <span
              v-for="item in checkRuleFatherTypeData"
              :key="item.contentKey"
            >
              <span v-if="row.checkRuleFatherType === item.contentKey">
                {{ item.contentValue }}
              </span>
            </span>
          </div>
          <div>
            <b>规则小类：</b>
            <span v-for="item in checkRuleTypeData" :key="item.contentKey">
              <span v-if="row.checkRuleType === item.contentKey">
                {{ item.contentValue }}
              </span>
            </span>
          </div>
          <div>
            <b>表名：</b>
            <span>{{ row.checkRuleTableOrView }}</span>
          </div>
          <div>
            <b>字段名：</b>
            <span>{{ row.checkRuleColumn }}</span>
          </div>
          <div>
            <b>检测时间：</b>
            <span>{{ row.qstnCheckTime }}</span>
          </div>
          <div>
            <b>问题数：</b>
            <span>{{ row.dataQstnNum }}</span>
          </div>
          <div>
            <b>检测数：</b>
            <span>{{ row.dataTotalNum }}</span>
          </div>
          <div>
            <b>错误率：</b>
            <span>{{
              ((row.dataQstnNum / row.dataTotalNum) * 100).toFixed(2) + "%"
            }}</span>
          </div>
        </div>
        <div class="question-nm-sql">
          <b>检测SQL或其他附加信息：</b>
          <span>{{ row.pbSubsidiarySql }}</span>
        </div>
        <div class="test-sample">
          <b>检测样例数据：</b>

          <div class="table">
            <div class="top">
              <span>{{ `全部 (${row.dataTotalNum}条)` }}</span>
              <span>{{ `问题数据 (${row.dataQstnNum}条)` }}</span>
              <span>{{
                `非问题数据 (${row.dataTotalNum - row.dataQstnNum}条)`
              }}</span>
            </div>
            <el-table
              :data="tableData"
              border
              v-loading="loading"
              height="200"
              :header-cell-style="{ background: '#F5F7FA', color: '#606266' }"
            >
              <el-table-column
                v-if="tableData.length > 0"
                label="序号"
                type="index"
                width="100"
              ></el-table-column>
              <el-table-column
                v-for="(item1, key1) in tableData.length > 0
                  ? tableData[0]
                  : []"
                :key="key1"
                :prop="key1"
                :label="key1"
              >
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
      <!-- 表单部分 -->
      <FormPart
        :isDisabled="isDisabled"
        :btnType="btnType"
        :editData="editData"
      />
      <template v-if="btnType !== '5'">
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="handlerClick">{{
            curBtnType
          }}</el-button>
          <el-button @click="dialogFormVisible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  queryDetailQstnData,
  editQstnInfo,
} from "@/api/qualityProblemMgt/qualityProblemMgt"
import FormPart from "./FormPart.vue"
export default {
  components: {
    FormPart,
  },
  data() {
    return {
      dialogFormVisible: false,
      isDisabled: false,
      editData: {},
      // formData: {
      //   rctfctnOpinions: "", // 整改意见
      //   pushMessageReceivers: [{
      //     account: "",
      //     accountType: ""
      //   }], // 推送消息接收方
      // },
      tableData: [], // 表格数据
      totalNum: 1,
      loading: false,
    }
  },
  computed: {
    curBtnType() {
      switch (this.btnType) {
        case "1":
          this.isDisabled = false
          return "关闭"
        case "2":
          this.isDisabled = false
          return "处理"
        case "5":
          this.isDisabled = true
          return "查看"
      }
    },
  },
  props: {
    row: {
      type: Object,
    },
    checkRuleFatherTypeData: {
      type: Array,
    },
    checkRuleTypeData: {
      type: Array,
    },
    btnType: {
      type: String,
    },
  },
  methods: {
    handlerClick() {
      if (this.btnType === "1") {
        this.$confirm("此操作将关闭问题, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.editQstnInfo()
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消关闭操作",
            })
          })
      } else {
        this.editQstnInfo()
      }
    },
    // 编辑
    editQstnInfo() {
      editQstnInfo(this.editData).then((res) => {
        if (res.status !== 0) {
          this.$message({
            message: res.msg,
            type: "error",
          })
        }
        this.$message({
          message: res.msg,
          type: "success",
        })
        this.dialogFormVisible = false
        this.$emit("queryList")
      })
    },
    // 当diolog打开时
    handlerOpen() {
      this.getDetailQstnData()
      this.editData = JSON.parse(JSON.stringify(this.row))
      this.editData.operationType = this.btnType
      if (
        this.editData.pushMessageReceivers == null ||
        (Array.isArray(this.editData.pushMessageReceivers) &&
          this.editData.pushMessageReceivers.length == 0)
      ) {
        this.editData.pushMessageReceivers = []
        this.editData.pushMessageReceivers.push({
          account: "",
          accountType: "",
        })
      }
    },
    // 查询检测样例数据
    getDetailQstnData() {
      this.loading = true
      queryDetailQstnData({
        dataQltyQstnId: this.row.pkId,
      }).then((res) => {
        if (res.status === 0) {
          this.tableData = res.data.map((item) => {
            return item.detailLineJson
          })
          this.loading = false
        } else {
          this.loading = false
          this.$message({
            message: res.msg,
            type: "error",
          })
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.operation-container {
  .operation-main {
    // margin-left: 10px;
    padding-left: 20px;

    .operation-header {
      .button {
        margin-left: 20px;
      }
    }

    margin: 10px;

    .operation-table {
      margin-top: 20px;
    }

    .tips {
      display: flex;
      flex-wrap: wrap;

      > div {
        display: flex;
        width: 200px;
        margin-right: 30px;
        margin-bottom: 30px;
        color: rgba($color: #000000, $alpha: 0.6);
        font-size: 14px;

        > b {
          white-space: nowrap;
        }

        > span {
          line-height: 20px;
        }
      }
    }

    .question-nm-sql {
      display: flex;
      margin-bottom: 30px;

      > b {
        white-space: nowrap;
      }
    }

    .test-sample {
      display: flex;
      margin-bottom: 30px;

      > b {
        white-space: nowrap;
      }

      .table {
        width: 1000px !important;

        .top {
          margin-bottom: 10px;

          span {
            margin-right: 30px;
          }
        }
      }
    }
  }
}

::v-deep .el-dialog .el-dialog__body {
  height: 70vh;
  overflow: auto;
}

::v-deep .el-dialog {
  top: -60px;
}
</style>
