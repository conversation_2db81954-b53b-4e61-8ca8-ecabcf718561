// 电子病历模块
.emr-container {
  // background: #F2F3F4;
      // font-family: Noto Sans SC;
  min-height: calc(100vh - 170px);
  display: flex;
  flex-direction: column;
  overflow-y: scroll;

  //   头部解释说明文字       【字典】
  .emr-container-explain {
    margin: 20px 0px;
    font-weight: 400;
    font-size: 18px;
    color: #5270DD;
    line-height: 13px;
  }


  .emr-headline {
    font-weight: bold;
    font-size: 16px;
    color: #333333;

    i {
      border-left: 5px solid #5270dd;
      margin-right: 10px;
    }

    span {
      font-weight: 400;
      font-size: 14px;
      color: #333333;
    }
  }

  // 主要表格，内容           【字典】
  .emr-container-main {
    flex: 1;
    background: #fff;
    border-radius: 12px;
    overflow-x: scroll;
    // margin-bottom: 80px;

    // 新增按钮，筛选按钮     【字典】
    .emr-container-button {
      padding: 20px 20px;

      // // 改变按钮样式
      // .el-button--primary {
      //   border-radius: 9px;
      //   background: #5270DD;
      //   border: 0px;
      // }
    }


    // 页面切换
    .emr-container-pag {
      padding-right: 20px;

    }
  }
}

.emr-container-table {
  padding: 0px 20px;
  font-size: 14px;

  // 展示信息样式 【字典】
  .showstate {
    svg {
      color: #5783E6;
      font-size: 16px;
    }

    b {
      margin-left: 6px;
      letter-spacing: 0.2em;
    }
  }

  .el-divider {
    margin: 0px 10px;
  }

  // 修改UI样式
  .has-gutter tr th {
    background: #fff;
  }

  .cell {
    text-align: left;

    .el-button {
      border-radius: 6px;
      padding: 4px 6px;
    }

    .noidinput {
      .el-input__inner {
        border: 1px solid #6581E9;
      }
    }

    .shezhi {
      color: #E75463;
      cursor: pointer;
    }
  }
}

//  自定义表格
.emr-container-table-self-defined {
  overflow-x: scroll;

  .emr-container-table-self-defined-row {
    display: flex;
    // height: 50px;
    line-height: 30px;
    font-size: 14px;

    >div {
      text-align: left;
      border-bottom: 1px solid #DBDDE1;
      padding: 10px 8px;

      .noidinput {
        .el-input__inner {
          border: 1px solid #6581E9;
        }
      }

      .el-input__inner {
        border-radius: 9px !important;
      }
    }

    .son1 {
      width: 60px;
    }

    .son2 {
      width: 15%;
    }

    .son3 {
      width: 15%;
    }

    .son4 {
      width: 8%;
    }

    .son5 {
      width: 10%;
    }

    .son6 {
      width: 10%;

      .emr-tag {
        border-radius: 4px;
        text-align: center;
        padding: 4px 10px;
      }

      .emr-tag-jiben {
        background: #E0ECFB;

      }

      .emr-tag-xuanze {
        background: #F8EEC9;
      }
    }

    .son7 {
      width: 18%;

      .shezhi {
        color: #E75463;
        cursor: pointer;

      }
    }


    .son9 {
      width: 25%;


      .el-divider {
        margin: 0px 10px;
      }
    }
  }

  .emr-container-table-title {
    text-align: left;
    font-weight: bold;
    font-size: 14px;
    color: #333333;

    >div {
      text-align: left;
    }
  }
}

// 改变按钮样式
// .el-button {
//   border-radius: 9px;
// }

// .el-button--primary:hover {
//   border: 1px solid transparent;
//   background: #5270dd;
//   color: #fff;
// }

// .el-button--text:hover {
//   border: 1px solid transparent;
//   background: transparent;
//   color: #5270dd;
// }

// .el-button--default:hover {
//   border: 1px solid #dcdfe6;
//   background: #fff;
//   color: #606266;
// }

// .el-button:focus,
// .el-button:hover {
//   color: #fff;

// }

.el-menu--horizontal>.el-menu-item {
  background: #f8f8f9;
  border-radius: 10px 10px 0px 0px;
  margin-right: 20px;
  height: 40px;
  font-size: 16px;
  line-height: 40px;
}

.el-menu--horizontal>.el-menu-item.is-active {
  font-weight: bold;
  font-size: 16px;
  color: #5270dd;
  background: #eee;
  border-radius: 10px 10px 0px 0px;
  border: 0px solid transparent;

  div {
    position: relative;

    i {
      position: absolute;
      bottom: 0px;
      left: 42%;
      padding: 2px 8px;
      background: #5270dd;
      border-radius: 3px;
    }

    img {
      position: absolute;
      bottom: 0px;
      right: -28px;
    }
  }
}
