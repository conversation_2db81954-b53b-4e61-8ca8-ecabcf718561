<template>
  <div>
    <div class="navbar-container">
      <div class="navbar-content">
        <div class="second-level-nav">
          <div class="title">
            <img class="title-img" :src="firstLevelImg" alt="" />
            <span>{{ firstLevelTitle }}</span>
          </div>
          <div class="nav-list">
            <div
              class="nav-item"
              :style="{
                fontWeight: curActiveSecondLevel === item.path ? '700' : '500',
              }"
              v-for="item in curMenuItemChild"
              :key="item.path"
              @click="handlerSecondLevelSkip(item)"
            >
              {{ item.meta.title }}
              <div v-if="curActiveSecondLevel === item.path">
                <img class="active-img1" :src="navbar_active_01" alt="" />
                <img class="active-img2" :src="navbar_active_02" alt="" />
              </div>
            </div>
          </div>
        </div>

        <div class="tips-block">
          <el-popover
            placement="right"
            width="400"
            trigger="hover"
            v-show="
              $store.state.user.isDownload || $store.state.user.isDownloadmgt
            "
          >
            <div class="download-tex">
              {{
                isDownload ? "正在下载电子病历评级文档" : "正在下载证明材料文档"
              }}
              {{ exportRecordId }}
            </div>
            <el-progress :percentage="percentage"></el-progress>
            <el-progress
              type="circle"
              slot="reference"
              :stroke-width="4"
              :percentage="percentage"
              :width="40"
            ></el-progress>
          </el-popover>
          <div
            class="tips-block-download"
            v-show="$store.state.user.isDownload"
          ></div>
        </div>
      </div>
      <div class="userMgt">
        <UserMgt @startHandleCommand="(command) => this[command]()" />
      </div>
    </div>
  </div>
</template>

<script>
import Logo from "@/layout/components/Logo.vue";
import UpdatePassword from "@/components/UpdatePassword";
import UserMgt from "./UserMgt.vue";
import {
  exportProcess,
  downloadDoc,
} from "@/api/document-management/document-review";
import { exportProcessmgt, downloadDocmgt } from "@/api/empirical-material-mgt";
import moment from "moment";
import saveAs from "file-saver";
import navbar_active_01 from "@/assets/navbar/navbar_active_01.png";
import navbar_active_02 from "@/assets/navbar/navbar_active_02.png";
export default {
  name: "vvvv",
  components: { Logo, UpdatePassword, UserMgt },
  data() {
    return {
      timer: null,
      timermgt: null,
      navbar_active_01,
      navbar_active_02,
      dateTime: moment().format("YYYY-MM-DD HH:mm"),
      avaterUrl:
        "https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",
      tags: [],
      currentTag: "首页",
      title: "首页",

      timershow: "",
      percentage: 0,
      exportRecordId: "",
      curActiveSecondLevel: "",
    };
  },
  props: {
    curMenuItem: {
      type: Object,
      default: () => ({}),
    },
  },
  created() {
    this.exportProcessmgt();
    this.exportProcess();
    // this.$store.commit('user/SET_ISDOWNLOAD', true)
    // this.$store.commit('user/SET_ISDOWNLOADMGT', true)
    this.timershow = setInterval(() => {
      this.dateTime = moment().format("YYYY-MM-DD HH:mm");
    }, 1000);
    const { meta, path, matched } = this.$route;
    if (meta.title !== "首页") {
      this.tags.push({ ...meta, path, closable: true });
      this.currentTag = meta.title;
    }
    this.title = matched[0].meta.title;
  },
  destroyed() {
    this.timer && clearInterval(this.timer);
    this.timermgt && clearInterval(this.timermgt);
  },
  computed: {
    isDownload() {
      return this.$store.state.user.isDownload;
    },
    isDownloadmgt() {
      return this.$store.state.user.isDownloadmgt;
    },
    curMenuItemChild() {
      return this.curMenuItem.children;
    },
    firstLevelTitle() {
      return this.curMenuItem.meta?.title;
    },
    firstLevelImg() {
      return this.curMenuItem.meta?.menuIcons?.navbarTitleIcon;
    },
  },
  methods: {
    removeTab(tagName) {
      if (tagName == "首页") return;
      this.tags = this.tags.filter((item, i) => {
        return item.title != tagName;
      });
      this.$router.push("/");
    },
    handlelink(tag) {
      const {
        $vnode: {
          data: {
            attrs: { path },
          },
        },
      } = tag;
      // console.log("tag", tag);
      this.$router.push(path);
    },
    exportProcessmgt() {
      exportProcessmgt().then((res) => {
        if (res.status != 0) {
          this.$store.commit("user/SET_ISDOWNLOADMGT", false);
          clearInterval(this.timermgt);
          this.$message({
            type: "error",
            message: res.msg,
          });
        } else if (res.data.exportRecordId === null) {
          clearInterval(this.timermgt);
          this.$store.commit("user/SET_ISDOWNLOADMGT", false);
        } else {
          this.exportRecordId = res.data.exportRecordId;
          if (res.data.process === 1) {
            clearInterval(this.timermgt);
            this.$store.commit("user/SET_ISDOWNLOADMGT", false);
            downloadDocmgt({ exportRecordId: this.exportRecordId }).then(
              (res) => {
                const blob = new Blob([res]);
                saveAs(blob, `${decodeURIComponent(res.name)}`);
              }
            );
          } else if (res.data.process === -1) {
            clearInterval(this.timermgt);
            this.$store.commit("user/SET_ISDOWNLOADMGT", false);
            this.$message({
              type: "error",
              message: "导出失败",
            });
          } else {
            this.percentage = Number((res.data.process * 100).toFixed(0));
          }
        }
      });
    },

    exportProcess() {
      exportProcess().then((res) => {
        if (res.status != 0) {
          this.$store.commit("user/SET_ISDOWNLOAD", false);
          clearInterval(this.timer);
          this.$message({
            type: "error",
            message: res.msg,
          });
        } else if (res.data.exportRecordId === null) {
          clearInterval(this.timer);
          this.$store.commit("user/SET_ISDOWNLOAD", false);
        } else {
          this.exportRecordId = res.data.exportRecordId;
          if (res.data.process === 1) {
            clearInterval(this.timer);
            this.$store.commit("user/SET_ISDOWNLOAD", false);
            downloadDoc({ exportRecordId: this.exportRecordId }).then((res) => {
              const blob = new Blob([res]);
              saveAs(blob, `${decodeURIComponent(res.name)}`);
            });
          } else if (res.data.process === -1) {
            clearInterval(this.timer);
            this.$store.commit("user/SET_ISDOWNLOAD", false);
            this.$message({
              type: "error",
              message: "导出失败",
            });
          } else {
            this.percentage = Number((res.data.process * 100).toFixed(0));
          }
        }
      });
    },
    handlerSecondLevelSkip(item) {
      this.curActiveSecondLevel = item.path;
      const fullPath = `${this.curMenuItem.path}/${item.path}`;
      this.$router.push(fullPath);
    },
  },
  watch: {
    "$route.path": {
      handler(val) {
        if (!!val) {
          this.curActiveSecondLevel = val.split("/")[2];
        }
      },
      immediate: true,
    },
    isDownload: {
      handler(val) {
        if (val === true) {
          //监听到变化 去触发下一步操作
          let that = this;
          this.exportProcess();
          that.timer = setInterval(this.exportProcess, 5000);
        }
      },
    },
    isDownloadmgt: {
      handler(val) {
        if (val === true) {
          //监听到变化 去触发下一步操作
          let that = this;
          this.exportProcessmgt();
          that.timermgt = setInterval(this.exportProcessmgt, 5000);
        }
      },
    },
  },
};
</script>

<style lang="scss" scoped>

::v-deep .el-progress__text {
  font-size: 10px !important;
}
.navbar-container {
  height: 58px;
  background-color: #fff;
  position: relative;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.08);
  .navbar-content {
    position: absolute;
    transform: translateY(-50%);
    top: 50%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 0 26px;
    align-items: center;
    .second-level-nav {
      display: flex;
      align-items: center;
      white-space: nowrap;
      .title {
        font-size: 20px;
        font-weight: 600;
        color: #98a0b7;
        .title-img {
          vertical-align: middle;
          margin-top: -4px;
          width: 28px;
          margin-right: 8px;
        }
      }
      .nav-list {
        display: flex;
        margin-left: 64px;
        .nav-item {
          padding: 4px 18px;
          font-size: 16px;
          cursor: pointer;
          position: relative;
          .active-img1 {
            position: absolute;
            width: 98px;
            height: 4px;
            left: 50%;
            top: -16px;
            z-index: 1000;
            transform: translateX(-50%);
          }
          .active-img2 {
            position: absolute;
            left: 50%;
            top: 24px;
            z-index: 1000;
            transform: translateX(-50%);
          }
          &:hover {
            font-weight: 700 !important;
          }
        }
        .nav-item + .nav-item {
          border-left: 2px solid #e5e7e8;
        }
      }
    }
    .tips-block {
      margin-right: 200px;
      display: flex;
      align-items: center;
      .avatar {
        margin-right: 9px;
        cursor: pointer;
      }
      .icon {
        cursor: pointer;
      }
      .logout {
        padding: 0 10px;
        margin-left: 10px;
        border-left: 1px solid #c7cadd;
        cursor: pointer;
        font-size: 30px;
      }
    }
  }

  .tips-block-download {
    position: absolute;
    top: 30px;
    right: 10px;
    font-size: 14px;
    display: flex;
    // .el-progress {
    //   width: 200px;
    // }
    // .download-tex {
    //   width: 70px;
    // }
  }

  .titlePic {
    font-size: 32px;
    background-position: right -695px top -10px;
    height: 105px;
    margin-top: 20px;
    margin-left: -40px;
    font-weight: 700;
    color: #333;
    // width: 260px;
  }
  /* .title {
    left: 40px;
    bottom: 0px;
    position: absolute;
    font-size: 28px;
    color: #1b88c0;
    font-weight: 600;
    line-height: 50px;
    .bottom-line {
      border-top: 5px solid #6bcfc0;
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;
    }
  } */

  .tabs {
    width: 100%; //解决欣姐电脑上:hover 鬼畜问题
    position: absolute;
    bottom: 0px;
    left: 340px;
    & > :first-child {
      margin-bottom: 0px;
    }
  }
  .userMgt {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
  }
}
</style>
