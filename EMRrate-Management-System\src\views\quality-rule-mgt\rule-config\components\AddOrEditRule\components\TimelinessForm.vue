<template>
  <div>
    <!-- 及时性 -->
    <template v-if="formData.checkRuleType === 'JSX'">
      <el-form-item label="检测表或视图" prop="checkRuleTableOrView">
        <el-autocomplete
          ref="checkRuleTableOrView"
          v-model="formData.checkRuleTableOrView"
          :fetch-suggestions="querySearchAsync"
          value-key="tableOrViewName"
          @select="handlerTableOrViewSelect($event, 'checkRuleTableOrView')"
          placeholder="请输入内容"
          clearable
        ></el-autocomplete>
      </el-form-item>
      <el-form-item label="检测字段" prop="checkRuleColumn">
        <el-input
          :disabled="isDisabled"
          @click.native="handlerclick('checkRuleColumn')"
          v-model="formData.checkRuleColumn"
        >
          <el-button slot="append">
            <SelectFieldList
              type="checkRuleColumn"
              selectType="radio"
              @click.native.stop="handlerclick('checkRuleColumn')"
              :isDisabled="isDisabled"
              :checkRuleColumnData="checkRuleColumnData"
              @backfillSelectedData="backfillSelectedData"
            />
          </el-button>
        </el-input>
      </el-form-item>
      <el-form-item label="执行条件" prop="dtExecCondDescr">
        <el-radio-group v-model="formData.dtExecCondDescr">
          <el-radio label="0">T</el-radio>
          <el-radio label="1">T-1</el-radio>
          <el-radio label="2">T-2</el-radio>
          <el-radio label="3">T-3</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="明细表WHERE条件" prop="checkRuleWhere">
        <el-input type="textarea" v-model="formData.checkRuleWhere"></el-input>
      </el-form-item>
      <el-form-item label="问题明细显示字段" prop="pbSubsidiaryColumns">
        <el-input
          :disabled="isDisabled"
          @click.native="handlerclick"
          v-model="formData.pbSubsidiaryColumns"
        >
          <el-button slot="append">
            <SelectFieldList
              type="pbSubsidiaryColumns"
              selectType="checkBox"
              @click.native.stop="handlerclick"
              :isDisabled="isDisabled"
              :checkRuleColumnData="checkRuleColumnData"
              @backfillSelectedData="backfillSelectedData"
            />
          </el-button>
        </el-input>
      </el-form-item>
      <slot></slot>
    </template>

    <!-- 自定义及时性 -->
    <template v-if="formData.checkRuleType === 'ZDYJSX'">
      <CustomCheckRuleType :formData.sync="formData" />
    </template>
  </div>
</template>


<script>
import tableViewAndField from "@/mixins/tableViewAndField"
import CustomCheckRuleType from "@/components/CustomCheckRuleType/index.vue"
import SelectFieldList from "./common/SelectFieldList.vue"
export default {
  data() {
    return {
      isDisabled: true,
      dictTableData: [], // 字典中的表数据
      dictTableFiledData: [], // 字典表字段数据
    }
  },

  components: {
    CustomCheckRuleType,
    SelectFieldList,
  },
  mixins: [tableViewAndField],
  props: {
    formData: {
      type: Object,
    },
    dataSourceId: {
      type: Number,
    },
  },
  mounted() {
    if (this.formData.checkRuleTableOrView) {
      this.queryCheckRuleColumnData.tableName =
        this.formData.checkRuleTableOrView
      this.queryCheckRuleColumnData.dataSourceId = this.dataSourceId
      this.getCheckRuleColumnData()
    }
  },
  watch: {
    formData: {
      handler(val) {
        // 只要表单有值变化就清空获取的SQL数据
        this.$emit("clearCheckSQLData")
        if (val.checkRuleTableOrView) {
          this.isDisabled = false
        } else {
          this.clearPartData()
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    //handlerClear
    clearPartData() {
      this.isDisabled = true
      this.checkRuleColumnData = []
      this.formData.checkRuleColumn = ""
      this.formData.pbSubsidiaryColumns = ""
    },
    // 先选择表或视图
    handlerclick(type) {
      if (this.isDisabled && type === "checkRuleColumn") {
        this.$message({
          type: "warning",
          message: "请先选择主表或视图",
        })
        this.$refs["checkRuleTableOrView"].focus()
      } else if (this.isDisabled) {
        this.$message({
          type: "warning",
          message: "请先选择主表或视图",
        })
        this.$refs["checkRuleTableOrView"].focus()
      }
    },
    // 当主表或视图或副表或视图选择时
    handlerTableOrViewSelect(e, type) {
      // console.log(e, type)
      if (type === "checkRuleTableOrView") {
        this.isDisabled = false
        this.queryCheckRuleColumnData.tableName = e.tableOrViewName
        this.queryCheckRuleColumnData.dataSourceId = this.row
          ? this.row.dataSourceId
          : this.dataSourceId
        this.getCheckRuleColumnData()
      }
      this.isDisabled = false
    },
    backfillSelectedData(val, type) {
      if (type === "checkRuleColumn") {
        this.formData.checkRuleColumn = val.join(",")
      } else if (type === "pbSubsidiaryColumns") {
        this.formData.pbSubsidiaryColumns = val.join(",")
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.selected {
  margin-bottom: 20px;
}
.el-form-item .el-form-item {
  display: flex;
  flex-direction: column;
  text-align: center;
}
</style>
