<template>
  <MainCard>
    <div class="emr-container">
      <div class="emr-container-explain">
        电子病历系统整体应用水平分级评价等级划分
      </div>
      <div class="emr-container-main">
        <div class="emr-container-button">
          <el-button
            type="primary"
            size="mini"
            @click="
              $refs.AddOrEditDictionaryConfiguration.dialogFormVisible = true
            "
            >添加等级</el-button
          >
        </div>
        <el-table
          class="emr-container-table"
          :data="tableData"
          ref="sourceMgtTable"
          v-loading="loading"
          :header-cell-style="{ background: '#fff', color: '#606266' }"
        >
          <el-table-column
            prop="levelName"
            label="等级"
            min-width="100"
            sortable
          >
            <template slot-scope="scope">
              <div v-show="scope.row.editstate">
                <el-input size="mini" v-model="scope.row.levelName"></el-input>
              </div>
              <div class="showstate" v-show="!scope.row.editstate">
                <svg-icon icon-class="icon_9" />
                <b>{{ scope.row.levelName }}</b>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="levelCode" label="等级代码" min-width="100">
            <template slot-scope="scope">
              <div v-show="scope.row.editstate">
                <el-input size="mini" v-model="scope.row.levelCode"></el-input>
              </div>
              <div class="showstate" v-show="!scope.row.editstate">
                {{ scope.row.levelCode }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createBy" label="创建人" min-width="100">
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" min-width="150">
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="220">
            <template slot-scope="scope">
              <span v-show="!scope.row.editstate">
                <el-button
                  size="mini"
                  type="text"
                  @click="editstatechange(scope.$index, scope.row)"
                  >编辑</el-button
                >
                <el-divider direction="vertical"> </el-divider>
              </span>

              <el-popconfirm
                title="此操作将删除该等级, 是否继续?"
                @onConfirm="deletelevelDictionary(scope.$index, scope.row)"
              >
                <el-button
                  slot="reference"
                  type="text"
                  size="mini"
                  v-show="!scope.row.editstate"
                >
                  删除
                </el-button>
              </el-popconfirm>
              <span v-show="scope.row.editstate">
                <el-button
                  size="mini"
                  type="primary"
                  @click="updatelevelDictionary(scope.$index, scope.row)"
                >
                  保存
                </el-button>
                <el-divider direction="vertical"> </el-divider>
              </span>

              <el-button
                v-show="scope.row.editstate"
                size="mini"
                @click="editstatechange(scope.$index, scope.row)"
              >
                取消
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <AddOrEditDictionaryConfiguration
        ref="AddOrEditDictionaryConfiguration"
      />
    </div>
  </MainCard>
</template>

<script>
import {
  querylevelDictionary,
  deletelevelDictionary,
  updatelevelDictionary,
} from "@/api/document-management/dictionary-configuration";
import AddOrEditDictionaryConfiguration from "./components/AddOrEditDictionaryConfiguration.vue";
export default {
  data() {
    return {
      queryData: {
        // 查询数据
        startTime: "",
        endTime: "",
        pageNum: 1,
        pageSize: 100,
      },
      tableData: [], // 表格数据
      tableDatacopy: [], //请求回来的表格数据，保持不变，方便在取消时还原
      loading: false, //表格加载状态
    };
  },
  components: {
    AddOrEditDictionaryConfiguration,
  },
  created() {
    // 进入页面初始化查询
    this.getlevelDictionary();
  },
  methods: {
    // 查询等级字典配置列表
    getlevelDictionary() {
      this.loading = true;
      this.tableDatacopy = [];
      this.tableData = [];
      querylevelDictionary(this.queryData).then((res) => {
        if (res.status === 0) {
          this.tableDatacopy = JSON.parse(JSON.stringify(res.data.list));
          res.data.list.map((v) => {
            this.tableData.push(Object.assign(v, { editstate: false }));
          });
          this.loading = false;
        }
      });
    },
    // 改变编辑状态
    editstatechange(index, row) {
      if (row.editstate) {
        // 取消操作
        this.tableData[index].editstate = false;
        this.tableData[index].levelName = this.tableDatacopy[index].levelName;
        this.tableData[index].levelCode = this.tableDatacopy[index].levelCode;
      } else {
        //编辑操作
        this.tableData[index].editstate = true;
      }
    },
    // 更新等级字典
    updatelevelDictionary(index, row) {
      updatelevelDictionary({
        levelCode: row.levelCode,
        levelName: row.levelName,
        id: row.id,
      }).then((res) => {
        if (res.status !== 0) {
          this.$message({
            message: res.msg,
            type: "error",
          });
          return;
        } else {
          this.$message({
            message: "更新等级字典成功",
            type: "success",
          });
          this.tableData[index].editstate = false;
        }
      });
    },
    // 删除等级字典配置
    deletelevelDictionary(index, row) {
      deletelevelDictionary({ ids: row.id }).then((res) => {
        this.loading = true;
        if (res.status === 0) {
          this.$message({
            message: "删除成功!",
            type: "success",
          });
          this.tableData.splice(index, 1);
          this.loading = false;
        } else {
          this.$message({
            message: res.msg,
            type: "error",
          });
          this.loading = false;
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
@import "@/styles/emr-styles/emr-main-table.scss";
</style>
