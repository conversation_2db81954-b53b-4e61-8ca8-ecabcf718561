<template>
  <div
    class="component-log-container"
    :style="`width:${width}px;height:${height}px`"
  >
    <img v-if="src" class="img" :src="src" alt="商标" />
    <div v-else :style="`width:${width}px;height:${height}px`"></div>
  </div>
</template>

<script>
export default {
  name: "Logo",
  props: ["width", "height", "keyName"],
  data() {
    return {};
  },
  computed: {
    src() {
      if (this.$store.state.app[this.keyName]) {
        return  this.$store.state.app[this.keyName];
      } else {
        return "";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.component-log-container {
  display: flex;
  flex-direction: column;
  justify-content: center;

  .img {
    width: 100%;
    background-color: transparent;
  }
}
</style>
