import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [{
    path: '/login',
    name: 'login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/singleLogin',
    component: () => import('@/views/login/single.vue'),
    hidden: true
  },  
  {
    path: '/redirect',
    name:'redirect',
    component: () => import('@/views/login/redirect.vue'),
    hidden: true
  },
  {
    path: '/',
    redirect: '/home-page'
  },
  {
    path: '/home-page',
    name: 'home-page',
    component: Layout,
    meta: {
      title: '首页',
      menuIcons: {
        normal: require('@/assets/sidebar/home_nav_1.png'),
        active: require('@/assets/sidebar/home_nav_1_active.png'),
        // navbarTitleIcon: require('@/assets/sidebar/dict_nav_2_lg.png')
      }
    },
    children: [{
      path: '',
      component: () => import('@/views/HomePage/index.vue'),
      meta: {
        title: '首页'
      }
    }]
  },
  {
    path: '/404',
    component: () => import('@/views/404/index.vue'),
    hidden: true
  }
]

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [
  {
    path: '/datasource-structure-mgt',
    component: Layout,
    name: 'Datasource-structure-mgt',
    redirect: '/datasource-structure-mgt/data-source-mgt',
    meta: {
      title: '数源结构管理',
      icon: 'data',
      activeMenu: 'info',
      menuIcons: {
        normal: require('@/assets/sidebar/nav_8.png'),
        active: require('@/assets/sidebar/nav_8_active.png'),
        navbarTitleIcon: require('@/assets/sidebar/nav_8_lg.png')
      }
    },
    children: [{
        path: 'data-source-mgt',
        name: 'Data-source-mgt',
        component: () =>
          import('@/views/datasource-structure-mgt/data-source-mgt'),
        meta: {
          title: '数源配置'
        }
      },
      {
        path: 'collection-task',
        name: 'Collection-task',
        component: () =>
          import('@/views/datasource-structure-mgt/collection-task'),
        meta: {
          title: '采集任务'
        }
      },
      {
        path: 'collection-task-status',
        name: 'Collection-task-status',
        component: () =>
          import('@/views/datasource-structure-mgt/collection-task-status'),
        meta: {
          title: '任务日志'
        }
      },
      {
        path: 'data-structure-details',
        name: 'Data-structure-details',
        component: () =>
          import('@/views/datasource-structure-mgt/data-structure-details'),
        meta: {
          title: '数源结构'
        }
      }
    ]
  },
  {
    path: '/quality-rule-mgt',
    name: 'Quality-rule-mgt',
    component: Layout,
    redirect: '/quality-rule-mgt/rule-config',
    meta: {
      title: '质量规则管理',
      icon: 'quality',
      menuIcons: {
        normal: require('@/assets/sidebar/data_source_nav_1.png'),
        active: require('@/assets/sidebar/data_source_nav_1_active.png'),
        navbarTitleIcon: require('@/assets/sidebar/data_source_side.png')
      }
    },
    children: [{
        path: 'rule-config',
        component: () => import('@/views/quality-rule-mgt/rule-config'),
        meta: {
          title: '规则配置'
        }
      },
      {
        path: 'rule-execution',
        component: () => import('@/views/quality-rule-mgt/rule-execution'),
        meta: {
          title: '规则执行'
        }
      },
      {
        path: 'execution-record',
        component: () => import('@/views/quality-rule-mgt/execution-record'),
        meta: {
          title: '执行记录'
        }
      }
    ]
  },
  {
    path: '/quality-control-mgt',
    component: Layout,
    name: 'Quality-control-mgt',
    redirect: '/quality-control-mgt/check-task-mgt',
    meta: {
      title: '质量监控管理',
      icon: 'data',
      activeMenu: 'info',
      menuIcons: {
        normal: require('@/assets/sidebar/dict_nav_2.png'),
        active: require('@/assets/sidebar/dict_nav_2_active.png'),
        navbarTitleIcon: require('@/assets/sidebar/dict_nav_2_lg.png')
      }
    },
    children: [{
        path: 'check-task-mgt',
        name: 'Check-task-mgt',
        component: () =>
          import('@/views/datasource-structure-mgt/data-source-mgt'),
        meta: {
          title: '检核任务管理'
        }
      },
      {
        path: 'check-status-monitor',
        name: 'Check-status-monitor',
        component: () =>
          import('@/views/datasource-structure-mgt/collection-task'),
        meta: {
          title: '检核状态监控'
        }
      }
    ]
  },
  {
    path: '/quality-problem-mgt',
    component: Layout,
    name: 'Quality-problem-mgt',
    redirect: '/quality-problem-mgt/problem-mgt',
    alwaysShow: true,
    meta: {
      title: '质量问题管理',
      icon: 'problem',
      activeMenu: 'info',
      menuIcons: {
        normal: require('@/assets/sidebar/dict_nav_2.png'),
        active: require('@/assets/sidebar/dict_nav_2_active.png'),
        navbarTitleIcon: require('@/assets/sidebar/dict_nav_2_lg.png')
      }
    },
    children: [{
        path: 'problem-mgt',
        name: 'Problem-mgt',
        component: () => import('@/views/quality-problem-mgt'),
        meta: {
          title: '问题管理'
        }
      }
      // ,{
      //   path: 'check-result-mgt',
      //   name: 'Check-result-mgt',
      //   component: () => import('@/views/datasource-structure-mgt/data-source-mgt'),
      //   meta: {
      //     title: '检核结果管理',
      //   }
      // }
    ]
  },
  // *******************************************电子病历评级文档管理*************************************

  {
    path: '/dictionary-configuration',
    component: Layout,
    redirect: 'dictionary-configuration/rank-dictionary',
    meta: {
      title: '字典配置',
      icon: 'el-icon-document-add',
      menuIcons: {
        normal: require('@/assets/sidebar/dict_nav_2.png'),
        active: require('@/assets/sidebar/dict_nav_2_active.png'),
        navbarTitleIcon: require('@/assets/sidebar/dict_nav_2_lg.png')
      }
    },
    children: [{
        path: 'rank-dictionary',
        name: 'Rank-dictionary',
        component: () =>
          import('@/views/dictionary-configuration/rank-dictionary'),
        meta: {
          title: '评价等级配置'
        }
      },
      {
        path: 'catalogue-configuration',
        name: 'Catalogue-configuration',
        component: () =>
          import('@/views/dictionary-configuration/catalogue-configuration'),
        meta: {
          title: '评价文档目录配置'
        }
      },
      {
        path: 'catalogue-empirical-materials',
        name: 'Catalogue-empirical-materials',
        component: () =>
          import(
            '@/views/dictionary-configuration/catalogue-empirical-materials'
          ),
        meta: {
          title: '实证材料目录配置'
        }
      },
      {
        path: 'name-character',
        name: 'Name-character',
        component: () =>
          import('@/views/dictionary-configuration/name-character'),
        meta: {
          title: '要求项目名称字典'
        }
      }
    ]
  },
  {
    path: '/document-management',
    name: 'Document-management',
    component: Layout,
    redirect: '/document-management/project-management',
    meta: {
      title: '数据质量文档',
      icon: 'case',
      menuIcons: {
        normal: require('@/assets/sidebar/nav_4.png'),
        active: require('@/assets/sidebar/nav_4_active.png'),
        navbarTitleIcon: require('@/assets/sidebar/nav_4_lg.png')
      }
    },
    children: [{
        path: 'project-management',
        name: 'project-management',
        component: () =>
          import('@/views/document-management/project-management'),
        meta: {
          title: '项目管理'
        }
      }, {
        path: 'rule-permission-configuration',
        name: 'rule-permission-configuration',
        component: () =>
          import('@/views/document-management/rule-permission-configuration'),
        meta: {
          title: '任务分配及进度'
        }
      },
      {
        path: 'rule-configuration',
        name: 'rule-configuration',
        component: () =>
          import('@/views/document-management/rule-configuration'),
        meta: {
          title: '质量规则配置'
        }
      },
      {
        path: 'document-review',
        name: 'document-review',
        component: () => import('@/views/document-management/document-review'),
        meta: {
          title: '文档预览导出'
        }
      },
    ]
  },
  {
    path: '/empirical-material-mgt',
    component: Layout,
    redirect: '/empirical-material-mgt/project-management',
    meta: {
      title: '实证材料管理',
      icon: 'el-icon-document-add',
      menuIcons: {
        normal: require('@/assets/sidebar/nav_5.png'),
        active: require('@/assets/sidebar/nav_5_active.png'),
        navbarTitleIcon: require('@/assets/sidebar/nav_5_lg.png')
      }
    },
    children: [
      {
        path: 'project-management',
        name: 'project-management',
        component: () =>
          import('@/views/document-management/project-management'),
        meta: {
          title: '项目管理'
        }
      },  {
        path: 'task-assignment-schedule',
        name: 'task-assignment-schedule',
        component: () =>
          import('@/views/empirical-material-mgt/task-assignment-schedule'),
        meta: {
          title: '材料任务分配及进度'
        }
      },
      {
        path: 'material-proof-upload',
        name: 'Material-proof-upload',
        component: () =>
          import('@/views/empirical-material-mgt/material-proof-upload'),
        meta: {
          title: '材料证明上传'
        }
      },
      {
        path: 'proof-preview-export',
        name: 'Proof-preview-export',
        component: () =>
          import('@/views/empirical-material-mgt/proof-preview-export'),
        meta: {
          title: '证明预览导出'
        }
      }
    ]
  },
  {
    path: '/rating-info-management',
    component: Layout,
    redirect: '/rating-info-management/rating-info-config',
    meta: {
      title: '评级信息管理',
      menuIcons: {
        normal: require('@/assets/sidebar/nav_9.png'),
        active: require('@/assets/sidebar/nav_9_active.png'),
        navbarTitleIcon: require('@/assets/sidebar/nav_9_lg.png')
      }
    },
    children: [{
      path: 'rating-info-config',
      name: 'Rating-info-config',
      component: () =>
        import('@/views/rating-info-management/rating-info-config'),
      meta: {
        title: '评级信息配置'
      }
    }]
  },

  {
    path: '/test-tool',
    name: 'Test-tool',
    component: Layout,
    redirect: '/test-tool/name-character',
    meta: {
      title: '测试工具',
      icon: "test",
      menuIcons: {
        normal: require('@/assets/sidebar/nav_10.png'),
        active: require('@/assets/sidebar/nav_10_active.png'),
        navbarTitleIcon: require('@/assets/sidebar/nav_10_lg.png')
      }
    },
    children: [{
      path: 'sql-statement-test',
      component: () => import('@/views/test-tool/sql-statement-test'),
      meta: {
        title: 'SQL语句查询'
      }
    }, ]
  },
  // ********************************************************************************

  {
    path: '/log-management',
    component: Layout,
    redirect: '/log-management/operation-log',
    meta: {
      title: '日志管理',
      icon: 'log',
      menuIcons: {
        normal: require('@/assets/sidebar/nav_3.png'),
        active: require('@/assets/sidebar/nav_3_active.png'),
        navbarTitleIcon: require('@/assets/sidebar/nav_3_lg.png')
      }
    },
    children: [{
        path: 'operation-log',
        name: 'Operation-log',
        component: () => import('@/views/log-management/operation-log'),
        meta: {
          title: '操作日志'
        }
      },
      {
        path: 'login-log',
        name: 'Login-log',
        component: () => import('@/views/log-management/login-log'),
        meta: {
          title: '登录日志'
        }
      }
    ]
  },
  {
    path: '/authority-management',
    component: Layout,
    redirect: '/authority-management/authority',
    meta: {
      title: '成员管理',
      icon: 'permission',
      menuIcons: {
        normal: require('@/assets/sidebar/nav_6.png'),
        active: require('@/assets/sidebar/nav_6_active.png'),
        navbarTitleIcon: require('@/assets/sidebar/nav_6_lg.png')
      }
    },
    children: [{
        path: 'user',
        name: 'user',
        component: () => import('@/views/authority-management/user'),
        meta: {
          title: '成员管理'
        }
      },
      {
        path: 'authority',
        name: 'authority',
        component: () => import('@/views/authority-management/authority'),
        meta: {
          title: '角色权限'
        }
      }
    ]
  },
  {
    path: '/system-settings',
    name: 'System-settings',
    redirect: '/system-settings/system-config',
    component: Layout,
    meta: {
      title: '系统设置',
      icon: 'setUp',
      menuIcons: {
        normal: require('@/assets/sidebar/nav_7.png'),
        active: require('@/assets/sidebar/nav_7_active.png'),
        navbarTitleIcon: require('@/assets/sidebar/nav_7_lg.png')
      }
    },
    children: [
      {
        path: 'system-config',
        name: 'System-config',
        component: () => import('@/views/system-settings/system-config'),
        meta: {
          title: '系统配置'
        }
      },  {
        path: 'license-mgt',
        name: 'license-mgt',
        component: () => import('@/views/system-settings/license-mgt'),
        meta: {
          title: '授权管理'
        }
      },
      {
        path: 'dictionary-mgt',
        name: 'Dictionary-mgt',
        component: () => import('@/views/system-settings/dictionary-mgt'),
        meta: {
          title: '字典管理'
        }
      },
      {
        path: 'dictionary-details',
        name: 'Dictionary-details',
        hidden: true,
        component: () => import('@/views/system-settings/dictionary-details'),
        meta: {
          title: '字典详情',
          istab: true
        }
      },
      {
        path: 'code-value-mgt',
        name: 'Code-value-mgt',
        component: () => import('@/views/system-settings/code-value-mgt'),
        redirect: '/system-settings/code-value-mgt/code-value-type',
        meta: {
          title: '码值管理'
        },
        children: [{
            path: 'code-value-type',
            component: () =>
              import('@/views/system-settings/code-value-mgt/code-value-type'),
            name: 'Code-value-type',
            meta: {
              title: '码值类型'
            }
          },
          {
            path: 'code-value-content',
            component: () =>
              import(
                '@/views/system-settings/code-value-mgt/code-value-content'
              ),
            name: 'Code-value-content',
            meta: {
              title: '码值内容'
            }
          }
        ]
      }
    ]
  }

  // {
  //   path: '/log-manage',
  //   component: Layout,
  //   meta: { title: '日志管理', icon: "snippets", activeMenu: 'info' },
  //   children: [
  //     {
  //       path: '/log-manage',
  //       redirect: '/log-manage/operation-log',
  //     },
  //     {
  //       path: 'operation-log',
  //       component: () => import('@/views/log-manage/operationLog.vue'),
  //       meta: {
  //         title: '操作日志',
  //       }
  //     },
  //     {
  //       path: 'post-log',
  //       component: () => import('@/views/log-manage/postLog.vue'),
  //       meta: {
  //         title: '发送消息日志',
  //       }
  //     },
  //     {
  //       path: 'receive-log',
  //       component: () => import('@/views/log-manage/receiveLog.vue'),
  //       meta: {
  //         title: '接收消息日志',
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/terminology-structure',
  //   name: 'terminology-structure',
  //   component: Layout,
  //   meta: { title: '术语结构', icon: "cluster", activeMenu: 'info' },
  //   children: [
  //     {
  //       path: '/terminology-structure',
  //       redirect: '/terminology-structure/terminology-list',
  //     },
  //     {
  //       path: 'terminology-list',
  //       name: 'terminology-list',
  //       component: () => import('@/views/terminology-structure/terminologyList.vue'),
  //       meta: {
  //         title: '术语列表',
  //       }
  //     },
  //     {
  //       path: 'terminology-edit',
  //       name: 'terminology-edit',
  //       component: () => import('@/views/terminology-structure/terminologyEdit.vue'),
  //       props: route => ({ termName: route.query.termName, termId: route.query.termId }),
  //       meta: {
  //         title: '术语结构编辑',
  //       }
  //     },
  //     {
  //       path: 'terminology-add',
  //       name: 'terminology-add',
  //       component: () => import('@/views/terminology-structure/terminologyAdd.vue'),
  //       meta: {
  //         title: '新增术语',
  //       }
  //     },
  //     {
  //       path: 'terminology-subscription',
  //       name: 'terminology-subscription',
  //       component: () => import('@/views/terminology-structure/terminologySubscription'),
  //       meta: {
  //         title: '订阅术语',
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/terminology-data',
  //   name: 'terminology-data',
  //   component: Layout,
  //   meta: { title: '术语数据', icon: "database", activeMenu: 'info' },
  //   children: [
  //     {
  //       path: '/terminology-data',
  //       redirect: '/terminology-data/terminology-edit',
  //     },
  //     {
  //       path: 'terminology-edit',
  //       name: "terminology-edit",
  //       component: () => import('@/views/terminology-data/terminologyEdit'),
  //       meta: {
  //         title: '术语编辑',
  //       }
  //     },
  //     {
  //       path: 'terminology-edit-viewdata',
  //       name: "terminology-edit-viewdata",
  //       component: () => import('@/views/terminology-data/terminologyEdit/viewdata'),
  //       props: route => ({ dictTable: route.query.dictTable, termId: route.query.termId }),
  //       hidden: true,
  //       meta: {
  //         title: '术语字段编辑',
  //       }
  //     },

  //     {
  //       path: 'terminology-examine',
  //       name: "terminology-examine",
  //       component: () => import('@/views/terminology-data/terminologyExamine'),
  //       meta: {
  //         title: '术语审批',
  //       }
  //     },
  //     {
  //       path: 'terminology-examine-viewExamine',
  //       name: "terminology-examine-viewExamine",
  //       component: () => import('@/views/terminology-data/terminologyExamine/viewExamine'),
  //       props: route => ({ dictTable: route.query.dictTable, termId: route.query.termId }),
  //       hidden: true,
  //       meta: {
  //         title: '术语内容审批',
  //       }
  //     },
  //     {
  //       path: 'terminology-publish',
  //       name: "terminology-publish",
  //       component: () => import('@/views/terminology-data/terminologyPublish'),
  //       meta: {
  //         title: '术语发布',
  //       }
  //     },

  //   ]
  // },
  // {
  //   path: '/terminology-mapping',
  //   name: 'terminology-mapping',
  //   component: Layout,
  //   meta: { title: '术语映射', icon: "map", activeMenu: 'info' },
  //   children: [
  //     {
  //       path: '/terminology-mapping',
  //       redirect: '/terminology-mapping/mapping-list',
  //     },
  //     {
  //       path: 'mapping-list',
  //       name: "mapping-list",
  //       component: () => import('@/views/terminology-mapping/mappingList'),
  //       meta: {
  //         title: '映射关系列表',
  //       }
  //     },
  //     {
  //       path: 'mapping-content',
  //       name: "mapping-content",
  //       component: () => import('@/views/terminology-mapping/mappingContent.vue'),
  //       props: route => ({ termName: route.query.termName, termId: route.query.termId }),
  //       meta: {
  //         title: '术语映射内容',
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/authority-management',
  //   component: Layout,
  //   meta: { title: '权限管理', icon: 'security' },
  //   children: [
  //     {
  //       path: '/authority-management/authority',
  //       name: 'authority',
  //       component: () => import("@/views/authority-management/authority"),
  //       meta: { title: '权限配置' },
  //     },
  //     {
  //       path: '/authority-management/user',
  //       name: 'user',
  //       component: () => import("@/views/authority-management/user"),
  //       meta: { title: '用户管理' },
  //     },
  //   ]
  // },
  // {
  //   path: '/setting',
  //   name: 'setting',
  //   component: Layout,
  //   meta: { title: '系统配置', icon: 'el-icon-setting' },
  //   children: [
  //     {
  //       path: '',
  //       component: () => import("@/views/setting/index.vue"),
  //       meta: { title: '系统配置', },
  //     },

  //   ]
  // },
]

// export const asyncRoutes = []
const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({
      y: 0
    }),
    routes: constantRoutes
  })

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
