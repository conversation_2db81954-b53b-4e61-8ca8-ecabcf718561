export function flatteningKeys(tree,key) {
    
    if (!Array.isArray(tree)) return;
    let result = [], stack = [];
    tree.forEach(v => {
        stack.push(v)
        while (stack.length != 0) {
            var item = stack.pop();
            result.push(key?item[key]:item);
            var children = item.children;
            for (var i = children.length - 1; i >= 0; i--) {
                stack.push(children[i]);
            }

        }
    })
    
    return result
}