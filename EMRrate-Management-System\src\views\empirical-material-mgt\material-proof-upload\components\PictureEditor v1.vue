<template>
  <div
    class="picture-editor"
    v-loading="thumbnaillistststate"
    :element-loading-text="
      (thumbnaillist.length === 0) & (afterrequest === true)
        ? '暂无图片，请先添加图片'
        : '图片加载中，请稍后'
    "
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(255, 255, 255, 0.8)"
  >
    <div class="picture-thumbnail">
      <div
        v-for="(item, index) in thumbnaillist"
        :key="index"
        class="picture-thumbnail-item"
      >
        <div class="imagebox">
          <el-image
            :src="'data:image/png;base64,' + item.base64Str"
            style="width: 226px; max-width: 236px; max-height: 144px"
          ></el-image>
        </div>
        <div class="ordinalindex">{{ item.ordinalindex }}</div>
        <div class="operation_show1">
          <el-button
            @click="clickimage(item, index)"
            icon="el-icon-edit"
            type=""
            size="mini"
            >编辑</el-button
          >
        </div>
        <!-- <div class="operation_show2">
          <el-button @click="getImages(item)" type="" size="mini"
            ><svg-icon icon-class="icon_scale"></svg-icon
          ></el-button>
        </div> -->
      </div>
    </div>
    <el-dialog
      title="编辑图片"
      :visible.sync="dialogTableVisible"
      width="1376px"
      :close-on-click-modal="false"
      v-dialogDrag
    >
      <div id="tui-image-editor">获取原图失败,重新获取</div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogTableVisible = false">取 消</el-button>
        <el-button @click="openeditimg()">重新获取图片</el-button>
        <el-button type="primary" @click="uploadImg()"
          >确定修改并提交</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
// 引入样式
import "tui-image-editor/dist/tui-image-editor.css";
import "tui-color-picker/dist/tui-color-picker.css";
const ImageEditor = require("tui-image-editor");

import {
  queryAllflowpath,
  getImages,
  uploadMultipleFiles,
} from "@/api/empirical-material-mgt";
const localeZh = {
  // override default English locale to your custom
  Crop: "裁剪",
  ZoomIn: "放大",
  ZoomOut: "缩小",
  Hand: "拖拽",
  History: "历史记录",
  DeleteAll: "全部删除",
  Delete: "删除",
  Undo: "撤销",
  Redo: "反撤销",
  Reset: "重置",
  Flip: "镜像",
  Rotate: "旋转",
  Draw: "画",
  Shape: "形状标注",
  Icon: "图标标注",
  Text: "文字标注",
  Mask: "遮罩",
  Filter: "滤镜",
  Bold: "加粗",
  Italic: "斜体",
  Underline: "下划线",
  Left: "左对齐",
  Center: "居中",
  Right: "右对齐",
  Color: "颜色",
  "Text size": "字体大小",
  Custom: "自定义",
  Square: "正方形",
  Apply: "应用",
  Cancel: "取消",
  "Flip X": "X 轴",
  "Flip Y": "Y 轴",
  Range: "区间",
  Stroke: "描边",
  Fill: "填充",
  Circle: "圆",
  Triangle: "三角",
  Rectangle: "矩形",
  Free: "曲线",
  Straight: "直线",
  Arrow: "箭头",
  "Arrow-2": "箭头2",
  "Arrow-3": "箭头3",
  "Star-1": "星星1",
  "Star-2": "星星2",
  Polygon: "多边形",
  Location: "定位",
  Heart: "心形",
  Bubble: "气泡",
  "Custom icon": "自定义图标",
  "Load Mask Image": "加载蒙层图片",
  Grayscale: "灰度",
  Blur: "模糊",
  Sharpen: "锐化",
  Emboss: "浮雕",
  "Remove White": "除去白色",
  Distance: "距离",
  Brightness: "亮度",
  Noise: "噪音",
  "Color Filter": "彩色滤镜",
  Sepia: "棕色",
  Sepia2: "棕色2",
  Invert: "负片",
  Pixelate: "像素化",
  Threshold: "阈值",
  Tint: "色调",
  Multiply: "正片叠底",
  Blend: "混合色",
  // etc...
};
const customTheme = {
  // image 左上角度图片
  "common.bi.image": "", // 在这里换上你喜欢的logo图片
  "common.bisize.width": "0px",
  "common.bisize.height": "0px",
  "common.backgroundImage": "none",
  "common.backgroundColor": "#f3f4f6",
  // "common.border": "1px solid #444",

  // header
  "header.backgroundImage": "none",
  "header.backgroundColor": "#f3f4f6",
  "header.border": "0px",
  "header.display": "none",

  // load button
  "loadButton.backgroundColor": "#fff",
  "loadButton.border": "1px solid #ddd",
  "loadButton.color": "#222",
  "loadButton.fontFamily": "NotoSans, sans-serif",
  "loadButton.fontSize": "12px",
  "loadButton.display": "none", // 可以直接隐藏掉

  // download button
  "downloadButton.backgroundColor": "#fdba3b",
  "downloadButton.border": "1px solid #fdba3b",
  "downloadButton.color": "#fff",
  "downloadButton.fontFamily": "NotoSans, sans-serif",
  "downloadButton.fontSize": "12px",
  "downloadButton.display": "none", // 可以直接隐藏掉

  // icons default
  "menu.normalIcon.color": "#8a8a8a",
  "menu.activeIcon.color": "#555555",
  "menu.disabledIcon.color": "#434343",
  "menu.hoverIcon.color": "#e9e9e9",
  "submenu.normalIcon.color": "#8a8a8a",
  "submenu.activeIcon.color": "#e9e9e9",

  "menu.iconSize.width": "24px",
  "menu.iconSize.height": "24px",
  "submenu.iconSize.width": "32px",
  "submenu.iconSize.height": "32px",

  // submenu primary color
  "submenu.backgroundColor": "#1e1e1e",
  "submenu.partition.color": "#858585",

  // submenu labels
  "submenu.normalLabel.color": "#858585",
  "submenu.normalLabel.fontWeight": "lighter",
  "submenu.activeLabel.color": "#fff",
  "submenu.activeLabel.fontWeight": "lighter",

  // checkbox style
  "checkbox.border": "1px solid #ccc",
  "checkbox.backgroundColor": "#fff",

  // rango style
  "range.pointer.color": "#fff",
  "range.bar.color": "#666",
  "range.subbar.color": "#d1d1d1",

  "range.disabledPointer.color": "#414141",
  "range.disabledBar.color": "#282828",
  "range.disabledSubbar.color": "#414141",

  "range.value.color": "#fff",
  "range.value.fontWeight": "lighter",
  "range.value.fontSize": "11px",
  "range.value.border": "1px solid #353535",
  "range.value.backgroundColor": "#151515",
  "range.title.color": "#fff",
  "range.title.fontWeight": "lighter",

  // colorpicker style
  "colorpicker.button.border": "1px solid #1e1e1e",
  "colorpicker.title.color": "#fff",
};
export default {
  data() {
    return {
      // 整个编辑图片弹出框
      dialogTableVisible: false,
      // 缩率图加载提示
      thumbnaillistststate: false,
      // 请求之后无图片
      afterrequest: false,
      // 编辑图片名字
      imgname: "",
      editpath: "",
      // 缩率图列表
      thumbnaillist: [],
      // 编辑图片实例
      instance: null,
      // 修改图片提交表单
      submitform: {
        directoryCode: "",
        directoryName: "",
        evaluationContentId: "",
        materialFlowPathId: "",
      },
      srcList: [],
    };
  },
  methods: {
    // 点击左侧树获取图片缩率图
    async queryAllflowpath(data) {
      this.thumbnaillist = [];
      this.afterrequest = false;
      this.thumbnaillistststate = true;
      await queryAllflowpath({
        directoryCode: data.directoryCode,
        directoryName: data.directoryName,
        projectId: this.$store.state.user.projectMsg.id,
      }).then((res) => {
        // 重新排列数组，方便展示
        if (res.data.length > 0) {
          res.data.forEach((element) => {
            element.imagesBase64List.forEach((item, index) => {
              this.thumbnaillist.push({
                ...element,
                ...item,
                ordinalindex:
                  element.serialNum +
                  (element.imagesBase64List.length >= 1
                    ? "(" + Number(index + 1) + ")"
                    : ""),
              });
            });
            this.thumbnaillistststate = false;
          });
        } else {
          this.afterrequest = true;
        }
      });
    },
    // 点击图片展开
    clickimage(data, index) {
      this.submitform = {
        directoryCode: data.directoryCode,
        directoryName: data.directoryName,
        evaluationContentId: data.evaluationContentId,
        materialFlowPathId: data.id,
      };
      this.editpath = "";
      this.dialogTableVisible = true;
      this.imgname = data.fileName;
      getImages({ ...this.submitform, fileName: data.fileName,projectId: this.$store.state.user.projectMsg.id, }).then((res) => {
        let blob = new Blob([res], { type: "image/jpeg/png" });
        this.editpath = window.URL.createObjectURL(blob);
        this.openeditimg();
      });
    },
    // 获取高清照片地址
    getImages(data) {
      getImages({
        directoryCode: data.directoryCode,
        directoryName: data.directoryName,
        evaluationContentId: data.evaluationContentId,
        materialFlowPathId: data.id,
        fileName: data.fileName,
        projectId: this.$store.state.user.projectMsg.id,
      }).then((res) => {
        let blob = new Blob([res], { type: "image/jpeg/png" });
        this.srcList = [window.URL.createObjectURL(blob)];
      });
    },
    // 打开编辑图片
    openeditimg() {
      // 使用编辑组件
      this.instance = new ImageEditor(
        document.querySelector("#tui-image-editor"),
        {
          includeUI: {
            // 表示使用它内置的 UI 控件
            loadImage: {
              path: this.editpath,
              name: this.imgname,
            }, // 默认加载的图片
            initMenu: "draw", // 表示编辑器加载后，第一个被选中的操作菜单功能
            menu: [
              "crop", // 裁切
              // "flip", // 翻转
              "rotate", // 旋转
              "draw", // 添加绘画
              "shape", // 添加形状
              "icon", // 添加图标
              "text", // 添加文本
              "mask", // 添加覆盖
              // "filter", // 添加滤镜
            ], // 支持的菜单
            locale: localeZh,
            theme: customTheme,
            usageStatistics: false,
            menuBarPosition: "right", // 菜单位置栏的位置，有四个选项可选：'top', 'bottom', 'left', 'right'
          },
          // cssMaxWidth: 1100, // 编辑器 Canvas 的最大宽度
          // cssMaxHeight: 500, // 编辑器 Canvas 的最大高度
          selectionStyle: {
            cornerSize: 20,
            rotatingPointOffset: 70,
          },
        }
      );
    },
    // 保存图片
    uploadImg() {
      const base64String = this.instance.toDataURL();
      const data = window.atob(base64String.split(",")[1]);
      const ia = new Uint8Array(data.length);
      for (let i = 0; i < data.length; i++) {
        ia[i] = data.charCodeAt(i);
      }
      // 将图片转换成二进制形式发送给后台
      const blob = new Blob([ia], { type: "image/png" });
      // 获取上传需要的参数
      // 调用上传接口
      this.fileData = new FormData();
      const data1 = {
        ...this.submitform,
        projectId: this.$store.state.user.projectMsg.id,
      };
      Object.keys(data1).forEach((key) => {
        const value = data1[key];
        if (Array.isArray(value)) {
          value.forEach((subValue, i) =>
            this.fileData.append(key + `[${i}]`, subValue)
          );
        } else {
          this.fileData.append(key, data1[key]);
        }
      });
      let newFile = new File([blob], this.imgname, {
        type: "png",
      }); //创建出来也是不可编辑的file对象
      this.fileData.append("files", newFile);
      uploadMultipleFiles(this.fileData)
        .then((res) => {
          if (res.status === 0) {
            this.$message({
              message: "图片编辑成功!",
              type: "success",
            });
            this.dialogTableVisible = false;
          } else {
            this.$message({
              message: res.msg,
            });
          }
        })
        .catch((err) => {
          this.$message({
            message: err.msg,
          });
        });
    },
  },
};
</script>

<style scoped lang="scss">
.picture-editor {
  .picture-thumbnail {
    margin-bottom: 10px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    .picture-thumbnail-item {
      width: 240px;
      margin-bottom: 20px;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      position: relative;
      .imagebox {
        height: 144px;
        .el-image {
          // margin: 0px 10px;
          cursor: pointer;
          background: #ffffff;
          border-radius: 9px;
          border: 1px solid #dcdcdf;
        }
      }

      .ordinalindex {
        line-height: 20px;
        text-align: center;
      }
      .el-image:hover {
        opacity: 0.3;
        background: #2c3148;
      }
      .operation_show1 {
        display: none;
        position: absolute;
        top: 10px;
        left: 30px;
        .el-button {
          background: #313944;
          border: 0px solid transparent;
          padding: 8px 10px;
        }
      }
      .operation_show2 {
        display: none;
        position: absolute;
        top: 10px;
        right: 30px;
        .el-button {
          background: #ffffff;
          border: 0px solid transparent;
          padding: 8px 10px;
          color: #555555;
        }
      }
    }

    .picture-thumbnail-item:hover .operation_show1 {
      display: block;
    }
    .picture-thumbnail-item:hover .operation_show2 {
      display: block;
    }
  }
}
.tui-image-editor-container {
  height: 80vh !important;
  // width: calc(100% - 64px);
}
.el-dialog .el-dialog__body {
  padding: 0px !important;
  // display: inline;
}

.drawing-container {
  height: 900px;
}
</style>