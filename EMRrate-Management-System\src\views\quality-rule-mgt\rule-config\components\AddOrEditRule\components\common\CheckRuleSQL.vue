<template>
  <div class="check-rule-SQL">
    <div class="left">
      <el-button type="primary" @click="$emit('goCheck')">获取SQL</el-button>
    </div>
    <div class="right" v-loading="isLoading">
      <el-card shadow="never">
       {{ qstDetailFieldSql }}
      </el-card>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    checkSQLData: {
      type: String,
    },
    isLoading: {
      type: Boolean,
    },
  },
  computed: {
    qstDetailFieldSql() {
      return this.checkSQLData == ""
        ? "暂无数据"
        : this.checkSQLData
    },
  },
}
</script>

<style lang="scss" scoped>
.check-rule-SQL {
  display: flex;
  .left {
    margin-left: 30px;
    padding-left: 20px;
    width: 110px;
  }
  .right {
    flex: 1;
    margin-bottom: 20px;
    span {
      color: rgb(123, 123, 123);
    }
  }
}
</style>
