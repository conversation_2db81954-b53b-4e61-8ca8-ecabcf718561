<template>
  <MainCard>
    <div class="authority-container">
      <div class="table-container">
        <div class="authorityButtonContainer">
          <el-button
            type="primary"
            @click="handleCreate">
            新建角色
          </el-button>
        </div>
        <el-table
          :data="tableData"
          style="width: 100%; margin: 15px 0px"
          ref="multipleTable"
          :header-cell-style="{ background: '#fff', color: '#606266' }"
          tooltip-effect="dark"
          size="mini">
          <el-table-column
            type="index"
            label="序号"
            width="50">
          </el-table-column>

          <el-table-column
            prop="roleName"
            label="角色名称"
            width="230"></el-table-column>
          <el-table-column
            prop="$permissionIdsKey"
            label="权限">
          </el-table-column>
          <el-table-column
            prop="description"
            label="备注"
            width="130"></el-table-column>

          <el-table-column
            prop="action"
            label="操作"
            width="230">
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="handleEdit('edit', scope.row)"
                >编辑</el-button
              >
              <span style="margin: 0 2px">
                <el-divider direction="vertical"></el-divider>
              </span>
              <el-button
                type="text"
                @click="deleteCount(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.size"
          :total="pagination.total"
          :current-page="pagination.current"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          @prev-click="handleCurrentChange"
          @next-click="handleCurrentChange">
        </el-pagination>
      </div>
      <el-dialog
        v-dialogDrag
        :title="flag == 'create' ? '新建角色' : '编辑角色'"
        :visible="flag == 'create' || flag == 'edit'"
        width="40%"
        @close="closeCreateModal"
        top="50px"
        :close-on-click-modal="false">
        <el-form
          :model="createForm"
          size="small"
          :rules="rules"
          label-width="90px"
          class="modalContainer"
          ref="createFormRef">
          <el-form-item
            label="角色名称"
            prop="roleName"
            style="margin-bottom: 15px">
            <el-input v-model="createForm.roleName"></el-input>
          </el-form-item>
          <el-form-item
            label="备注"
            style="margin-bottom: 15px">
            <el-input v-model="createForm.description"></el-input>
          </el-form-item>
          <el-form-item label="管理权限">
            <div class="treeContainer">
              <el-tree
                show-checkbox
                :data="defaultData"
                node-key="permissionId"
                ref="tree"
                highlight-current
                openAll
                :default-checked-keys="defaultKeys"
                :props="{ label: 'permissionName' }">
              </el-tree>
            </div>
          </el-form-item>
        </el-form>
        <div slot="footer">
          <el-button
            @click="flag = ''"
            size="small"
            >取 消</el-button
          >
          <el-button
            type="primary"
            @click="submitForm('createFormRef')"
            size="mini"
            >确 定</el-button
          >
        </div>
      </el-dialog>
    </div>
  </MainCard>
</template>

<script>
import {
  getRoleList as _getRoleList,
  addRole as _addRole,
  deleteRole as _deleteRole,
  updateRole as _updateRole,
  getPermissioList as _getPermissioList
} from '@/api/user'
import treeTransfer from 'el-tree-transfer'
import { flatteningKeys } from '@/utils/tree'
var _ = require('lodash')

function defaultCreateForm() {
  return {
    description: '',
    roleName: '',
    permissionIds: []
  }
}
export default {
  components: { treeTransfer },
  created() {
    this.getRoleQueryList()
    this.getPermissioList()
  },
  data() {
    return {
      breadcrumbs: [],
      createForm: defaultCreateForm(),
      //分页
      pagination: {
        current: 1,
        size: 10,
        total: null
      },
      rules: {
        roleName: [
          { required: true, message: '请输入角色名称', trigger: 'blur' }
        ]
      },
      tableData: [],
      row: '',
      flag: '',
      defaultData: [],
      fromData: [],
      toData: [],
      defaultKeys: [] //穿梭框默认选中的
    }
  },
  methods: {
    //点击新建按钮
    handleCreate() {
      this.flag = 'create'
      this.defaultKeys = []
      this.$refs.tree && this.$refs.tree.setCheckedKeys(this.defaultKeys)
    },

    //点击编辑按钮
    handleEdit(flag, row) {
      this.flag = flag
      this.row = JSON.parse(JSON.stringify(row))
      delete this.row.$permissionIdsKey
      this.createForm.roleName = row.roleName
      this.createForm.description = row.description
      //过滤父节点
      let defaultPermissions = flatteningKeys(row.permissionIds, '')
      this.defaultKeys = defaultPermissions
        .filter((permission) => {
          return permission.description.indexOf(':') != -1
        })
        .map((v) => v.permissionId)
      this.$refs.tree && this.$refs.tree.setCheckedKeys(this.defaultKeys)
    },
    // 校验
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.addOrEditRole()
        } else {
          return false
        }
      })
    },
    //新建或者编辑角色
    async addOrEditRole() {
      let permissionIds = [].concat(
        this.$refs.tree.getCheckedNodes(),
        this.$refs.tree.getHalfCheckedNodes()
      )
      // console.log("permissionIds", permissionIds);
      permissionIds = permissionIds.map((v) => {
        let result = { ...v }
        delete result.children
        return result
      })

      if (this.flag == 'create') {
        await _addRole({
          ...this.createForm,
          permissionIds
        }).then((res) => {
          if (res && res.msg == 'success') {
            this.$message.success('添加角色成功!')
            this.flag = ''
            this.getRoleQueryList()
          } else {
            this.$message.error(res.msg)
          }
        })
      } else {
        await _updateRole({
          ...this.row,
          ...this.createForm,
          permissionIds
        }).then((res) => {
          if (res && res.msg == 'success') {
            this.$message.success('修改角色成功!')
            this.flag = ''
            this.getRoleQueryList()
          } else {
            this.$message.error(res.msg)
          }
        })
      }
      this.defaultKeys = []
    },

    closeCreateModal() {
      this.flag = ''
      this.createForm = defaultCreateForm()
      this.defaultKeys = []
      this.$refs['createFormRef'].resetFields()
    },

    getPermissioList() {
      _getPermissioList().then((res) => {
        if (res && 'data' in res) {
          this.defaultData = res.data
        }
      })
    },
    //获取角色列表
    getRoleQueryList() {
      _getRoleList({
        page: this.pagination.current,
        size: this.pagination.size
      }).then((res) => {
        if (res && 'data' in res) {
          this.tableData =
            res.data.list.map((item) => {
              item.$permissionIdsKey =
                Array.isArray(item.permissionIds) &&
                item.permissionIds
                  .map((v) => {
                    return v.permissionName
                  })
                  .join(',')
              return item
            }) || []
          this.pagination.current = res.data.pageNum
          this.pagination.size = res.data.pageSize
          this.pagination.total = res.data.total
        }
      })
    },
    //删除角色
    deleteCount(row) {
      this.$confirm('此操作将删除角色, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          _deleteRole({ roleId: row.roleId }).then((res) => {
            if (res && res.msg == 'success') {
              this.$message.success('删除成功!')
              this.getRoleQueryList()
            } else {
              this.$message.error(res.msg)
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    handleSizeChange(size) {
      this.pagination.size = size
      this.getRoleQueryList()
    },
    handleCurrentChange(current) {
      this.pagination.current = current
      this.getRoleQueryList()
    }
  },
  computed: {}
}
</script>

<style lang="scss" scope>
.table-container {
  position: relative;
  width: 100%;
  background-color: white;
}
.modalContainer {
  .treeContainer {
    height: 400px;
    overflow: auto;
  }
}
</style>
