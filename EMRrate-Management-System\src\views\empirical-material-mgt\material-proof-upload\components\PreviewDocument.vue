<template>
  <el-drawer
    title="预览文档"
    :visible.sync="drawer"
    direction="ltr"
    size="50%"
    @closed="handlerClose"
  >
    <div
      class="el-drawercontent"
      v-loading="fileLoading"
      element-loading-text="加载中,请稍等"
    >
      <div id="container"></div>
    </div>
  </el-drawer>
</template>

<script>
import { previewWord } from "@/api/empirical-material-mgt";
export default {
  data() {
    return {
      drawer: false,
      fileLoading: true,
    };
  },
  methods: {
    // 新增打开
    opendrawer(data1) {
      this.drawer = true;
      this.fileLoading = true;
      previewWord({...data1,projectId: this.$store.state.user.projectMsg.id,}).then((res) => {
        document.getElementById("container").innerHTML = res;
        this.fileLoading = false;
      });
    },
    // 关闭抽屉函数
    handlerClose() {
      this.drawer = false;
      document.getElementById("container").innerHTML = "";
    },
  },
};
</script>

<style  scoped lang="scss">
.el-drawercontent {
  padding: 20px;
}
#container {
  height: calc(90vh);
  display: flex;
  justify-content: center;
  overflow-y: scroll;
  overflow-x: scroll;
  ::v-deep div {
    width: 550px !important;
    margin: 0px !important;
    ::v-deep span {
      width: 550px !important;
      word-break: break-all !important;
      color: red !important;
    }
  }
}
</style>