<template>
  <MainCard>
    <div class="collection-task-status">
      <div class="task-status-header">
        <el-form
          :model="queryData"
          ref="ruleForm"
          :inline="true">
          <el-form-item
            label="数据源ID"
            prop="dataSourceName">
            <el-input
              v-model="queryData.dataSourceId"
              placeholder="请输入数据源ID"></el-input>
          </el-form-item>
          <el-form-item
            label="数据源名称"
            prop="dataSourceName">
            <el-input
              v-model="queryData.dataSourceName"
              placeholder="请输入数据源名称"></el-input>
          </el-form-item>
          <el-form-item
            label="任务开始时间"
            prop="taskStartDt">
            <el-date-picker
              v-model="queryData.taskStartDt"
              type="date"
              placeholder="请选择任务开始时间"
              value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
          <el-form-item
            label="任务结束时间"
            prop="taskEndDt">
            <el-date-picker
              v-model="queryData.taskEndDt"
              type="date"
              placeholder="请选择任务结束时间"
              value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
          <el-form-item
            label="任务状态"
            prop="taskState">
            <el-select
              style="width: 140px"
              v-model="queryData.taskState"
              placeholder="请选择任务状态">
              <el-option
                label="全部"
                value=""></el-option>
              <el-option
                label="执行中"
                value="00"></el-option>
              <el-option
                label="成功"
                value="11"></el-option>
              <el-option
                label="失败"
                value="99"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="searchTaskStatus"
              icon="el-icon-search"
              >搜索</el-button
            >
          </el-form-item>
          <el-form-item>
            <el-button @click="resetForm('ruleForm')">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="task-status-table">
        <el-table
          :data="tableData"
          ref="taskStatusTable"
          style="width: 100%"
          v-loading="loading"
          :header-cell-style="{ background: '#fff', color: '#606266' }">
          <el-table-column
            width="200"
            prop="taskGroupId"
            label="任务编号">
          </el-table-column>
          <el-table-column
            prop="taskGroupName"
            label="任务名称">
          </el-table-column>
          <el-table-column
            prop="dataSourceId"
            label="数据源ID">
          </el-table-column>
          <el-table-column
            prop="dataSourceName"
            label="数据源名称">
          </el-table-column>
          <el-table-column
            prop="taskState"
            label="任务状态">
            <template slot-scope="scope">
              <span v-if="scope.row.taskState === '00'">执行中</span>
              <span v-else-if="scope.row.taskState === '11'">成功</span>
              <span v-else-if="scope.row.taskState === '99'">失败</span>
            </template>
          </el-table-column>
          <el-table-column
            width="540"
            prop="taskResult"
            label="任务执行结果">
          </el-table-column>
          <el-table-column
            prop="taskStartDt"
            width="200"
            label="任务开始时间">
          </el-table-column>
          <el-table-column
            width="200"
            prop="taskEndDt"
            label="任务结束时间">
          </el-table-column>
        </el-table>
      </div>
      <div class="task-status-pag">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryData.pageNum"
          :page-sizes="[5, 10, 15, 20]"
          :page-size="queryData.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalNum">
        </el-pagination>
      </div>
    </div>
  </MainCard>
</template>

<script>
import { getTaskExecutionResult } from '@/api/dataSourceStructureMgt/collectionMgt'
export default {
  name: 'aaa',
  data() {
    return {
      queryData: {
        // 查询数据
        dataSourceId: '',
        dataSourceName: '',
        pageNum: 1,
        pageSize: 10,
        taskEndDt: '',
        // taskGroupId: "",
        // taskInstanceId: 0,
        taskStartDt: '',
        taskState: ''
      },
      tableData: [],
      totalNum: 1,
      loading: false
    }
  },
  created() {
    // 初始化查询执行结果列表
    this.queryTaskExecutionResult()
  },
  methods: {
    // 查询任务执行结果列表
    queryTaskExecutionResult() {
      this.loading = true
      getTaskExecutionResult(this.queryData).then((res) => {
        if (res.status === 0) {
          // console.log(res.data.list)
          this.tableData = res.data.list
          this.totalNum = res.data.total
          this.loading = false
        } else {
          this.loading = false
          this.$message({
            message: res.msg,
            type: 'error'
          })
        }
      })
    },
    // 搜索任务状态
    searchTaskStatus() {
      this.queryTaskExecutionResult(this.queryData)
    },
    // 改变页面显示条数
    handleSizeChange(val) {
      this.queryData.pageSize = val
      this.queryTaskExecutionResult(this.queryData)
    },
    // 重置
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.queryTaskExecutionResult()
    },
    // 改变页数
    handleCurrentChange(val) {
      this.queryData.pageNum = val
      this.queryTaskExecutionResult(this.queryData)
    }
  }
}
</script>

<style scoped lang="scss">
.collection-task-status {
  .task-status-header {
    /* margin: 10px 0; */
    .search {
      margin: 0 20px;
    }
  }
  .task-status-pag {
    margin-top: 10px;
  }
}
</style>
