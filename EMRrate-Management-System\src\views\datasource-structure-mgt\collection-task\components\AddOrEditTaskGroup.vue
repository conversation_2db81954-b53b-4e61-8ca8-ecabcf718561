<template>
  <div class="add-task-group">
    <el-dialog
      v-dialogDrag
      :title="btnType ? '新增任务组' : '编辑任务组'"
      :visible.sync="dialogFormVisible"
      @open="handlerOpen"
      @closed="handlerClose"
      width="40%"
      :close-on-click-modal="false"
    >
      <el-form
        :model="formData"
        :rules="rules"
        ref="ruleForm"
        :label-width="formLabelWidth"
      >
        <el-form-item label="任务名称" prop="jobName">
          <el-input v-model="formData.jobName"></el-input>
        </el-form-item>
        <el-form-item
          v-if="formData.jobExeType === '0'"
          label="任务描述"
          prop="jobDesc"
        >
          <el-input v-model="formData.jobDesc"></el-input>
        </el-form-item>
        <el-form-item label="执行方式" prop="jobExeType">
          <el-select
            @change="handlerjobExeTypeSelect"
            v-model="formData.jobExeType"
            placeholder="请选择执行方式"
          >
            <el-option
              v-for="item in jobExeTypeData"
              :key="item.id"
              :label="item.contentValue"
              :value="item.contentKey"
            ></el-option>
          </el-select>
        </el-form-item>

        <!-- 定时调度时 -->
        <template v-if="formData.jobExeType === '1'">
          <!-- 定时调度时各种频次的公共部分/每分/每天 start-->
          <el-form-item label="执行频率" prop="jobExeRate">
            <el-select
              @change="handlerSelectChange"
              v-model="formData.jobExeRate"
              placeholder="请选择执行频率"
            >
              <el-option label="每分" value="1"></el-option>
              <el-option label="每小时" value="2"></el-option>
              <el-option label="每天" value="3"></el-option>
              <el-option label="每周" value="4"></el-option>
              <el-option label="每月" value="5"></el-option>
            </el-select>
          </el-form-item>
          <el-row>
            <el-col :span="12">
              <el-form-item label="开始日期" prop="jobStartDate">
                <el-date-picker
                  v-model="formData.jobStartDate"
                  type="date"
                  placeholder="请选择开始日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="结束日期" prop="jobEndDate">
                <el-date-picker
                  v-model="formData.jobEndDate"
                  type="date"
                  placeholder="请选择结束日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="起始时间" prop="jobStartTime">
            <el-time-picker
              placeholder="请选择起始时间"
              v-model="formData.jobStartTime"
              value-format="HH:mm:ss"
              style="width: 100%"
            ></el-time-picker>
          </el-form-item>
          <!-- 定时调度时各种频次的公共部分/每分/每天 end-->

          <!-- 每小时 start-->
          <template v-if="formData.jobExeRate === '2'">
            <el-form-item label="间隔时间" prop="jobSpan">
              <el-input
                v-model="formData.jobSpan"
                placeholder="单位：小时(h)"
              ></el-input>
            </el-form-item>
          </template>
          <!-- 每小时 end-->

          <!-- 每周 start-->
          <template v-if="formData.jobExeRate === '4'">
            <el-form-item label="间隔时间" prop="jobSpan">
              <el-input
                v-model="formData.jobSpan"
                placeholder="单位：小时(h)"
              ></el-input>
            </el-form-item>
            <el-form-item label="周执行星期" ref="week" prop="weekCheckList">
              <el-checkbox-group v-model="weekCheckList">
                <el-checkbox
                  v-for="item in weekData"
                  :key="item.value"
                  :label="item.value"
                  >{{ item.label }}</el-checkbox
                >
              </el-checkbox-group>
            </el-form-item>
          </template>
          <!-- 每周 end-->

          <!-- 每月 start-->
          <template v-if="formData.jobExeRate === '5'">
            <el-form-item label="间隔时间" prop="jobSpan">
              <el-input
                v-model="formData.jobSpan"
                placeholder="单位：小时(h)"
              ></el-input>
            </el-form-item>
            <el-form-item label="执行的月份" prop="monthCheckList">
              <el-checkbox-group v-model="monthCheckList">
                <el-checkbox label="1">一月</el-checkbox>
                <el-checkbox label="2">二月</el-checkbox>
                <el-checkbox label="3">三月</el-checkbox>
                <el-checkbox label="4">四月</el-checkbox>
                <el-checkbox label="5">五月</el-checkbox>
                <el-checkbox label="6">六月</el-checkbox>
                <el-checkbox label="7">七月</el-checkbox>
                <el-checkbox label="8">八月</el-checkbox>
                <el-checkbox label="9">九月</el-checkbox>
                <el-checkbox label="10">十月</el-checkbox>
                <el-checkbox label="11">十一月</el-checkbox>
                <el-checkbox label="12">十二月</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="执行日期">
              <el-radio-group
                v-model="formData.chkType"
                @change="handlerRadioChange"
                style="display: flex; align-items: center"
              >
                <el-radio label="1">
                  日期：
                  <el-input
                    style="width: 70px"
                    v-model="dayOfMonthValue"
                    :disabled="isDisable1"
                  ></el-input>
                </el-radio>
                <el-radio label="2">月末</el-radio>
                <el-radio label="3">
                  星期：
                  <el-select
                    style="width: 100px; display: inline-block"
                    :disabled="isDisable2"
                    v-model="formData.weekTime"
                    placeholder="请选周次"
                  >
                    <el-option
                      v-for="item in weekTimeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                  <el-select
                    :disabled="isDisable3"
                    style="width: 100px; display: inline-block"
                    v-model="formData.dayOfWeek2"
                    placeholder="请选星期"
                  >
                    <el-option
                      v-for="item in weekData"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </template>
          <!-- 每月 end-->
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm('ruleForm')"
          >保存</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addTaskGroup,
  updateTaskGroup,
} from "@/api/dataSourceStructureMgt/collectionMgt";
import getCodeValueContent from "@/mixins/getCodeValueContent";

export default {
  mixins: [getCodeValueContent],
  data() {
    return {
      dialogFormVisible: false, // 弹框状态
      formLabelWidth: "100px",
      isShowLoading: false, // 是否显示loading
      formData: {
        // 表单数据
        jobName: "",
        jobDesc: "",
        jobExeType: "1", // 执行方式默认自动执行
        chkMonth: "",
        chkType: "",
        dataSourceId: "",
        dataSourceName: "",
        dayOfMonth: "",
        dayOfWeek: "",
        dayOfWeek2: "",
        jobEndDate: "",
        jobExeRate: "1", // 执行频次
        jobSpan: "", //暂时去掉
        jobStartDate: "",
        jobStartTime: "",
        rosterType: "",
        taskMetamodelType: "",
        weekTime: "",
      },
      resetFormData: {
        chkMonth: "",
        chkType: "",
        dataSourceId: "",
        dataSourceName: "",
        dayOfMonth: "",
        dayOfWeek: "",
        dayOfWeek2: "",
        jobEndDate: "",
        jobExeRate: "", // 执行频次
        jobSpan: "", //暂时去掉
        jobStartDate: "",
        jobStartTime: "",
        rosterType: "",
        taskMetamodelType: "",
        weekTime: "",
      },
      rules: {
        jobName: [
          { required: true, message: "请输入任务名称", trigger: "blur" },
        ],
        jobExeType: [
          { required: true, message: "请选择执行方式", trigger: "change" },
        ],
        jobExeRate: [
          { required: true, message: "请选择执行频率", trigger: "change" },
        ],
        jobStartDate: [
          { required: true, message: "请选择开始日期", trigger: "change" },
        ],
        jobEndDate: [
          { required: true, message: "请选择结束日期", trigger: "change" },
        ],
      },
      formDataCopy: {},
      weekTimeOptions: [
        {
          value: "1",
          label: "第一周",
        },
        {
          value: "2",
          label: "第二周",
        },
        {
          value: "3",
          label: "第三周",
        },
        {
          value: "4",
          label: "第四周",
        },
      ],
      weekData: [
        {
          value: "2",
          label: "星期一",
        },
        {
          value: "3",
          label: "星期二",
        },
        {
          value: "4",
          label: "星期三",
        },
        {
          value: "5",
          label: "星期四",
        },
        {
          value: "6",
          label: "星期五",
        },
        {
          value: "7",
          label: "星期六",
        },
        {
          value: "1",
          label: "星期日",
        },
      ],
      weekCheckList: [], // 周执行星期复选框数据
      monthCheckList: [], // 月执行月份复选框数据
      jobExeTypeData: [],
      dayOfMonthValue: "",
      isDisable1: true,
      isDisable2: true,
      isDisable3: true,
    };
  },
  props: {
    row: {
      type: Object,
    },
    btnType: {
      type: Number,
    },
  },
  watch: {
    row: {
      handler(newVal) {
        this.dayOfMonthValue = newVal.dayOfMonth;
        // 转换月执行月份数据渲染至复选框
        if (newVal.chkMonth) {
          this.monthCheckList = newVal.chkMonth.split(",");
        }
        // 转换周执行星期复选框数据渲染至复选框
        if (newVal.dayOfWeek) {
          this.weekCheckList = newVal.dayOfWeek.split(",");
        }
      },
      immediate: true,
    },
    // 监听周执行星期复选框数据
    weekCheckList: {
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          // 重置的情况
          this.formData.dayOfWeek = "";
        } else {
          this.formData.dayOfWeek = newVal.join(); // 转化成字符串
        }
      },
      deep: true,
      immediate: true,
    },
    // 监听月执行的月份复选框数据
    monthCheckList: {
      handler(newVal, oldVal) {
        if (newVal.length === 0) {
          // 重置的情况
          this.formData.chkMonth = "";
        } else {
          this.formData.chkMonth = newVal.join(); // 转化成字符串
        }
      },
      deep: true,
      immediate: true,
    },
    // 监听月执行的日期 日期选项数据
    dayOfMonthValue: {
      handler(newVal, oldVal) {
        this.formData.dayOfMonth = newVal;
      },
      immediate: true,
    },
  },
  methods: {
    // 处理dialog打开时
    handlerOpen() {
      this.formDataCopy = { ...this.formData };
      if (this.btnType === 0) {
        this.formData = this.row;
      }
      // 获取执行方式数据
      this.getCodeValueContent("jobExeTypeData", "jobExeType");
    },
    // 处理dialog关闭后
    handlerClose() {
      this.formData = this.formDataCopy;
      this.$refs["ruleForm"].resetFields();
    },
    handlerjobExeTypeSelect(val) {
      if (val === "0") {
        this.formData = { ...this.formData, ...this.resetFormData };
      } else {
        this.formData.jobExeRate = "1";
      }
    },
    // 保存
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.btnType) {
            this.addTaskGroup();
            return;
          }
          this.updateTaskGroup();
        } else {
          return false;
        }
      });
    },
    // 执行频次选项框发生变化时重置部分表单
    handlerSelectChange() {
      this.formData.jobSpan = "";
      this.formData.dayOfWeek = "";
      this.formData.chkMonth = "";
      this.formData.dayOfMonth = "";
      this.formData.chkType = "";
      this.formData.dayOfWeek2 = "";
      this.formData.weekTime = "";
      this.formData.jobSpan = "";
      this.weekCheckList = [];
      this.monthCheckList = [];
    },
    // 处理月执行日期单选框改变时
    handlerRadioChange(e) {
      if (e === "2") {
        this.formData.dayOfMonth = "L";
        this.formData.weekTime = "";
        this.formData.dayOfWeek2 = "";
        this.dayOfMonthValue = "";
        this.isDisable1 = true;
        this.isDisable2 = true;
        this.isDisable3 = true;
      } else if (e === "1") {
        this.formData.dayOfMonth = "";
        this.formData.weekTime = "";
        this.formData.dayOfWeek2 = "";
        this.isDisable1 = false;
        this.isDisable2 = true;
        this.isDisable3 = true;
      } else {
        this.formData.dayOfMonth = "";
        this.dayOfMonthValue = "";
        this.isDisable1 = true;
        this.isDisable2 = false;
        this.isDisable3 = false;
      }
    },
    // 新增任务组
    addTaskGroup() {
      addTaskGroup(this.formData).then((res) => {
        if (res.status !== 0) {
          this.$message({
            message: res.msg,
            type: "error",
          });
          return;
        }
        this.$message({
          message: "新增任务组成功!",
          type: "success",
        });
        this.$emit("queryTaskGroupList");
        this.dialogFormVisible = false;
      });
    },
    // 编辑任务组
    updateTaskGroup() {
      updateTaskGroup(this.formData).then((res) => {
        if (res.status !== 0) {
          this.$message({
            message: res.msg,
            type: "error",
          });
          return;
        }
        this.$message({
          message: "更新任务组成功!",
          type: "success",
        });
        this.$emit("queryTaskGroupList");
        this.dialogFormVisible = false;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.add-task-group {
  display: inline-block;
  margin-right: 10px;
  .mgt-dialog-upload {
    margin-left: 50px;
  }
  .dialog-footer {
    position: relative;
    .test-data-source {
      position: absolute;
      top: 0;
      bottom: 0;
    }
  }
}
.el-form {
  margin-right: 20px;
}
.el-select {
  display: block;
}
</style>
