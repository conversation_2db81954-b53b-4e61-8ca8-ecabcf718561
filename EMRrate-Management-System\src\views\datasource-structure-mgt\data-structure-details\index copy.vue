<template>
  <MainCard>
    <div class="data-structure-details">
      <div class="structure-details-header">
        <el-form :inline="true">
          <el-form-item label="数据源：">
            <el-select
              @change="getLeftTreeList"
              v-model="queryLeftTree.dataSourceId"
              placeholder="请选择">
              <el-option
                v-for="item in dataSourceOptions"
                :key="item.dataSourceId"
                :label="item.dataSourceName"
                :value="item.dataSourceId">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <span class="tips"
              ><i class="el-icon-back"></i
              >选择数据源，查看数据库的表、视图、字段信息</span
            >
          </el-form-item>
        </el-form>
      </div>
      <div class="structure-details-main">
        <div class="tree-list-left card-header">
          <TreeCard
            :title="sourceCardTitle"
            widthSize="360px">
            <el-tree
              :data="treeData"
              default-expand-all
              node-key="id"
              ref="tree"
              highlight-current
              :props="defaultProps"
              @node-click="handleClick">
              <template #default="props">
                <span>
                  <!-- 收缩和折叠的图标 -->
                  <img
                    class="icon fold"
                    v-if="props.node.data.children"
                    :src="
                      props.node.expanded
                        ? treeIcons.tree_shouqi
                        : treeIcons.tree_zhankai
                    "
                    alt="" />
                  <!-- 每个层级的图表 -->
                  <img
                    class="icon"
                    :src="getIconClass(props.node)"
                    alt="" />
                  <span
                    class="tree-itemLabel"
                    style="margin-left: 6px"
                    >{{ getLabelName(props.node) }}</span
                  >
                </span>
              </template>
            </el-tree>
          </TreeCard>
        </div>
        <div class="table-right card-header">
          <TreeCard :title="tableOrViewTitle">
            <el-table
              :data="tableData"
              v-loading="loading"
              :header-cell-style="{ background: '#fff', color: '#606266' }">
              <el-table-column
                prop="tableName"
                label="表名"></el-table-column>
              <el-table-column
                prop="name"
                width="100"
                label="字段名称">
              </el-table-column>
              <el-table-column
                prop="remarks"
                label="字段注释"></el-table-column>
              <el-table-column
                prop="type"
                label="字段类型"></el-table-column>
              <el-table-column
                prop="length"
                label="字段长度"></el-table-column>
              <el-table-column
                prop="scale"
                label="精度"></el-table-column>
              <el-table-column
                prop="defaultvalue"
                label="默认值">
                <template slot-scope="scope">
                  <span v-if="scope.row.defaultvalue === null">null</span>
                  <span v-else>{{ scope.row.defaultvalue }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="nullable"
                label="是否为空">
                <template slot-scope="scope">
                  <span v-if="scope.row.nullable === 'false'">否</span>
                  <span v-else-if="scope.row.nullable === 'true'">是</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="pk"
                label="是否主键">
                <template slot-scope="scope">
                  <span v-if="scope.row.pk === 'false'">否</span>
                  <span v-else-if="scope.row.pk === 'true'">是</span>
                </template>
              </el-table-column>
            </el-table>
            <div class="structure-details-pag">
              <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="queryData.pageNum"
                :page-sizes="[5, 10, 15, 20]"
                :page-size="queryData.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalNum">
              </el-pagination>
            </div>
          </TreeCard>
        </div>
      </div>
    </div>
  </MainCard>
</template>

<script>
import { queryDataSourceList } from '@/api/dataSourceStructureMgt/dataSourceMgt'
import ArrowAnimation from '@/components/ArrowAnimation'
import TreeCard from './TreeCard'
import {
  getLeftTree,
  getRightStructures
} from '@/api/dataSourceStructureMgt/dataStructureDetails'

export default {
  components: {
    ArrowAnimation,
    TreeCard
  },
  data() {
    return {
      queryData: {
        // 查询数据
        pageNum: 1,
        pageSize: 10
      },
      treeIcons: {
        tree_data_sourse: require('@/assets/tree/tree_data_sourse.png'),
        tree_folder: require('@/assets/tree/tree_folder.png'),
        tree_shouqi: require('@/assets/tree/tree_shouqi.png'),
        tree_table: require('@/assets/tree/tree_table.png'),
        tree_view: require('@/assets/tree/tree_view.png'),
        tree_zhankai: require('@/assets/tree/tree_zhankai.png')
      },
      tree_card_header: require('@/assets/tree/tree_card_header.png'),
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
        disabled: function (data) {
          if (data.children) {
            return true
          } else {
            return false
          }
        }
      },
      querySelectData: {}, // 查询所有数据源
      queryLeftTree: {
        // 查询左侧树数据
        dataSourceId: ''
      },
      queryRightTable: {
        // 查询右侧表数据
        dataSourceId: '',
        databaseName: '',
        name: '',
        pageNum: 1,
        pageSize: 10,
        schema: ''
      },
      dataSourceOptions: [], // 数据源下拉框数据
      leftTreeData: [],
      tableData: [],
      totalNum: 1,
      isleftTreeData: false,
      loading: false,
      isLoading: false,
      sourceCardTitle: '',
      tableOrViewTitle: ''
    }
  },
  created() {
    // 初始化数据源下拉框
    this.queryDataSourceList()
  },
  methods: {
    // 处理各级别目录图标
    getIconClass(node) {
      // 数据源目录图标
      if (node.level == 1) {
        return this.treeIcons.tree_data_sourse
      }
      // 模式、表、视图目录图标
      if (node.level == 2 || node.level == 3) {
        return this.treeIcons.tree_folder
      }
      // 表内容
      if (node.level == 4 || node.parent.label === 'TABLE') {
        return this.treeIcons.tree_table
      }
      // 视图内容
      if (node.level == 4 || node.parent.label === 'VIEW') {
        return this.treeIcons.tree_table
      }
    },
    getLabelName(node) {
      if (node.level == 2) {
        return '模式'
      }
      if (node.label === 'TABLE') {
        return '表'
      }
      if (node.label === 'VIEW') {
        return '视图'
      }
      return node.label
    },
    // 查询数据源列表
    queryDataSourceList() {
      queryDataSourceList(this.querySelectData).then((res) => {
        if (res.status === 0) {
          this.dataSourceOptions = res.data.list
          if (this.$route.params.hasOwnProperty('dataSourceId')) {
            // 数据源页面跳转而来
            this.queryLeftTree.dataSourceId = Number(
              this.$route.params.dataSourceId
            )
            this.getLeftTreeList(this.queryLeftTree.dataSourceId)
          }
        }
      })
    },
    // 获取左侧树列表数据
    getLeftTreeList(val) {
      this.sourceCardTitle = this.dataSourceOptions.filter(
        (item) => item.dataSourceId === val
      )[0].dataSourceName
      this.isLoading = true
      this.treeData = [] // 清空左侧树数据
      this.tableData = [] // 将表格数据清空
      getLeftTree(this.queryLeftTree)
        .then((res) => {
          if (res.status !== 0) {
            this.$message({
              message: res.msg,
              type: 'error'
            })
            this.isLoading = false
            return
          }
          const addWhether = (arr) =>
            arr.map((item) => ({
              ...item,
              whether: item.children ? false : true,
              children: item.children ? addWhether(item.children) : null
            }))
          this.treeData = addWhether(res.data)
          this.queryRightTable.dataSourceId = val // val为数据源ID
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    // 点击树形结构 表 或 view
    handleClick(data1, data2, data3) {
      if (data1.whether) {
        this.tableOrViewTitle = data1.label
        this.queryRightTable.databaseName =
          data2.parent.parent.parent.data.label
        this.queryRightTable.name = data1.label
        this.queryRightTable.schema = data2.parent.parent.data.label
        this.getRightTableData()
      }
    },
    // 获取右侧表数据
    getRightTableData() {
      this.loading = true
      getRightStructures(this.queryRightTable).then((res) => {
        if (res.status === 0) {
          this.tableData = res.data.list
          this.totalNum = res.data.total
          this.loading = false
        } else {
          this.loading = false
          this.$message({
            message: res.msg,
            type: 'error'
          })
        }
      })
    },
    // 改变页面显示条数
    handleSizeChange(val) {
      this.queryRightTable.pageSize = val
      this.getRightTableData()
    },
    // 改变页数
    handleCurrentChange(val) {
      this.queryRightTable.pageNum = val
      this.getRightTableData()
    }
  }
}
</script>

<style scoped lang="scss">
.data-structure-details {
  .structure-details-header {
    margin: 10px 0 60px 0;
    border-bottom: 1px solid #dcdfe6;
    .tips {
      color: #888;
      margin-left: 10px;
      .el-icon-back {
        margin-right: 4px;
      }
    }
  }
  .structure-details-main {
    display: flex;
    margin-top: 10px;
    .tree-list-left {
      .el-tree {
        font-size: 14px;
        min-width: 100%;
        display: inline-block !important;
      }

      .fold {
        width: 18px;
        margin-left: -2px;
      }
      .icon {
        margin-right: 6px;
        vertical-align: middle;
      }
      ::v-deep.el-tree-node__expand-icon {
        color: transparent;
      }
    }
    .table-right {
      width: 64vw;
      margin-left: 20px;
    }
    .structure-details-pag {
      margin-top: 10px;
    }
    .card-header {
      position: relative;
      .gimg {
        position: absolute;
        top: -33px;
        left: 0;
      }
    }
  }
}
.el-card {
  border: 1.6px solid #e7eaec;
}
::v-deep .el-tree-node__label {
  font-size: 12px;
  display: block;
}
/* ::v-deep.el-tree--highlight-current
  .el-tree-node.is-current
  > .el-tree-node__content {
  background-color: #3aa6dc;
  color: white;
  border-radius: 3px;
}  */
</style>
