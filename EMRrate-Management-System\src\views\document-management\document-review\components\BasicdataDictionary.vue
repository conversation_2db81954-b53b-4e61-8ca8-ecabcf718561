<template>
  <MainCard>
    <div class="source-mgt-main">
      <div class="emr-headline">
        <i></i>
        基础数据
        <span> （仅医疗服务基础数据）</span>
        <span
          class="aboutme"
          v-if="
            activeporject.personInCharge.includes($store.state.user.loginId)
          "
        >
          是否和我相关
          <el-switch
            @change="getBasedocumentExport"
            v-model="aboutme"
            active-color="#4969de"
            inactive-color="#aaa"
          >
          </el-switch>
        </span>
      </div>
      <div class="source-mgt-table">
        <el-table
          :data="tableData"
          style="width: 100%"
          row-key="id"
          ref="LoadTable"
          lazy
          v-loading="loading"
          :header-cell-style="{ background: '#fff', color: '#606266' }"
        >
          <el-table-column prop="directoryName" label="名称" min-width="200"
            ><template slot-scope="scope">
              <div
                :class="
                  scope.row.directoryCode.includes('.') ? 'right' : 'left'
                "
              >
                {{ scope.row.directoryCode }}.
                {{ scope.row.directoryName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="allNum" label="数量" width="100">
          </el-table-column>
          <el-table-column prop="dataUnit" label="单位" width="100">
          </el-table-column>
        </el-table>
      </div>
    </div>
  </MainCard>
</template>

<script>
import { getBasedocumentExport } from "@/api/document-management/document-review";
export default {
  data() {
    return {
      tableData: [], // 表格数据
      loading: false,
      aboutme: true,
    };
  },

  props: {
    exportRecordId: {
      type: String,
    },
    activeporject: {
      type: Object,
    },
  },
  created() {
    // 进入页面初始化查询
    this.getBasedocumentExport();
  },
  methods: {
    // 查询列表
    getBasedocumentExport() {
      this.loading = true;
      getBasedocumentExport({
        exportRecordId: this.exportRecordId,
        userAccount: this.aboutme ? this.$store.state.user.loginId : null,
      }).then((res) => {
        if (res.status === 0) {
          this.tableData = res.data;
          this.loading = false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .left {
  padding-left: 0px !important;
}
::v-deep .right {
  padding-left: 15px !important;
}
.main-card {
  margin-top: 20px;
}
.aboutme {
  color: #aaa;
  font-size: 14px;
}
</style>
