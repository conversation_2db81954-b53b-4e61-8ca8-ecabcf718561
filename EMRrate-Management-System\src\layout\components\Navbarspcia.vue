<template>
  <!-- 选择有项目时整个头部高度变化 -->
  <div
    :style="{
      height: activeproject.id ? '102px' : '58px',
    }"
  >
    <div class="navbar-container" size="small">
      <!-- 选择有项目时距离左边距变化 -->
      <div
        class="navbar-content"
        :style="{
          paddingLeft: activeproject.id ? '0px' : '26px',
        }"
      >
        <div class="second-level-nav" v-if="activeproject.id">
          <el-button
            type="text"
            icon="el-icon-back"
            size="medium"
            class="backpar"
            @click="backparmanagment(firstLevelTitle)"
          ></el-button>
          <el-divider direction="vertical"></el-divider>
          <svg-icon icon-class="icon_10" />
          <b>{{activeproject.levelName}}</b>
          <el-dropdown
            :hide-on-click="false"
            trigger="hover"
            @command="changeproject"
          >
            <span class="el-dropdown-link">
              {{ activeproject.projectName }}
              <i class="el-icon-caret-bottom el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="item in projectList"
                :key="item.id"
                :command="item.id"
              >
                {{ item.projectName }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <i class="el-icon-info icon-info" @click="dialogVisible = true"></i>
        </div>
        <div class="second-level-nav" v-if="!activeproject.id">
          <div class="title">
            <img class="title-img" :src="firstLevelImg" alt="" />
            <span>{{ firstLevelTitle }}</span>
          </div>
          <el-button
            type="text"
            icon="el-icon-plus"
            size="medium"
            @click="addproject(firstLevelTitle)"
          >
            添加项目
          </el-button>
        </div>

        <div class="tips-block">
          <el-popover
            placement="right"
            width="400"
            trigger="hover"
            v-show="
              $store.state.user.isDownload || $store.state.user.isDownloadmgt
            "
          >
            <div class="download-tex">
              {{
                isDownload ? "正在下载电子病历评级文档" : "正在下载证明材料文档"
              }}
              {{ exportRecordId }}
            </div>
            <el-progress :percentage="percentage"></el-progress>
            <el-progress
              type="circle"
              slot="reference"
              :stroke-width="4"
              :percentage="percentage"
              :width="40"
            ></el-progress>
          </el-popover>
          <div
            class="tips-block-download"
            v-show="$store.state.user.isDownload"
          ></div>
        </div>
      </div>
      <div class="userMgt">
        <UserMgt @startHandleCommand="(command) => this[command]()" />
      </div>
    </div>
    <!-- 选择有项目时菜单重新布局 ，并且隐藏项目管理菜单-->
    <div class="nav-list" v-if="activeproject.id">
      <div
        class="nav-item"
        :style="{
          fontWeight: curActiveSecondLevel === item.path ? '700' : '500',
        }"
        v-for="item in curMenuItemChild"
        :key="item.path"
        @click="handlerSecondLevelSkip(item)"
        v-show="item.meta.title !== '项目管理'"
      >
        {{ item.meta.title }}
        <div v-if="curActiveSecondLevel === item.path">
          <img class="active-img1" :src="navbar_active_01" alt="" />
          <img class="active-img2" :src="navbar_active_02" alt="" />
        </div>
      </div>
    </div>

    <el-dialog title="项目信息" :visible.sync="dialogVisible" width="540px">
      <el-form class="activeproject">
        <el-form-item label="项目名称：">
          <b> {{ activeproject.projectName }}</b>
        </el-form-item>
        <el-form-item label="评级等级:">
          <span class="activecolor">{{ activeproject.levelName }}</span>
        </el-form-item>
        <el-form-item label="项目进度:">
          <el-progress
            :percentage="Math.trunc(activeproject.progress * 100)"
            color="#38D695"
          ></el-progress>
        </el-form-item>
        <el-form-item label="创建信息:">
          <span class="activecolor">{{ activeproject.createBy }}</span>
          创建于{{ activeproject.createTime }}
        </el-form-item>
        <el-form-item :label="'项目成员'" v-if="activeproject.projectMembers">
          <span>({{ activeproject.memberCount }})</span>

          <div
            class="treeitem2"
            v-for="item in JSON.parse(activeproject.projectMembers)"
            :key="item.userAccount"
          >
            <div
              class="sex"
              :style="{
                background: item.gender === 'M' ? '#ad7ef4' : '#4EA5F8',
              }"
            >
              {{ item.userName.substr(item.userName.length - 1, 1) }}
            </div>
            <span class="name">{{ item.userName }}</span>
            <span v-show="item.isincharge">(负责人)</span>
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import Logo from "@/layout/components/Logo.vue";
import UpdatePassword from "@/components/UpdatePassword";
import UserMgt from "./UserMgt.vue";
import {
  exportProcess,
  downloadDoc,
} from "@/api/document-management/document-review";
import { exportProcessmgt, downloadDocmgt } from "@/api/empirical-material-mgt";
import { queryProject } from "@/api/document-management/project-management";
import moment from "moment";
import saveAs from "file-saver";
import navbar_active_01 from "@/assets/navbar/navbar_active_01.png";
import navbar_active_02 from "@/assets/navbar/navbar_active_02.png";
export default {
  name: "vvvv",
  components: { Logo, UpdatePassword, UserMgt },
  data() {
    return {
      timer: null,
      timermgt: null,
      navbar_active_01,
      navbar_active_02,
      dateTime: moment().format("YYYY-MM-DD HH:mm"),
      tags: [],
      currentTag: "首页",
      title: "首页",

      timershow: "",
      percentage: 0,
      exportRecordId: "",
      curActiveSecondLevel: "",
      projectList: [],
      dialogVisible: false,
    };
  },
  props: {
    curMenuItem: {
      type: Object,
      default: () => ({}),
    },
  },
  async created() {
    if (this.$route.params.id === undefined) {
      this.activeporject = JSON.parse(sessionStorage.getItem("projectactive"));
    } else {
      this.activeporject = this.$route.params;
    }
    await queryProject({
      projectType: this.firstLevelTitle === "数据质量文档" ? 0 : 1,
    }).then((res) => {
      if (res && "data" in res) {
        this.projectList = res.data;
      }
    });

    this.exportProcessmgt();
    this.exportProcess();
    // this.$store.commit("user/SET_ISDOWNLOAD", true);
    // this.$store.commit("user/SET_ISDOWNLOADMGT", true);
    this.timershow = setInterval(() => {
      this.dateTime = moment().format("YYYY-MM-DD HH:mm");
    }, 1000);
    const { meta, path, matched } = this.$route;
    if (meta.title !== "首页") {
      this.tags.push({ ...meta, path, closable: true });
      this.currentTag = meta.title;
    }
    this.title = matched[0].meta.title;
  },
  destroyed() {
    this.timer && clearInterval(this.timer);
    this.timermgt && clearInterval(this.timermgt);
  },
  computed: {
    isDownload() {
      return this.$store.state.user.isDownload;
    },
    isDownloadmgt() {
      return this.$store.state.user.isDownloadmgt;
    },
    curMenuItemChild() {
      return this.curMenuItem.children;
    },
    firstLevelTitle() {
      return this.curMenuItem.meta?.title;
    },
    firstLevelImg() {
      return this.curMenuItem.meta?.menuIcons?.navbarTitleIcon;
    },
    activeproject() {
      if (this.$route.params.id === undefined) {
        return JSON.parse(sessionStorage.getItem("projectactive"));
      } else {
        return this.$route.params;
      }
    },
  },
  methods: {
    removeTab(tagName) {
      if (tagName == "首页") return;
      this.tags = this.tags.filter((item, i) => {
        return item.title != tagName;
      });
      this.$router.push("/");
    },
    handlelink(tag) {
      const {
        $vnode: {
          data: {
            attrs: { path },
          },
        },
      } = tag;
      // console.log("tag", tag);
      this.$router.push(path);
    },
    exportProcessmgt() {
      exportProcessmgt().then((res) => {
        if (res.status !== 0) {
          this.$store.commit("user/SET_ISDOWNLOADMGT", false);
          clearInterval(this.timermgt);
          this.$message({
            type: "error",
            message: res.msg,
          });
        } else if (res.data.exportRecordId === null) {
          clearInterval(this.timermgt);
          this.$store.commit("user/SET_ISDOWNLOADMGT", false);
        } else {
          this.exportRecordId = res.data.exportRecordId;
          if (res.data.process === 1) {
            clearInterval(this.timermgt);
            this.$store.commit("user/SET_ISDOWNLOADMGT", false);
            downloadDocmgt({ exportRecordId: this.exportRecordId }).then(
              (res) => {
                const blob = new Blob([res]);
                saveAs(blob, `${decodeURIComponent(res.name)}`);
              }
            );
          } else if (res.data.process === -1) {
            clearInterval(this.timermgt);
            this.$store.commit("user/SET_ISDOWNLOADMGT", false);
            this.$message({
              type: "error",
              message: "导出失败",
            });
          } else {
            this.percentage = Number((res.data.process * 100).toFixed(0));
          }
        }
      });
    },

    exportProcess() {
      exportProcess().then((res) => {
        if (res.status !== 0) {
          this.$store.commit("user/SET_ISDOWNLOAD", false);
          clearInterval(this.timer);
          this.$message({
            type: "error",
            message: res.msg,
          });
        } else if (res.data.exportRecordId === null) {
          clearInterval(this.timer);
          this.$store.commit("user/SET_ISDOWNLOAD", false);
        } else {
          this.exportRecordId = res.data.exportRecordId;
          if (res.data.process === 1) {
            clearInterval(this.timer);
            this.$store.commit("user/SET_ISDOWNLOAD", false);
            downloadDoc({ exportRecordId: this.exportRecordId }).then((res) => {
              const blob = new Blob([res]);
              saveAs(blob, `${decodeURIComponent(res.name)}`);
            });
          } else if (res.data.process === -1) {
            clearInterval(this.timer);
            this.$store.commit("user/SET_ISDOWNLOAD", false);
            this.$message({
              type: "error",
              message: "导出失败",
            });
          } else {
            this.percentage = Number((res.data.process * 100).toFixed(0));
          }
        }
      });
    },
    handlerSecondLevelSkip(item) {
      this.curActiveSecondLevel = item.path;
      const fullPath = `${this.curMenuItem.path}/${item.path}`;
      // this.$router.push(fullPath);

      this.$router.push({
        name: item.name,
        params: { ...this.activeproject },
      });
    },
    // 点击返回按钮
    backparmanagment(backto) {
      if (backto === "实证材料管理") {
        this.$router.push("/empirical-material-mgt/project-management");
      } else if (backto === "数据质量文档") {
        this.$router.push("/document-management/project-management");
      }
    },
    // 改变项目
    changeproject(val) {
      if (this.activeproject.id !== val) {
        let paractive = this.projectList.filter(
          (value) => value["id"] === val
        )[0];

        this.$router.push({
          name: "redirect",
          query: { ...paractive, newname: this.$route.name },
        });
        // this.$router.push({
        //   name: this.$route.name,
        //   params: { ...paractive },
        // });
        // this.$store.commit("user/SET_PROJECTMSG", paractive);
      }
    },

    // 新增项目
    addproject(val) {
      if (val === "实证材料管理") {
        this.$store.commit("user/SET_ADDPROJECT", "实证材料管理");
      } else if (val === "数据质量文档") {
        this.$store.commit("user/SET_ADDPROJECT", "数据质量文档");
      }
    },
  },
  watch: {
    "$route.path": {
      handler(val) {
        if (!!val) {
          this.curActiveSecondLevel = val.split("/")[2];
        }
      },
      immediate: true,
    },
    isDownload: {
      handler(val) {
        if (val === true) {
          //监听到变化 去触发下一步操作
          let that = this;
          this.exportProcess();
          that.timer = setInterval(this.exportProcess, 5000);
        }
      },
    },
    isDownloadmgt: {
      handler(val) {
        if (val === true) {
          //监听到变化 去触发下一步操作
          let that = this;
          this.exportProcessmgt();
          that.timermgt = setInterval(this.exportProcessmgt, 5000);
        }
      },
    },
    activeproject: {
      handler(val) {
        if (val === true) {
          let that = this;
          that.activeproject = this.projectList.filter(
            (value) => value["id"] === this.activeproject.id
          )[0];
        }
      },
    },
    firstLevelTitle: {
      handler(val) {
        queryProject({
          projectType: this.firstLevelTitle === "数据质量文档" ? 0 : 1,
        }).then((res) => {
          if (res && "data" in res) {
            this.projectList = res.data;
          }
        });
      },
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-progress__text {
  font-size: 10px !important;
}
.navbar-container {
  height: 58px;
  background-color: #fff;
  position: relative;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.08);
  .navbar-content {
    position: absolute;
    transform: translateY(-50%);
    top: 50%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 0 26px;
    align-items: center;
    .second-level-nav {
      display: flex;
      align-items: center;
      white-space: nowrap;
      .title {
        font-size: 20px;
        font-weight: 600;
        color: #98a0b7;
        margin-right: 20px;
        .title-img {
          vertical-align: middle;
          margin-top: -4px;
          width: 28px;
          margin-right: 8px;
        }
      }
    }
    .tips-block {
      margin-right: 200px;
      display: flex;
      align-items: center;
      .avatar {
        margin-right: 9px;
        cursor: pointer;
      }
      .icon {
        cursor: pointer;
      }
      .logout {
        padding: 0 10px;
        margin-left: 10px;
        border-left: 1px solid #c7cadd;
        cursor: pointer;
        font-size: 30px;
      }
    }
  }

  .tips-block-download {
    position: absolute;
    top: 30px;
    right: 10px;
    font-size: 14px;
    display: flex;
    // .el-progress {
    //   width: 200px;
    // }
    // .download-tex {
    //   width: 70px;
    // }
  }

  .titlePic {
    font-size: 32px;
    background-position: right -695px top -10px;
    height: 105px;
    margin-top: 20px;
    margin-left: -40px;
    font-weight: 700;
    color: #333;
    // width: 260px;
  }
  /* .title {
    left: 40px;
    bottom: 0px;
    position: absolute;
    font-size: 28px;
    color: #1b88c0;
    font-weight: 600;
    line-height: 50px;
    .bottom-line {
      border-top: 5px solid #6bcfc0;
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;
    }
  } */

  .tabs {
    width: 100%; //解决欣姐电脑上:hover 鬼畜问题
    position: absolute;
    bottom: 0px;
    left: 340px;
    & > :first-child {
      margin-bottom: 0px;
    }
  }
  .userMgt {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
  }
}

.nav-list {
  display: flex;
  // margin-left: 64px;
  justify-content: center;
  line-height: 34px;
  .nav-item {
    padding: 4px 18px;
    font-size: 16px;
    cursor: pointer;
    position: relative;
    .active-img1 {
      position: absolute;
      width: 98px;
      height: 4px;
      left: 50%;
      top: 0px;
      z-index: 1000;
      transform: translateX(-50%);
    }
    .active-img2 {
      position: absolute;
      left: 50%;
      top: 36px;
      z-index: 1000;
      transform: translateX(-50%);
    }
    &:hover {
      font-weight: 700 !important;
    }
  }
  // .nav-item + .nav-item + .nav-item {
  //   border-left: 2px solid #e5e7e8;
  // }
}
.el-button {
  font-weight: 1000;
}
.backpar {
  font-size: 20px;
  // border-right: 2px solid #e5e7e8;
  border-radius: 0px;
  // height: 20px;
  // line-height: 20px;
}
b {
  color: #4969de;
  font-size: 15px;
}
.el-dropdown {
  color: #000;
  font-size: 15px;
}
.icon-info {
  cursor: pointer;
  color: #aeaeae;
}
::v-deep .el-dialog .el-dialog__header {
  font-weight: 600 !important;
}
.activeproject {
  b {
    color: #000;
  }
  .el-progress {
    width: 200px;
    display: flex;
    margin-top: 10px;
    align-items: center;
  }
}
.activecolor {
  color: #5270dd;
}

.treeitem2 {
  margin-top: 10px;
  line-height: 40px;
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0px 15px;
  .sex {
    background: #ad7ef4;
    width: 26px;
    height: 26px;
    line-height: 26px;
    text-align: center;
    border-radius: 50%;
    color: #ffffff;
    margin-right: 10px;
  }
  .name {
    font-size: 15px;
    color: #000000;
  }
}
</style>
