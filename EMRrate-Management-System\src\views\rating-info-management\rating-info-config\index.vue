<template>
  <MainCard>
    <div class="app-container">
      <div class="table-container">
        <HeaderSearch>
          <template v-slot:left>
            <el-button
              type="primary"
              @click="handleCreate"
              >新增评级信息</el-button
            >
          </template>
          <template v-slot:right>
            <el-form
              inline
              size="small">
              <el-form-item label="信息类型">
                <el-select
                  style="width: 180px"
                  v-model="formInline.msgType"
                  clearable
                  placeholder="请选择">
                  <el-option value="行业新闻"></el-option>
                  <el-option value="行业标准"></el-option>
                  <el-option value="政策文件"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="信息标题">
                <el-input
                  style="width: 140px"
                  v-model="formInline.msgTitle" />
              </el-form-item>
              <el-form-item label="信息内容">
                <el-input
                  style="width: 140px"
                  v-model="formInline.msgContent" />
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  @click="getAllMsgList"
                  icon="el-icon-search"
                  >搜索</el-button
                >

                <el-button @click="formInline = {}">重置</el-button>
              </el-form-item>
            </el-form>
          </template>
        </HeaderSearch>
        <el-table
          :data="tableData"
          ref="multipleTable"
          tooltip-effect="dark"
          v-loading="loading"
          :header-cell-style="{ background: '#fff', color: '#606266' }"
          size="mini">
          <el-table-column
            type="index"
            label="序号"
            width="50">
          </el-table-column>
          <el-table-column
            prop="msgType"
            width="100"
            label="信息类型">
          </el-table-column>
          <el-table-column
            prop="msgTitle"
            width="500"
            label="信息标题"
            :show-overflow-tooltip="true">
          </el-table-column>
          <el-table-column
            prop="msgContent"
            label="信息内容"></el-table-column>
          <el-table-column
            prop="action"
            label="操作"
            width="120">
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="handleEdit('edit', scope.row)"
                >编辑</el-button
              >
              <el-divider
                style="margin: 0 2px"
                direction="vertical"></el-divider>
              <el-button
                type="text"
                @click="deleteMsg(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div
          class="rating-info-pag"
          style="margin-top: 20px">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.size"
            :total="pagination.total"
            :current-page="pagination.current"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            @prev-click="handleCurrentChange"
            @next-click="handleCurrentChange">
          </el-pagination>
        </div>
      </div>
      <div class="msgModal">
        <el-dialog
          v-dialogDrag
          :title="flag == 'create' ? '新建' : '编辑'"
          :visible="flag == 'create' || flag == 'edit' || flag == 'view'"
          width="600px"
          @close="closeCreateModal"
          :close-on-click-modal="false"
          top="50px">
          <el-form
            :model="createForm"
            size="small"
            label-width="80px"
            :disabled="flag == 'view'"
            class="modalContainer"
            ref="createFormRef">
            <el-form-item
              label="信息类型"
              style="margin-bottom: 15px">
              <el-select
                v-model="createForm.msgType"
                placeholder="请选择">
                <el-option value="行业新闻"></el-option>
                <el-option value="行业标准"></el-option>
                <el-option value="政策文件"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="信息标题"
              style="margin-bottom: 15px">
              <el-input v-model="createForm.msgTitle" />
            </el-form-item>
            <el-form-item label="信息内容">
              <div class="textarea">
                <el-input
                  :autosize="{ minRows: 2, maxRows: 6 }"
                  type="textarea"
                  v-model="createForm.msgContent" />
              </div>
            </el-form-item>
            <el-form-item
              label="附件上传"
              style="margin-bottom: 15px">
              <el-upload
                class="upload-demo"
                ref="upload"
                action=""
                :on-preview="handlePreview"
                :on-change="fileChange"
                :before-remove="handlerDeleteListItem"
                multiple
                :limit="10"
                :file-list="fileList"
                :auto-upload="false">
                <el-button
                  size="small"
                  type="primary"
                  >选择文件</el-button
                >
                <div
                  slot="tip"
                  class="el-upload__tip">
                  支持上传多个附件，附件总大小不能超出100M。
                </div>
                <!-- <template v-slot:file="scope">
                  <div class="file">
                    <span class="name">{{ scope.file.name }}</span>
                    <span class="icon">
                      <i
                        v-if="
                          flag !== 'create' &&
                          scope.file.name.split('.')[1] !== 'zip' &&
                          scope.file.name.split('.')[1] !== 'rar' &&
                          scope.file.name.split('.')[1] !== 'tar'
                        "
                        @click.stop="handlerPreview(scope.file.name)"
                        class="el-icon-view el-icon--right"></i>
                      <i
                        @click.stop="handlerDeleteListItem(scope.file)"
                        class="el-icon-close"></i>
                    </span>
                  </div>
                </template> -->
              </el-upload>
              <span v-if="flag !== 'create' && fileList.length > 0"
                ><el-link
                  type="primary"
                  @click="downLoadMSG('all')"
                  >下载全部</el-link
                ></span
              >
            </el-form-item>
          </el-form>
          <div slot="footer">
            <el-button
              @click="handlerCancel"
              size="small"
              >取 消</el-button
            >
            <el-button
              v-if="flag !== 'view'"
              type="primary"
              @click="hanldeClick"
              size="mini"
              >确 定</el-button
            >
          </div>
        </el-dialog>
      </div>
    </div>
  </MainCard>
</template>

<script>
import {
  queryAllMsgList as _queryAllMsgList,
  addMsg as _addMsg,
  updateMsg as _updateMsg,
  messageDownload as _messageDownload,
  deleteMsg as _deleteMsg
} from '@/api/ratingInfoManagement/ratingInfoConfig'
import { queryAllSysConfig as _queryAllSysConfig } from '@/api/sys-config'
export default {
  created() {
    this.getAllMsgList()
  },
  computed: {
    recipientType() {
      return function (v) {
        let res
        switch (Number(v)) {
          case 0:
            res = '所有人'
            break
          case 1:
            res = '科室'
            break
          case 2:
            res = '用户'
            break
        }
        return res
      }
    }
  },
  data() {
    return {
      loading: false,
      formInline: {},
      createForm: {
        msgType: '',
        msgTitle: '',
        msgContent: ''
      },
      value: '',
      kkfileviewsAddr: '',
      //分页
      pagination: {
        current: 1,
        size: 10,
        total: null
      },
      tableData: [],
      msgId: '',
      row: '',
      flag: '',
      createUserList: [],
      createRoleList: [21],
      deptData: [],
      fileList: [],
      fileName: ''
    }
  },
  methods: {
    //获取信息列表
    getAllMsgList() {
      this.loading = true
      _queryAllMsgList({
        ...this.formInline,
        pageNum: this.pagination.current,
        pageSize: this.pagination.size
      }).then((res) => {
        if (res && 'data' in res) {
          this.tableData = res.data.list
          this.pagination.total = res.data.total
          this.loading = false
        }
      })
    },
    defaultCreateForm() {
      return { msgType: '', msgTitle: '', msgContent: '' }
    },
    //点击新建按钮
    handleCreate() {
      this.flag = 'create'
      this.fileList = []
    },
    downLoadMSG(item) {
      const link = document.createElement('a')
      if (item === 'all') {
        const data = {
          msgId: this.msgId
        }
        _messageDownload(data).then((v) => {
          if (v.type == 'application/json') {
            const reader = new FileReader() //创建一个FileReader实例
            reader.readAsText(v, 'utf-8') //读取文件,结果用字符串形式表示
            reader.onload = () => {
              const { msg } = JSON.parse(reader.result)
              this.$message.error(msg) //弹出错误提示
            }
          } else {
            link.style.display = 'none'
            const url = window.URL || window.webkitURL || window.moxURL
            link.href = url.createObjectURL(v)
            link.download = this.msgId //下载的文件名称
            link.click()
            window.URL.revokeObjectURL(url)
          }
        })
      } else {
        const data = {
          msgId: this.msgId,
          fileName: item
        }
        _messageDownload(data).then((v) => {
          if (v.type == 'application/json') {
            const reader = new FileReader() //创建一个FileReader实例
            reader.readAsText(v, 'utf-8') //读取文件,结果用字符串形式表示
            reader.onload = () => {
              const { msg } = JSON.parse(reader.result)
              this.$message.error(msg) //弹出错误提示
            }
          } else {
            let blob = new Blob([v]) //如果后台返回的直接是blob对象类型，直接获取数据
            let _fileName = decodeURIComponent(v.name) //拆解获取文件名，
            link.style.display = 'none'
            const url = window.URL || window.webkitURL || window.moxURL
            link.href = url.createObjectURL(blob)
            link.download = _fileName //下载的文件名称
            link.click()
            window.URL.revokeObjectURL(url)
          }
        })
      }
    },
    //点击编辑按钮
    handleEdit(flag, row) {
      this.flag = flag
      this.msgId = row.id
      // 处理显示文件
      const attachmentList = row.attachment ? row.attachment.split(',') : []
      this.fileList = attachmentList.map((item) => ({ name: item }))
      let line = JSON.parse(JSON.stringify(row))
      delete line.sendingTime
      this.createForm = line
    },
    // 删除
    deleteMsg(row) {
      this.$confirm('此操作将删除信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          _deleteMsg({ msgId: row.id }).then((res) => {
            if (res.status !== 0) {
              this.$message({
                message: res.msg,
                type: 'error'
              })
              return
            }
            this.$message({
              type: 'success',
              message: '删除成功'
            })
            this.getAllMsgList()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    //新建或者编辑信息
    async hanldeClick() {
      this.fileData = new FormData()
      if (this.flag == 'create') {
        const data = {
          ...this.createForm
        }
        Object.keys(data).forEach((key) => {
          const value = data[key]
          if (Array.isArray(value)) {
            value.forEach((subValue, i) =>
              this.fileData.append(key + `[${i}]`, subValue)
            )
          } else {
            this.fileData.append(key, data[key])
          }
        })
        let { uploadFiles } = this.$refs.upload
        let totalSize = 0
        uploadFiles.forEach((item) => {
          this.fileData.append('files', item.raw)
          totalSize += item.size
        })

        const isLimit = totalSize / 1024 / 1024 < 100
        if (isLimit) {
          await _addMsg(this.fileData).then((res) => {
            if (res && res.msg == 'success') {
              this.$message.success('新增信息成功!')
              this.flag = ''
              this.getAllMsgList()
            } else {
              this.$message.error(res.msg)
            }
          })
        } else {
          this.$message.error(`文件超出100M范围，请重新上传`)
        }
      } else {
        const data = {
          ...this.row,
          ...this.createForm
        }
        Object.keys(data).forEach((key) => {
          const value = data[key]
          if (Array.isArray(value)) {
            value.forEach((subValue, i) =>
              this.fileData.append(key + `[${i}]`, subValue)
            )
          } else {
            this.fileData.append(key, data[key])
          }
        })
        let { uploadFiles } = this.$refs.upload
        let totalSize = 0
        uploadFiles.forEach((item) => {
          if (item.raw) {
            this.fileData.append('files', item.raw)
            totalSize += item.size
          }
        })
        const isLimit = totalSize / 1024 / 1024 < 100
        if (isLimit) {
          await _updateMsg(this.fileData).then((res) => {
            if (res && res.msg == 'success') {
              this.$message.success('编辑信息成功!')
              this.flag = ''
              this.getAllMsgList()
            } else {
              this.$message.error(res.msg)
            }
          })
        } else {
          this.$message.error(`文件超出100M范围，请重新上传`)
        }
      }
    },
    handlerCancel() {
      this.flag = ''
    },
    closeCreateModal() {
      this.flag = ''
      this.createForm = this.defaultCreateForm()
    },
    handleSizeChange(size) {
      this.pagination.size = size
      this.getAllMsgList()
    },
    handleCurrentChange(current) {
      this.pagination.current = current
      this.getAllMsgList()
    },
    handlePreview(file) {
      this.downLoadMSG(file.name)
    },
    handlerDeleteListItem(file) {
      return this.$confirm(`确定移除 ${file.name}？`).then(() => {
        this.fileList = this.fileList.filter((item) => item.name !== file.name)
      })
    },
    fileChange(file, fileList) {
      console.log(file)
      let existFile = fileList
        .slice(0, fileList.length - 1)
        .find((f) => f.name === file.name) //如果文件名重复
      if (existFile) {
        this.$message.error('当前文件已经存在!')
        fileList.pop()
        return
      }

      if (!this.fileList.some((item) => item.name == file.name)) {
        this.fileList.push(file)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.table-container {
  position: relative;
  width: 100%;
  padding: 10px 10px;
  background-color: white;
  .authorityButtonContainer {
    position: absolute;
    right: 10px;
    top: 15px;
    display: flex;
    justify-content: space-between;
  }
}
.modalContainer {
  .treeContainer {
    height: 400px;
    overflow: auto;
  }
}
.el-upload__tip {
  display: inline;
  margin-left: 20px;
}
.file {
  .icon {
    .el-icon-view {
      &:hover {
        color: #409eff;
      }
    }
  }
}
.name {
  margin-left: 10px;
}
.iframe-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%;
  overflow: auto;
  iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}
</style>
