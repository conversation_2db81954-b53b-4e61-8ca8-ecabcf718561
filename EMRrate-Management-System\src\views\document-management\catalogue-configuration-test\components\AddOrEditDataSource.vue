<template>
  <div class="add-edit-data-source">
    <el-dialog
      v-dialogDrag
      :title="row.directoryName ? '上级目录>>' + row.directoryName : '新增一级'"
      :visible.sync="dialogFormVisible"
      @open="handlerOpen"
      @closed="handlerClose"
      :close-on-click-modal="false"
      width="30%"
    >
      <el-form
        :model="formData"
        :rules="rules"
        :label-width="formLabelWidth"
        ref="ruleForm"
      >
       
        <el-form-item label="目录编码" prop="directoryCode">
          <el-input
            v-model="formData.directoryCode"
            @input="autoadd(formData.directoryCode)"
          ></el-input>
        </el-form-item>
         <el-form-item label="目录名称" prop="directoryName">
          <el-input v-model="formData.directoryName"></el-input>
        </el-form-item>
        <el-form-item
          label="规则类型"
          prop="emrRuleType"
          v-if="formData.levelCode >= 3"
        >
          <el-radio
            v-model="formData.emrRuleType"
            v-for="item in emrRuleTypeData"
            :key="item"
            :label="item"
            >{{ item }}</el-radio
          >
        </el-form-item>
        <el-form-item label="目录等级" prop="levelCode">
          <el-select
            v-model="formData.levelCode"
            :disabled="formData.levelCode < 3"
          >
            <el-option
              v-for="item in levelCodeData"
              :key="item.levelName"
              :label="item.levelName"
              :value="item.levelCode"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm('ruleForm')"
          >保存</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { adddocumentDirectoryConfiguration } from "@/api/document-management/catalogue-configuration";
import getCodeValueConten from "@/mixins/getCodeValueContent";
export default {
  mixins: [getCodeValueConten],
  data() {
    return {
      dialogFormVisible: false, // 弹框状态
      formLabelWidth: "120px",
      formData: {
        // 表单数据
        directoryName: "",
        directoryCode: "",
        emrRuleType: "",
        levelCode: "",
      },
      rules: {
        directoryName: [
          { required: true, message: "请输入目录名称", trigger: "blur" },
        ],
        directoryCode: [
          { required: true, message: "请输入目录编码", trigger: "blur" },
        ],
        emrRuleType: [
          { required: true, message: "请选择规则类型", trigger: "blur" },
        ],
        levelCode: [
          { required: true, message: "请选择关联等级", trigger: "blur" },
        ],
      },
      emrRuleTypeData: ["一致性", "完整性", "整合性", "及时性"],
      isDisabled: false,
    };
  },
  props: {
    levelCodeData: {
      type: Array,
    },
    row: {
      type: Object,
    },
  },
  methods: {
    // 处理dialog打开时
    async handlerOpen() {
      this.formData = {
        // 表单数据
        directoryName: "",
        directoryCode: "",
        emrRuleType: "",
        levelCode: "",
      };
      if (this.row.directoryCode === undefined) {
        this.formData.levelCode = "1";
      } else if (this.row.directoryCode.length === 2) {
        this.formData.levelCode = "2";
      } else if (this.row.directoryCode.length === 5) {
        this.formData.levelCode = "3";
      }
      this.formData.directoryCode = this.row.directoryCode;
      this.isDisabled = true;
      this.isDisabled = false;
    },
    // 新增数据源
    adddocumentDirectoryConfiguration() {
      adddocumentDirectoryConfiguration(this.formData).then((res) => {
        if (res.status !== 0) {
          this.$message({
            message: res.msg,
            type: "error",
          });
          return;
        }
        this.$message({
          message: "新增目录成功",
          type: "success",
        });
        this.$parent.querydocumentDirectoryConfiguration();
        this.handlerClose();
      });
    },
    // 校验
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.adddocumentDirectoryConfiguration();
        } else {
          return false;
        }
      });
    },
    // 处理dialog关闭后
    handlerClose() {
      this.formData = {};
      this.dialogFormVisible = false;
    },
    // 自动填充等级
    autoadd(data) {
      console.log(data.length);
      if (data.length > 6) {
        let str = data.slice(6, 7);
        this.formData.levelCode = str;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.add-edit-data-source {
  display: inline-block;
  margin-right: 10px;
  .mgt-dialog-upload {
    margin-left: 50px;
  }
  .dialog-footer {
    position: relative;
    .test-data-source {
      position: absolute;
      top: 0;
      bottom: 0;
    }
  }
}
.el-form {
  margin-right: 10px;
}
::v-deep .el-dialog .el-dialog__body {
  // 设置dialog的固定高度
  min-height: 150px;
  overflow: auto;
}
</style>
