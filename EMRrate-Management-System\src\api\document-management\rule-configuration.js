import request from '@/utils/request'


// 查询左侧树形,可以指定文档等级
export function queryLeftTreedocumentRuleConfiguration(params) {
  return request({
    url: '/emr/documentRuleConfiguration/queryLeftTree',
    method: 'get',
    params
  })
}
// 查询左侧树形,可以指定文档等级
export function queryDirectoryTree(params) {
  return request({
    url: '/emr/rulePermissionConfiguration/queryDirectoryTree',
    method: 'get',
    params
  })
}



// 查询文档规则配置(目录名称+目录编码+关联类型)
export function querydocumentRuleConfiguration(data) {
  return request({
    url: '/emr/documentRuleConfiguration/query',
    method: 'post',
    data
  })
}

// 获取数据源  
export function datasourcelist(data) {
  return request({
    url: '/datasource/list',
    method: 'post',
    data
  })
}


//  获取系统-数据库-表-字段信息结构  
export function getStructureInfo(data) {
  return request({
    url: '/emr/documentRuleConfiguration/getStructures',
    method: 'post',
    data
  })
}



// 获取表字段  
export function getRightStructures(data) {
  return request({
    url: '/metadataStructure/getRightStructures',
    method: 'post',
    data
  })
}


// 获取时间条件SQL  
export function getTimeConditionSql(params) {
  return request({
    url: '/emr/documentRuleConfiguration/getTimeConditionSql',
    method: 'get',
    params
  })
}



// 执行SQL  
export function execute(data) {
  return request({
    url: '/emr/sql/execute',
    method: 'post',
    data
  })
}

// 保存文档规则配置
export function savedocumentRuleConfiguration(data) {
  return request({
    url: '/emr/documentRuleConfiguration/save',
    method: 'post',
    data
  })
}


// 执行当前页所有SQL
export function executeOneRule(data) {
  return request({
    url: '/emr/sql/executeOneRule',
    method: 'post',
    data
  })
}
// **************************************************

// 获取SQL执行状态
export function asyncExecuteOneRule(data) {
  return request({
    url: '/emr/sql/asyncExecuteOneRule',
    method: 'post',
    data
  })
}
// 异步执行一个规则的所有SQL
export function getSqlExecStatus(data) {
  return request({
    url: '/emr/sql/getSqlExecStatus',
    method: 'post',
    data
  })
}
// 获取SQL执行结果
export function getSqlExecResult(data) {
  return request({
    url: '/emr/sql/getSqlExecResult',
    method: 'post',
    data
  })
}


// **************************************************


// 根据用户账号查询负责的规则  
export function getRulePermission(params) {
  return request({
    url: '/emr/rulePermissionConfiguration/getRulePermission',
    method: 'get',
    params
  })
}

// 保存规则权限配置(勾选多少传多少，不传父目录，只传最后一级目录)
export function saveRulePermission(data) {
  return request({
    url: '/emr/rulePermissionConfiguration/saveRulePermission',
    method: 'post',
    data
  })
}
// 完成按钮
export function markComplete(data) {
  return request({
    url: '/emr/documentRuleConfiguration/markComplete',
    method: 'post',
    data
  })
}
export function getMedicaRecordsOrQualityRule(params) {
  return request({
    url: '/emr/dataDictionaryDirectoryRuleConfiguration/getMedicaRecordsOrQualityRule',
    method: 'get',
    params
  })
}

