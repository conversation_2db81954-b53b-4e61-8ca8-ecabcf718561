<template>
  <MainCard>
    <div class="collection-task-container">
      <HeaderSearch>
        <template v-slot:left>
          <el-button
            type="primary"
            @click="handlerAddClick"
            >新增任务组</el-button
          >
        </template>
        <template v-slot:right>
          <el-form
            :model="queryData"
            ref="ruleForm"
            :inline="true">
            <el-form-item
              label="任务名称"
              prop="taskName">
              <el-input
                v-model="queryData.taskName"
                placeholder="请输入任务名称"></el-input>
            </el-form-item>
            <el-form-item
              label="数据源编码"
              prop="dataSourceId">
              <el-input
                v-model="queryData.dataSourceId"
                placeholder="请输入数据源编码"></el-input>
            </el-form-item>
            <el-form-item
              label="数据源名称"
              prop="dataSourceName">
              <el-input
                v-model="queryData.dataSourceName"
                placeholder="请输入数据源名称"></el-input>
            </el-form-item>
            <el-form-item
              label="执行方式"
              prop="jobExeType">
              <el-select
                v-model="queryData.jobExeType"
                placeholder="请选择执行方式">
                <el-option
                  label="全部"
                  value=""></el-option>
                <el-option
                  v-for="item in jobExeTypeData"
                  :key="item.id"
                  :label="item.contentValue"
                  :value="item.contentKey"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                @click="searchTaskGroups"
                icon="el-icon-search"
                >搜索</el-button
              >
            </el-form-item>
            <el-form-item>
              <el-button @click="resetForm('ruleForm')">重置</el-button>
            </el-form-item>
          </el-form>
        </template>
      </HeaderSearch>
      <div class="collection-task-main">
        <div class="collection-task-table">
          <el-table
            :data="tableData"
            ref="collectionTaskTable"
            style="width: 100%"
            v-loading="loading"
            :header-cell-style="{ background: '#fff', color: '#606266' }">
            <el-table-column
              width="180"
              prop="jobId"
              label="任务编号">
            </el-table-column>
            <el-table-column
              width="160"
              prop="jobName"
              label="任务名称">
            </el-table-column>
            <el-table-column
              width="400"
              prop="jobDesc"
              label="任务描述">
            </el-table-column>
            <el-table-column
              prop="jobExeType"
              label="执行方式">
              <template slot-scope="scope">
                <span
                  v-for="item in jobExeTypeData"
                  :key="item.contentKey">
                  <span v-if="scope.row.jobExeType == item.contentKey">
                    {{ item.contentValue }}
                  </span>
                </span>
              </template>
            </el-table-column>
            <el-table-column
              prop="jobExeRate"
              label="执行频率">
              <template slot-scope="scope">
                <span v-if="scope.row.jobExeRate === '1'">每分</span>
                <span v-else-if="scope.row.jobExeRate === '2'">每小时</span>
                <span v-else-if="scope.row.jobExeRate === '3'">每天</span>
                <span v-else-if="scope.row.jobExeRate === '4'">每周</span>
                <span v-else-if="scope.row.jobExeRate === '5'">每月</span>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              width="340">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="handleShowDialog(scope.$index, scope.row, 1)"
                  >配置子任务</el-button
                >
                <span style="margin: 0 2px">
                  <el-divider direction="vertical"></el-divider>
                </span>
                <el-button
                  size="mini"
                  type="text"
                  @click="handleShowDialog(scope.$index, scope.row, 2)"
                  >编辑</el-button
                >
                <span style="margin: 0 2px">
                  <el-divider direction="vertical"></el-divider>
                </span>
                <el-dropdown trigger="click">
                  <span
                    style="cursor: pointer"
                    class="el-dropdown-link">
                    更多操作<i class="el-icon-arrow-down el-icon--right"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      @click.native="performTask(scope.$index, scope.row)"
                      >立即执行</el-dropdown-item
                    >
                    <el-dropdown-item
                      @click.native="deleteTaskGroup(scope.$index, scope.row)"
                      >删除</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="collection-task-pag">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="queryData.pageNum"
            :page-sizes="[5, 10, 15, 20]"
            :page-size="queryData.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalNum">
          </el-pagination>
        </div>
      </div>
    </div>
    <!-- 新增/编辑数据源 -->
    <AddOrEditTaskGroup
      ref="addOrEditTaskGroup"
      :btnType="btnType"
      :row="row" 
      @queryTaskGroupList="queryTaskGroupList"/>
    <!-- 配置任务组 -->
    <ConfigureTaskGroup
      ref="configureTaskGroup"
      :row="row" />
    <!-- <div class="config">
          <el-button
            type="primary"
            @click="searchTaskGroups"
            icon="el-icon-edit-outline"
            >规则配置</el-button
          >
        </div> -->
    <!-- <div class="delete">
          <el-button
            type="danger"
            @click="deleteTaskGroups"
            icon="el-icon-delete"
            >删除任务组</el-button
          >
        </div> -->
  </MainCard>
</template>

<script>
import {
  queryTaskGroup,
  deleteTaskGroups,
  performTask
} from '@/api/dataSourceStructureMgt/collectionMgt'
import getCodeValueContent from '@/mixins/getCodeValueContent'
import AddOrEditTaskGroup from './components/AddOrEditTaskGroup.vue'
import ConfigureTaskGroup from './components/ConfigureTaskGroup.vue'
export default {
  components: {
    AddOrEditTaskGroup,
    ConfigureTaskGroup
  },
  mixins: [getCodeValueContent],
  data() {
    return {
      queryData: {
        // 查询数据
        dataSourceId: '', // 数据源编码
        dataSourceName: '', // 数据源名称
        jobExeType: '', // 执行方式
        pageNum: 1,
        pageSize: 10,
        taskName: '' // 任务名称
      },
      btnType: 1, // 1 代表新增 0 代表编辑
      tableData: [], // 表格数据
      totalNum: 1,
      loading: false,
      jobExeTypeData: [],
      env: process.env.NODE_ENV, // 环境变量
      taskGroupIds: [], // 需要删除的任务组的ID
      row: {} // 表格整行的数据
    }
  },
  created() {
    // 进入页面初始化查询
    this.queryTaskGroupList()
    // 获取执行方式数据
    this.getCodeValueContent('jobExeTypeData', 'jobExeType')
  },
  methods: {
    handlerAddClick() {
      this.btnType = 1
      this.$refs.addOrEditTaskGroup.dialogFormVisible = true
    },
    // 查询任务组列表
    queryTaskGroupList() {
      this.loading = true
      queryTaskGroup(this.queryData).then((res) => {
        if (res.status === 0) {
          this.tableData = res.data.list
          this.totalNum = res.data.total
          this.loading = false
        }
      })
    },
    // 搜索任务组
    searchTaskGroups() {
      this.queryTaskGroupList(this.queryData)
    },
    // 改变页面显示条数
    handleSizeChange(val) {
      this.queryData.pageSize = val
      this.queryTaskGroupList(this.queryData)
    },
    // 改变页数
    handleCurrentChange(val) {
      this.queryData.pageNum = val
      this.queryTaskGroupList(this.queryData)
    },
    // 重置
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.queryTaskGroupList()
    },
    // 删除任务组
    deleteTaskGroup(index, row) {
      if (row.jobId) {
        this.$confirm('此操作将删除数据源, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          deleteTaskGroups({ taskGroupId: row.jobId }).then((res) => {
            this.loading = true
            if (res.status === 0) {
              this.$message({
                message: '删除成功!',
                type: 'success'
              })
              this.queryTaskGroupList()
            } else {
              this.$message({
                message: res.msg,
                type: 'error'
              })
            }
          })
        })
      }
    },
    // 立即执行任务
    performTask(index, row) {
      performTask({ taskGroupId: row.jobId }).then((res) => {
        if (res.status === 0) {
          this.$message({
            type: 'success',
            message: '执行任务成功'
          })
        }
      })
    },
    // 打开编辑任务组/规则配置
    handleShowDialog(index, row, flag) {
      if (flag === 1) {
        // 点击了规则配置
        this.$refs.configureTaskGroup.dialogFormVisible = true
      } else {
        this.btnType = 0
        this.$refs.addOrEditTaskGroup.dialogFormVisible = true
      }
      this.row = JSON.parse(JSON.stringify(row))
    }
  }
}
</script>

<style scoped lang="scss">
.collection-task-container {
  .collection-task-main {
    .collection-task-dialog {
      .mgt-dialog-upload {
        margin-left: 50px;
      }
    }
    .collection-task-pag {
      margin-top: 10px;
    }
  }
  ::v-deep .el-dialog {
    // dialog的定位
    top: -60px;
    .el-dialog__body {
      // height: 74vh;
      overflow: auto;
    }
  }
}
</style>
