import request from '@/utils/request'
// 新增项目
export function addProject(data) {
    return request({
        url: '/emr/project/addProject',
        method: 'post',
        data
    })
}
// 删除等级字典
export function deleteProjectById(params) {
    return request({
        url: `/emr/project/deleteProjectById/${params}`,
        method: 'delete',
    })
}
// 查询项目
export function queryProject(data) {
    return request({
        url: '/emr/project/queryProject',
        method: 'post',
        data
    })
}
// 查询项目管理员
export function queryProjectAdmin(params) {
    return request({
        url: '/emr/project/queryProjectAdmin',
        method: 'get',
        params
    })
}
// 更新项目
export function updateProject(data) {
    return request({
        url: '/emr/project/updateProject',
        method: 'post',
        data
    })
}
// 查询等级字典
export function querylevelDictionary(data) {
    return request({
      url: '/emr/levelDictionary/queryAllow',
      method: 'post',
      data
    })
  }

  
