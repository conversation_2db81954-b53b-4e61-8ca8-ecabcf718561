<template>
  <div class="add-edit-data-source">
    <el-dialog
      v-dialogDrag
      :visible.sync="dialogFormVisible"
      @closed="handlerClose"
      @open="handlerOpen"
      :close-on-click-modal="false"
      width="840px"
    >
      <div style="margin: 10px">
        <el-date-picker
          v-model="dataval"
          size="mini"
          type="monthrange"
          align="center"
          unlink-panels
          range-separator="至"
          start-placeholder="创建时间"
          end-placeholder="结束日期"
          value-format="yyyy-MM"
          :clearable="false"
          @change="changeMonth"
        >
        </el-date-picker>
        <el-button
          type="primary"
          size="mini"
          style="margin: 10px"
          @click="executeOneRule()"
          >执行</el-button
        >
      </div>
      <el-table
        :data="tableData"
        ref="sourceMgtTable1"
        style="width: 100%"
        border
        v-loading="loading"
      >
        <el-table-column
          prop="requiredProject"
          label="要求项目 "
          min-width="100"
        >
        </el-table-column>
        <el-table-column
          prop="hospitalProject"
          label="医院项目"
          min-width="100"
        >
        </el-table-column>
        <el-table-column
          :label="headerName[0]"
          prop="recordsNum"
          min-width="110"
        >
        </el-table-column>

        <el-table-column
          :label="headerName[1]"
          prop="recordsNum"
          min-width="110"
        >
          <template slot-scope="scope">
            <el-popover placement="top-start" width="300" trigger="hover">
              <div v-html="tohtml(scope.row.recordsSql)"></div>
              <div class="celldiv" slot="reference">
                {{ scope.row.recordsSql }}
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column
          v-if="headerName[2]"
          :label="headerName[2]"
          prop="conditionalRecordsNum"
          min-width="120"
        >
        </el-table-column>
        <el-table-column
          v-if="headerName[3]"
          :label="headerName[3]"
          prop="conditionalRecordsNum"
          min-width="120"
        >
          <template slot-scope="scope">
            <el-popover placement="top-start" width="300" trigger="hover">
              <div v-html="tohtml(scope.row.conditionalRecordsSql)"></div>
              <div class="celldiv" slot="reference">
                {{ scope.row.conditionalRecordsSql }}
              </div>
            </el-popover>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          size="mini"
          @click="dialogFormVisible = handlerClose()"
          >关闭</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  executeOneRule,
  asyncExecuteOneRule,
  getSqlExecStatus,
  getSqlExecResult,
} from "@/api/document-management/rule-configuration";
import getCodeValueConten from "@/mixins/getCodeValueContent";
export default {
  mixins: [getCodeValueConten],
  data() {
    return {
      dialogFormVisible: false, // 弹框状态
      querydata: {
        // 表单数据
        directoryName: "",
        directoryCode: "",
        emrRuleType: "",
        dataStartTime: "",
        dataEndTime: "",
      },
      dataval: [],
      tableData: [], //表字段列表
      loading: false, //表字段列表加载状态
      headerName: ["", ""],
      str: "",
      circulationtpost: null,
    };
  },
  props: {
    selectedProject: {
      type: Object, // 根据实际情况调整类型
      required: true,
    },
    queryRightTable: {
      type: Object,
    },
    headerobj: {
      type: Object,
    },
  },
  methods: {
    // 处理dialog打开时
    handlerOpen() {
      if (this.queryRightTable.emrRuleType === "一致性") {
        this.headerName = [
          "记录总数T",
          "记录总数T（SQL语句）",
          "有对照记录数C",
          "有对照记录数C（SQL语句）",
        ];
      } else if (this.queryRightTable.emrRuleType === "完整性") {
        this.headerName = [
          "记录总数T",
          "记录总数T（SQL语句）",
          "完整记录数N",
          "完整记录数N（SQL语句）",
        ];
      } else if (this.queryRightTable.emrRuleType === "整合性") {
        this.headerName = [
          this.headerobj.headerName1 + "记录数T",
          this.headerobj.headerName1 + "记录数T（SQL语句）",
          this.headerobj.headerName2 + "可关联对照记录数L",
          this.headerobj.headerName2 + "可关联对照记录数L（SQL语句）",
        ];
      } else if (this.queryRightTable.emrRuleType === "及时性") {
        this.headerName = [
          this.headerobj.headerName1 + "记录数T",
          this.headerobj.headerName1 + "记录数T（SQL语句）",
          this.headerobj.headerName2 + "完整记录数N",
          this.headerobj.headerName2 + "完整记录数N（SQL语句）",
        ];
      }
    },
    // 处理dialog关闭后
    handlerClose() {
      this.querydata = {
        // 表单数据
        directoryName: "",
        directoryCode: "",
        emrRuleType: "",
        dataStartTime: "",
        dataEndTime: "",
      };
      this.dataval = [];
      this.tableData = []; //表字段列表
      this.loading = false; //表字段列表加载状态
      this.headerName = ["", ""];
      clearInterval(this.circulationtpost);
    },
    //获取本月的第一天 获取本月的最后一天
    changeMonth() {
      let myDate = new Date(this.dataval[1]);
      let month = myDate.getMonth() + 1;
      month = month < 10 ? "0" + month : month; //格式化月份，补0
      let dayEnd = new Date(myDate.getFullYear(), month, 0).getDate(); //获取当月一共有多少天
      this.dataval = [this.dataval[0] + "-01", this.dataval[1] + "-" + dayEnd];
    },
    executeOneRule() {
      if (this.dataval.length < 2) {
        this.$message({
          type: "error",
          message: "请选择正确的时间再执行SQL",
        });
      } else {
        this.loading = true;
        this.querydata = {
          directoryName: this.queryRightTable.directoryName,
          directoryCode: this.queryRightTable.directoryCode,
          emrRuleType: this.queryRightTable.emrRuleType,
          dataStartTime: this.dataval[0],
          dataEndTime: this.dataval[1],
          configType: 0,
          projectId: this.selectedProject.id,
        };
        asyncExecuteOneRule(this.querydata).then((res) => {
          if (res.status === 0) {
            let circulationtpoststatus = false;
            this.circulationtpost = setInterval(async () => {
              if (circulationtpoststatus === false) {
                await getSqlExecStatus(this.querydata).then((ress) => {
                  if (ress.status === 0 && ress.data == "0") {
                    circulationtpoststatus = false;
                  } else if (ress.status === 0 && ress.data == "1") {
                    circulationtpoststatus = true;
                  } else {
                    this.$message({
                      type: "error",
                      message: ress.msg,
                    });
                  }
                });
              } else {
                await getSqlExecResult(this.querydata).then((resss) => {
                  clearInterval(this.circulationtpost);
                  if (resss.status === 0) {
                    this.tableData = resss.data;
                    this.loading = false;
                  } else {
                    this.$message({
                      type: "error",
                      message: resss.msg,
                    });
                  }
                });
              }
            }, 1000);
          } else {
            this.$message({
              type: "error",
              message: res.msg,
            });
          }
        });
        // executeOneRule({
        //   ...this.querydata,
        // }).then((res) => {
        //   if (res.status === 0) {
        //     this.tableData = res.data;
        //     this.loading = false;
        //   } else {
        //     this.$message({
        //       type: "error",
        //       message: res.msg,
        //     });
        //   }
        // });
      }
    },
    tohtml(data) {
      let str = "";
      str = data.replace(/\n/g, "<br/>");
      return str;
    },
  },
};
</script>

<style lang="scss" scoped>
.add-edit-data-source {
  display: inline-block;
  margin-right: 10px;
  .mgt-dialog-upload {
    margin-left: 50px;
  }
  .dialog-footer {
    text-align: center;
    position: relative;
    .test-data-source {
      position: absolute;
      top: 0;
      bottom: 0;
    }
  }
}
::v-deep .el-dialog .el-dialog__body {
  // 设置dialog的固定高度
  min-height: 450px;
  max-height: 650px;
  overflow: auto;
}
.celldiv {
  width: 100%;
  height: 20px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
