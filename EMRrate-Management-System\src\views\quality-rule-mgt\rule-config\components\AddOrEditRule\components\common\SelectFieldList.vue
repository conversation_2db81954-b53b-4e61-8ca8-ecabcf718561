<template>
  <el-popover
    @show="handlerShow"
    placement="right"
    width="500"
    trigger="click"
    v-model="visible"
  >
    <div class="selected">
      <h4>已选择字段：</h4>
      <span>{{ fieldNames }}</span>
    </div>
    <template v-if="type !== 'dictTableFiled'">
      <el-table
        ref="multipleTable"
        :data="checkRuleColumnData"
        height="500"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          v-if="selectType === 'checkBox'"
          fixed="left"
          type="selection"
          width="55"
        >
        </el-table-column>
        <el-table-column
          v-if="selectType === 'radio'"
          width="60"
          property="fieldName"
          label="选择"
        >
          <template slot-scope="scope">
            <el-radio
              v-model="radio"
              @change="handlerRadioChange"
              :label="scope.row.fieldName"
              >{{ null }}</el-radio
            >
          </template>
        </el-table-column>
        <el-table-column
          width="210"
          property="fieldName"
          label="字段名"
        ></el-table-column>
        <el-table-column
          width="100"
          property="fieldNameCn"
          label="描述"
        ></el-table-column>
        <el-table-column
          width="100"
          property="fieldType"
          label="字段类型"
        ></el-table-column>
        <el-table-column
          width="100"
          property="fieldlength"
          label="字段最大长度"
        ></el-table-column>
      </el-table>
    </template>
    <template v-if="type === 'dictTableFiled'">
      <el-table
        ref="multipleTable"
        :data="dictTableFiledData"
        height="500"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          v-if="selectType === 'checkBox'"
          fixed="left"
          type="selection"
          width="55"
        >
        </el-table-column>
        <el-table-column
          v-if="selectType === 'radio'"
          width="60"
          property="fieldName"
          label="选择"
        >
          <template slot-scope="scope">
            <el-radio
              v-model="radio"
              @change="handlerRadioChange"
              :label="scope.row.fieldName"
              >{{ null }}</el-radio
            >
          </template>
        </el-table-column>
        <el-table-column
          width="200"
          property="fieldName"
          label="字段名"
        ></el-table-column>
        <el-table-column
          width="100"
          property="fieldNameCn"
          label="描述"
        ></el-table-column>
        <el-table-column
          width="100"
          property="fieldType"
          label="字段类型"
        ></el-table-column>
        <el-table-column
          width="100"
          property="fieldlength"
          label="字段最大长度"
        ></el-table-column>
      </el-table>
    </template>
    <div style="text-align: right; margin: 0">
      <el-button size="mini" @click="toCancel">取消</el-button>
      <slot>
        <el-button type="primary" size="mini" @click="toSave">保存</el-button>
      </slot>
    </div>
    <el-button :disabled="isDisabled" @click.stop slot="reference"
      >选择</el-button
    >
  </el-popover>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      fieldNames: [],
      radio: "",
    }
  },
  props: {
    isDisabled: {
      type: Boolean,
    },
    checkRuleColumn: {
      type: String,
    },
    checkRuleColumnData: {
      type: Array,
    },
    dictTableFiledData: {
      type: Array,
    },
    type: {
      type: String,
    },
    selectType: {
      type: String,
    },
    index: {
      type: Number,
    },
  },
  methods: {
    // 复选框选中
    handleSelectionChange(val) {
      this.fieldNames = val.map((item) => {
        return item.fieldName
      })
    },
    // 保存
    toSave() {
      this.$emit("backfillSelectedData", this.fieldNames, this.type, this.index)
      this.visible = false
    },
    // 显示时复选框或单选框默认选中
    handlerShow() {
      if (this.selectType === "radio") {
        this.radio = this.$parent.$parent.value // 默认选中
        this.fieldNames = [this.radio]
      } else {
        const arr = this.$parent.$parent.value.split(",")
        let newArr = []
        arr.forEach((item) => {
          if (this.checkRuleColumnData.length > 0) {
            this.checkRuleColumnData.forEach((item1) => {
              if (item1.fieldName === item) newArr.push(item1)
            })
          }
        })
        if (newArr.length > 0) {
          this.$refs.multipleTable.clearSelection()
          newArr.forEach((row) => {
            this.$refs.multipleTable.toggleRowSelection(row, true)
          })
        } else {
          this.$refs.multipleTable.clearSelection()
        }
      }
    },
    // 取消
    toCancel() {
      this.visible = false
    },
    // popover框隐藏后
    // handlerHide() {
    //   this.$emit("backfillSelectedData", this.fieldNames, this.type)
    // },
    handlerRadioChange(val) {
      this.fieldNames = [val]
    },
  },
}
</script>

<style lang="scss" scoped>
</style>
