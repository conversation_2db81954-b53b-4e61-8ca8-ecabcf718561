<template>
  <div>
    <el-menu
      :default-active="activeIndex"
      class="el-menu-demo"
      mode="horizontal"
      @select="handleSelect"
    >
      <el-menu-item
        :index="item.index"
        v-for="item in menulist"
        :key="item.index"
        ><div>
          {{ item.name }} <i></i>

          <!-- <img
            v-if="activeIndex === item.index"
            src="./../../../assets/emrimg/icon_20.png"
            alt=""
          /> -->
        </div>
      </el-menu-item>
    </el-menu>

    <MainCard>
      <div class="emr-container">
        <div class="tree-right">
          <div>
            <div class="right-input" v-show="activeIndex !== '1'">
              <svg-icon icon-class="dengji" /> 评价等级：
              <el-select
                @change="getUserQueryList(queryLeftTreelevelCode)"
                v-model="queryLeftTreelevelCode"
                placeholder="请选择"
                size="mini"
              >
                <el-option
                  v-for="item in levelCodeData"
                  :key="item.levelName"
                  :label="item.levelName"
                  :value="item.levelCode"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="right-button" v-show="savelist.userAccount != ''">
            <el-button size="mini" type="primary" @click="saveRulePermissions"
              >保存</el-button
            >
            <el-button
              size="mini"
              @click="queryDirectoryTreerule({ loginId: savelist.userAccount })"
              >还原</el-button
            >
          </div>
        </div>
        <div class="emr-container-main">
          <div class="emr-container-main-left">
            <div class="title">
              <b>成员列表</b>
              <el-tooltip
                class="item"
                effect="dark"
                content="添加成员"
                placement="top"
              >
                <el-button
                  class="addicon"
                  style="padding: 6px"
                  icon="el-icon-plus"
                  @click="
                    $refs.Addpersonnel.handlerOpen(
                      activeIndex,
                      namedata[Number(activeIndex)]
                    )
                  "
                ></el-button>
              </el-tooltip>
            </div>
            <div class="tree">
              <div
                class="treeitem"
                @click="queryDirectoryTreerule({ loginId: '' })"
                :style="{
                  background: savelist.userAccount === '' ? '#EBEEFB' : '',
                }"
              >
                全部项目情况总览
              </div>
              <div
                class="treeitem2"
                v-for="item in namedata[Number(activeIndex)]"
                :key="item.userAccount"
                @click="queryDirectoryTreerule(item)"
                :style="{
                  background:
                    savelist.userAccount === item.loginId ? '#EBEEFB' : '',
                }"
              >
                <div
                  class="sex"
                  :style="{
                    background: item.gender === 'M' ? '#ad7ef4' : '#4EA5F8',
                  }"
                >
                  {{ item.userName.substr(item.userName.length - 1, 1) }}
                </div>
                <span class="name">{{ item.userName }}</span>
                <span
                  class="accomplish"
                  :style="{
                    color:
                      (item.finishedNum === item.allocatedNum) &
                      (item.allocatedNum > 0)
                        ? '#38d594'
                        : 'transparent',
                  }"
                  >√</span
                >
                <div class="performance">
                  <div>
                    完成进度：{{ item.finishedNum }}/{{ item.allocatedNum }}
                  </div>
                  <el-progress
                    :percentage="
                      item.allocatedNum === 0
                        ? 0
                        : (item.finishedNum / item.allocatedNum) * 100
                    "
                    color="#38D695"
                    :show-text="false"
                  ></el-progress>
                </div>
              </div>
            </div>
          </div>
          <div class="emr-container-main-right">
            <div class="right-statistics" v-show="savelist.userAccount === ''">
              <div class="statistics">
                <span
                  >评价项目总数：<b>{{ numberobj.allNum }}</b></span
                >
                <span
                  >未分配数：<b>{{
                    numberobj.allNum - numberobj.allocated
                  }}</b></span
                >
                <span
                  >完成进度：<b
                    ><span>{{ numberobj.finished }}</span
                    >/{{ numberobj.allocated }}</b
                  ><el-tooltip placement="top">
                    <div slot="content">已完成数/已分配数=完成进度</div>
                    <i class="el-icon-warning"></i> </el-tooltip
                ></span>
              </div>
              <el-progress
                type="circle"
                :width="50"
                :percentage="
                  numberobj.finished > 0
                    ? Math.floor(
                        (numberobj.finished / numberobj.allocated) * 100
                      )
                    : 0
                "
              ></el-progress>
            </div>
            <div class="title">
              <div>文档目录</div>
              <div>
                <span v-show="activeIndex == '2' || activeIndex == '3'"
                  >类型</span
                >
              </div>
              <div>负责人</div>
              <div>完成情况</div>
            </div>
            <div class="tree">
              <div class="tree-options" v-show="savelist.userAccount != ''">
                <el-checkbox v-model="checkAll" @change="handleCheckAll"
                  >全选</el-checkbox
                >
              </div>
              <el-tree
                :data="treeData"
                accordion
                :show-checkbox="savelist.userAccount != ''"
                node-key="id"
                ref="tree"
                highlight-current
                :props="defaultProps"
                :default-checked-keys="defaultKeys"
                :default-expand-all="true"
                v-loading="isLoading"
              >
                <div class="custom-tree-node" slot-scope="{ node }">
                  <div class="tree-label">{{ node.data.label }}</div>

                  <div class="tree-dataType" v-show="node.data.issubmit">
                    <span v-if="node.data.dataType == '0'" class="jiben"
                      >基本项</span
                    >
                    <span v-else-if="node.data.dataType == '1'" class="xuanze"
                      >选择项</span
                    >
                  </div>
                  <div class="tree-principal" v-if="node.data.issubmit">
                    {{ node.data.personInCharge }}
                  </div>
                  <!-- <div
                  class="tree-principal"
                  v-else-if="
                    node.data.issubmit & (node.data.taskStatus === '0')
                  "
                >
                  未分配
                </div> -->
                  <div class="tree-state">
                    <svg-icon
                      icon-class="icon_success"
                      v-show="
                        node.data.issubmit && node.data.taskStatus === '2'
                      "
                    />
                    <span
                      class="fonttask"
                      v-show="node.data.issubmit && !node.data.taskStatus"
                    >
                      未分配</span
                    >
                    <span
                      class="fonttask"
                      v-show="
                        node.data.issubmit && node.data.taskStatus === '0'
                      "
                    >
                      未分配</span
                    >
                    <span
                      class="fonttask"
                      v-show="
                        node.data.issubmit && node.data.taskStatus === '1'
                      "
                    >
                      已分配
                    </span>
                  </div>
                </div>
              </el-tree>
            </div>
          </div>
        </div>
      </div>
      <Addpersonnel ref="Addpersonnel" />
    </MainCard>
  </div>
</template>

<script>
import { queryAllow } from "@/api/document-management/dictionary-configuration";
import {
  getUserListAndTasks,
  queryDirectoryTreerule,
  getTaskrule,
  saveRulePermissions,
} from "@/api/document-management/catalogue-configuration";
import Addpersonnel from "./components/Addpersonnel.vue";
export default {
  components: {
    Addpersonnel,
  },
  data() {
    return {
      isLoading: false,
      activeIndex: "0", //激活菜单
      queryLeftTreelevelCode: "", //已选择等级
      // 人员列表
      namedata: {},
      // 底部数据
      numberobj: {
        allNum: 0,
        finished: 0,
        allocated: 0,
      },
      // 保存数据
      savelist: {
        userAccount: "",
        configType: "",
        rulePermissionList: [],
      },
      levelCodeData: [], //等级
      treeData: [], //左侧树形数据
      defaultKeys: [], //穿梭框默认选中的
      // 菜单列表
      menulist: [
        { index: "0", name: "质量文档" },
        { index: "1", name: "基础数据填报" },
        { index: "2", name: "病历数据填报" },
        { index: "3", name: "质量数据填报" },
      ],
      // 树形数据
      defaultProps: {
        children: "children",
        label: "label",
      },
      // projectList: [],
      // selectedProject: "",
      checkAll: false,
      activeporject: {
        levelCode: "",
      },
    };
  },
  async created() {
    if (this.$route.params.id === undefined) {
      this.activeporject = JSON.parse(sessionStorage.getItem("projectactive"));
    } else {
      this.activeporject = this.$route.params;
    }
    this.queryLeftTreelevelCode = this.activeporject.levelCode;
    this.savelist = {
      userAccount: "",
      rulePermissionList: [],
    };
    this.defaultKeys = []; //穿梭框默认选中的
    // 进入页面初始化查询等级
    await queryAllow({}).then((res) => {
      const filteredData = res.data.list.filter(
        (item) => parseInt(item.levelCode) <= this.activeporject.levelCode
      );
      this.levelCodeData = filteredData;
    });
    this.getUserQueryList();
  },
  methods: {
    // 切换菜单
    handleSelect(key, keyPath) {
      this.activeIndex = key;
      this.treeData = [];
      this.defaultKeys = [];
      this.getUserQueryList();
    },
    // 获取所有人员
    getUserQueryList() {
      this.treeData = [];
      this.defaultKeys = [];
      this.numberobj = {
        allNum: 0,
        finished: 0,
        allocated: 0,
      };
      getUserListAndTasks({
        configType: Number(this.activeIndex),
        levelCode: this.queryLeftTreelevelCode,
        projectId: this.activeporject.id,
      }).then((res) => {
        if (res.status === 0) {
          this.namedata = res.data;
          this.numberobj.allNum = res.data.allNum;
          this.namedata[Number(this.activeIndex)].forEach((item) => {
            this.numberobj.allocated =
              item.allocatedNum + this.numberobj.allocated;
            this.numberobj.finished =
              item.finishedNum + this.numberobj.finished;
          });
        }
        this.queryDirectoryTreerule({ loginId: "" });
      });
    },

    // 查询目录树形
    async queryDirectoryTreerule(data) {
      this.treeData = [];
      this.defaultKeys = [];
      this.savelist.userAccount = data.loginId;
      await queryDirectoryTreerule({
        configType: Number(this.activeIndex),
        levelCode: this.queryLeftTreelevelCode,
        needNotAllocationTask: true,
        userAccount: data.loginId,
        projectId: this.activeporject.id,
      }).then((res) => {
        if (this.activeIndex === "1") {
          const newData = res.data.map((item) => {
            return {
              label: item["directoryCode"] + "、   " + item["directoryName"],
              directoryCode: item["directoryCode"],
              directoryName: item["directoryName"],
              emrRuleType: item["emrRuleType"],
              personInCharge: item["personInCharge"],
              taskStatus: item["taskStatus"],
              issubmit: true,
              id: item["directoryName"] + item["directoryCode"],
            };
          });
          this.treeData = newData;
        } else if (this.activeIndex === "0") {
          this.treeData = this.directoryredefine(res.data);
        } else if (this.activeIndex === "2" || "3") {
          const newData = res.data.map((item) => {
            return {
              label: item["directoryCode"] + item["directoryName"],
              directoryCode: item["directoryCode"],
              directoryName: item["directoryName"],
              emrRuleType: item["emrRuleType"],
              issubmit: false,
              id: item["directoryName"] + item["directoryCode"],
              children: item.secondLevels.map((item2) => {
                return {
                  label: item2["serialNum"] + "、   " + item2["directoryName"],
                  directoryCode: item2["directoryCode"],
                  directoryName: item2["directoryName"],
                  emrRuleType: item2["emrRuleType"],
                  id: item2["directoryName"] + item2["directoryCode"],
                  issubmit: false,
                  children: item2.thirdLevels.map((item3) => {
                    return {
                      label: item3["directoryCode"] + item3["directoryName"],
                      issubmit: true,
                      directoryCode: item3["directoryCode"],
                      directoryName: item3["directoryName"],
                      emrRuleType: item3["emrRuleType"],
                      personInCharge: item3["personInCharge"],
                      taskStatus: item3["taskStatus"],
                      dataType: item3["dataType"],
                      id: item3["directoryName"] + item3["directoryCode"],
                    };
                  }),
                };
              }),
            };
          });
          this.treeData = newData;
        }
      });
      if (this.savelist.userAccount != "") {
        // 已选择反选
        await getTaskrule({
          configType: Number(this.activeIndex),
          levelCode: this.queryLeftTreelevelCode,
          userAccount: data.loginId,
          projectId: this.activeporject.id,
        }).then((res) => {
          this.defaultKeys = [];
          if (res.status === 0) {
            if (res.data.rulePermissionList.length >= 1) {
              res.data.rulePermissionList.forEach((item) => {
                this.defaultKeys.push(
                  item["directoryName"] +
                    item["directoryCode"] +
                    item["emrRuleType"]
                );
              });
              this.$refs.tree &&
                this.$refs.tree.setCheckedKeys(this.defaultKeys);
            }
          }
        });
      }
    },
    // 质量文档目录重定义
    directoryredefine(arr) {
      const newData = arr.map((item) => {
        return {
          label: item["directoryName"],
          directoryCode: item["directoryCode"],
          directoryName: item["directoryName"],
          emrRuleType: item["emrRuleType"],
          issubmit: false,
          id: item["directoryName"] + item["directoryCode"],
          children: item.secondLevels.map((item2) => {
            return {
              label: item2["directoryCode"] + item2["directoryName"],
              directoryCode: item2["directoryCode"],
              directoryName: item2["directoryName"],
              emrRuleType: item2["emrRuleType"],
              id: item2["directoryName"] + item2["directoryCode"],
              issubmit: false,
              children: Object.keys(item2.thirdLevels).map((i) => {
                if (item2.thirdLevels[i].length === 1) {
                  return {
                    label:
                      i + "  :  " + item2.thirdLevels[i][0]["directoryName"],
                    issubmit: true,
                    directoryCode: item2.thirdLevels[i][0]["directoryCode"],
                    directoryName: item2.thirdLevels[i][0]["directoryName"],
                    emrRuleType: item2.thirdLevels[i][0]["emrRuleType"],
                    personInCharge: item2.thirdLevels[i][0]["personInCharge"],
                    taskStatus: item2.thirdLevels[i][0]["taskStatus"],
                    id:
                      item2.thirdLevels[i][0]["directoryName"] +
                      item2.thirdLevels[i][0]["directoryCode"] +
                      item2.thirdLevels[i][0]["emrRuleType"],
                  };
                } else if (item2.thirdLevels[i].length > 1) {
                  return {
                    label: i,
                    issubmit: false,
                    id: item2["directoryName"] + item2["directoryCode"] + i,
                    children: item2.thirdLevels[i].map((item3) => {
                      return {
                        label: item3["directoryName"],
                        issubmit: true,
                        directoryCode: item3["directoryCode"],
                        directoryName: item3["directoryName"],
                        emrRuleType: item3["emrRuleType"],
                        personInCharge: item3["personInCharge"],
                        taskStatus: item3["taskStatus"],
                        id:
                          item3["directoryName"] +
                          item3["directoryCode"] +
                          item3["emrRuleType"],
                      };
                    }),
                  };
                }
              }),
            };
          }),
        };
      });
      return newData;
    },
    // 保存
    async saveRulePermissions() {
      this.savelist.configType = this.activeIndex;
      this.savelist.projectId = this.activeporject.id;
      this.savelist.levelCode = this.queryLeftTreelevelCode;
      var checkedNodes = this.$refs.tree.getCheckedNodes(true);
      if (checkedNodes.length > 0) {
        checkedNodes.forEach((item) => {
          this.savelist.rulePermissionList.push({
            configType: this.activeIndex,
            directoryCode: item.directoryCode,
            directoryName: item.directoryName,
            emrRuleType: item.emrRuleType,
          });
        });
      } else {
        this.savelist.rulePermissionList = [];
      }
      await saveRulePermissions(this.savelist).then((res) => {
        if (res.status === 0) {
          this.$message({
            message: "保存成功!",
            type: "success",
          });
          this.savelist.rulePermissionList = [];
          this.getUserQueryList();
        } else {
          this.$message({
            message: res.msg,
            type: "error",
          });
          this.getUserQueryList();
        }
      });
    },
    // 全选或反选节点
    handleCheckAll(checked) {
      // checked 参数表示当前是否被选中
      if (checked) {
        // 如果全选被选中，设置所有节点为选中状态
        this.$refs.tree.setCheckedKeys(this.getAllNodeKeys());
      } else {
        // 如果全选取消选中，清空所有节点的选中状态
        this.$refs.tree.setCheckedKeys([]);
      }
    },
    getAllNodeKeys() {
      // 获取所有节点的 id，作为默认选中的节点
      return this.treeData.map((node) => node.id);
    },
  },
};
</script>

<style scoped lang="scss">
@import "@/styles/emr-styles/emr-dialog.scss";
.el-menu {
  margin-bottom: 10px;
  background: transparent;
  border-bottom: 0px solid #000;
}

.el-menu--horizontal > .el-menu-item {
  background: transparent;
  margin-right: 20px;
  height: 40px;
  font-size: 16px;
  line-height: 40px;
}
.el-menu--horizontal > .el-menu-item:hover {
  background: transparent;
}

.el-menu--horizontal > .el-menu-item.is-active {
  font-weight: bold;
  font-size: 16px;
  color: #5270dd;
  background: transparent;
  border-radius: 10px 10px 0px 0px;
  border: 0px solid transparent;

  div {
    position: relative;

    i {
      position: absolute;
      bottom: 0px;
      left: 25%;
      padding: 2px 8px;
      background: #5270dd;
      width: 50%;
      border-radius: 3px;
    }
  }
}
.emr-container {
  .tree-right {
    margin-top: 20px;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    .el-select {
      width: 100px;
    }
    svg {
      color: #5783e6;
    }
  }
  .emr-container-main {
    display: flex;
    position: relative;
    .emr-container-main-left {
      width: 15%;
      min-width: 300px;
      .title {
        margin-top: 10px;
        margin-bottom: 10px;
        display: flex;
        width: 100%;
        justify-content: space-between;
        font-size: 15px;
        color: #333333;
      }
      .tree {
        background: #ffffff;
        height: 90%;
        border-radius: 9px;
        border: 1px solid #e3e6e8;
        padding: 10px;
        overflow: scroll;
        .treeitem {
          font-size: 14px;
          line-height: 40px;
          word-break: break-word;
          width: 100%;
          text-align: center;
          border: 1px solid #6581e9;
          border-radius: 9px;
          cursor: pointer;
          color: #5270dd;
        }
        .treeitem2 {
          border-radius: 9px;
          border: 1px solid #e3e4f2;
          margin-top: 10px;
          line-height: 56px;
          display: flex;
          align-items: center;
          justify-content: space-around;
          cursor: pointer;
          padding: 0px 15px;
          .sex {
            background: #ad7ef4;
            width: 26px;
            height: 26px;
            line-height: 26px;
            text-align: center;
            border-radius: 50%;
            color: #ffffff;
          }
          .name {
            font-size: 15px;
            color: #000000;
          }
          .accomplish {
            color: #38d594;
            font-weight: 800;
          }
          .performance {
            line-height: 30px;
          }
        }
      }
    }
    .emr-container-main-right {
      flex: 1;
      overflow: hidden;
      .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        width: 100%;

        .tree-label {
          width: 60%;
          text-align: left;
          white-space: normal;
        }

        .tree-dataType {
          text-align: left;
          width: 10%;
          min-width: 100px;
          span {
            border-radius: 4px;
            padding: 2px 6px;
          }
          .jiben {
            background: #e0ecfb;
          }
          .xuanze {
            background: #f8eec9;
          }
        }
        .tree-state {
          text-align: left;
          width: 10%;
          min-width: 100px;
          font-size: 30px;
          .fonttask {
            font-size: 14px;
            padding-bottom: 4px;
            line-height: 34px;
          }
        }
        .tree-principal {
          text-align: left;
          width: 10%;
          min-width: 100px;
          right: 120px;
        }
      }
      .title {
        margin-top: 20px;
        margin-left: 30px;
        margin-bottom: 10px;
        font-size: 15px;
        color: #333333;
        border-bottom: 1px solid #ebedef;
        line-height: 40px;
        justify-content: space-around;
        display: flex;
        > div {
          text-align: left;
        }
        :nth-child(1) {
          width: 60%;
        }
        :nth-child(2),
        :nth-child(3),
        :nth-child(4) {
          width: 9%;
        }
      }
      .el-tree {
        height: 60vh;
        overflow: scroll;
      }
    }
  }
  .right-statistics {
    margin-left: 10px;
    color: #666666;
    padding-top: 4px;
    // border-top: 1px solid #f3f4f5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .statistics {
      > span {
        margin-right: 40px;
        span {
          color: #38d594;
        }
      }
      b {
        color: #000;
      }
    }
  }
  .right-button {
    text-align: right;
  }
}
::v-deep .el-tree-node__content {
  height: auto;
  margin: 10px 0px;
}
::v-deep .el-progress-bar__outer {
  background: #fff !important;
}
::v-deep .el-progress__text {
  font-size: 14px !important;
}
.header {
  ::v-deep .el-input__inner {
    border: none;
    box-shadow: none;
    font-size: 20px;
    color: red;
    font-weight: bold;
  }
  ::v-deep .el-input {
    margin-bottom: 20px;
  }
}
.tree-options {
  margin-left: 10px;
}
</style>
