import request from '@/utils/request'


/*****************************************数据源*****************************************/
//查询数据源
export function datasourceQuery(data) {
    return request({
        url: '/datasource/query',
        method: 'post',
        data
    })
}
//删除数据源
export function datasourceDelete(id) {
    return request({
        url: '/datasource/delete',
        method: 'delete',
        params: { id },
    })
}

//更新数据源
export function datasourceUpdate(data) {
    return request({
        url: '/datasource/update',
        method: 'post',
        data
    })
}

//新增数据源
export function datasourceAdd(data) {
    return request({
        url: '/datasource/insert',
        method: 'post',
        data
    })
}


/*****************************************医疗机构*****************************************/
//查询医疗机构
export function medinstQuery(data) {
    return request({
        url: '/medinst/queryAll',
        method: 'post',
        data
    })
}

//删除医疗机构
export function medinstDelete(id) {
    return request({
        url: '/medinst/delete',
        method: 'delete',
        params: { id },
    })
}

//更新医疗机构
export function medinstUpdate(data) {
    return request({
        url: '/medinst/update',
        method: 'post',
        data
    })
}

//新增医疗机构
export function medinstAdd(data) {
    return request({
        url: '/medinst/insert',
        method: 'post',
        data
    })
}
