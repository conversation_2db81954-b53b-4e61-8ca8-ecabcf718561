import request from "@/utils/request"

/************************ APP ************************/
export function login(data) {
  return request({
    url: "/user/doLogin",
    method: "post",
    data,
  })
}

export function singleLogin(token) {
  return request({
    url: "/user/doSsoLogin",
    method: "post",
    params: {
      token,
    },
  })
}

export function logout(token) {
  return request({
    url: "/user/loginOut",
    method: "put",
    data: {
      token,
    },
  })
}

export function getInfo(token) {
  return request({
    url: "/user/getUserInfo",
    method: "get",
    params: { token },
  })
}

//检查token是否过期,如果过期时间小于5分钟就重新获取Token
export function checkToken() {
  return request({
    url: "/user/checkToken",
    method: "post",
  })
}

/************************ 用户管理 ************************/
//获取用户列表
export function userQueryList(data) {
  return request({
    url: "/user/queryList",
    method: "post",
    data,
  })
}
//修改用户
export function UpdateUser(data) {
  return request({
    url: "/user/updateUser",
    method: "post",
    data,
  })
}
//删除用户
export function DeleteUser(data) {
  return request({
    url: "/user/deleteUser",
    method: "delete",
    data,
  })
}
//添加用户
export function AddUser(data) {
  return request({
    url: "/user/add",
    method: "post",
    data,
  })
}
//修改用户状态
export function updateUserStatus(data) {
  return request({
    url: "/user/updateUserStatus",
    method: "post",
    data,
  })
}
//修改密码
export function resetPassWord(data) {
  return request({
    url: "/user/updateUserPassword",
    method: "post",
    data,
  })
}

/************************ 角色权限管理 ************************/
//获取角色列表
export function getRoleList(params) {
  return request({
    url: "/sysRole/list",
    method: "get",
    params,
  })
}
//添加角色
export function addRole(data) {
  return request({
    url: "/sysRole/add",
    method: "post",
    data,
  })
}
//删除角色
export function deleteRole(params) {
  return request({
    url: `/sysRole/deleteById/${params.roleId}`,
    method: "delete",
  })
}
//更新角色
export function updateRole(data) {
  return request({
    url: "/sysRole/updateRole",
    method: "post",
    data,
  })
}

/************************ 树状路径权限管理 ************************/
//获取Permissio列表
export function getPermissioList() {
  return request({
    url: "/sysPermission/list",
    method: "get",
  })
}
//企业微信图片
export function getQywxInfo() {
  return request({
    url: "/wx/getQywxInfo",
    method: "get",
  })
}
//企业微信登录回调
export function getScanResult(params) {
  return request({
    url: "/wx/getScanResult",
    method: "get",
    params,
  })
}
//企业微信登录回调
export function qywxLogin(params) {
  return request({
    url: "/wx/qywxLogin",
    method: "get",
    params,
  })
}
// 二维码图片
export function getQrCode(params) {
  return request({
    url: "/wx/getQrCode",
    method: "get",
    params,
  })
}

// 绑定企业微信账户和SSO账户
export function bindAccount(data) {
  return request({
    url: "/wx/bindAccount",
    method: "post",
    data,
  })
}
// 验证码登录
// 获取用户登录认证方式
export function getIdentifyInfo(params) {
  return request({
    url: "/user/getIdentifyInfo",
    method: "get",
    params,
  })
}
// 根据用户账号发送短信验证码
export function sendPin(params) {
  return request({
    url: "/user/sendPin",
    method: "get",
    params,
  })
}
// 微信登录
// 获取用户登录认证方式
export function getAuthUrl() {
  return request({
    url: "/wx/getAuthUrl",
    method: "get",
  })
}
//微信登录回调
export function weixinLogin(params) {
  return request({
    url: "/wx/weixinLogin",
    method: "get",
    params,
  })
}
export function getImagePin() {
  return request({
    url: "/user/getImagePin",
    method: "get",
  })
}
