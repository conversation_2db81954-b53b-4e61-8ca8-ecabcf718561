<template>
  <div class="emr-container">
    <MainCard v-show="!Boolean(exportRecordshow)">
      <div class="emr-container-search">
        <div class="emr-container-search-left">
          <el-form
            ref="ruleForm"
            label-width="0px"
            element-loading-spinner="el-icon-loading"
          >
            <el-form-item prop="exportDocumentLevel">
              <div>评价等级</div>
              <el-select v-model="exportDocumentLevel" placeholder="请选择">
                <el-option
                  v-for="item in levelCodeData"
                  :key="item.levelName"
                  :label="item.levelName"
                  :value="item.levelCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <div>预览导出文档类型</div>
              <el-checkbox-group v-model="exportDocumentTypelist">
                <el-checkbox label="0" disabled
                  >实证材料文档(必选)
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-printer"
                size="mini"
                @click="exportRecordshow = true"
                >生成预览页面</el-button
              >
            </el-form-item>
          </el-form>
        </div>

        <div class="emr-container-search-right">
          <div class="emr-container-header">
            <el-form :model="queryData" ref="ruleForm1" inline>
              <el-form-item label="评价等级：">
                <el-select
                  v-model="queryData.exportDocumentLevel"
                  placeholder="请选择"
                  @change="queryempiricalmaterialrecord()"
                >
                  <el-option
                    v-for="item in levelCodeData"
                    :key="item.levelName"
                    :label="item.levelName"
                    :value="item.levelCode"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="操作时间" prop="databaseType">
                <el-date-picker
                  v-model="dataval"
                  size="mini"
                  type="daterange"
                  align="center"
                  unlink-panels
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束日期"
                  :picker-options="pickerOptions"
                  value-format="yyyy-MM-dd"
                  :clearable="false"
                  @change="changedataval"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item prop="databaseType">
                <el-button
                  type="primary"
                  @click="queryempiricalmaterialrecord()"
                  icon="el-icon-search"
                  size="mini"
                  >查询</el-button
                >
              </el-form-item>
              <el-form-item>
                <el-button @click="resetForm('ruleForm')" size="mini"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>
          </div>
          <div class="emr-container-main">
            <div class="emr-container-table">
              <el-table
                :data="tableData"
                ref="sourceMgtTable"
                style="width: 100%"
                :header-cell-style="{ background: '#fff', color: '#333' }"
              >
                <el-table-column prop="id" label="ID" min-width="150">
                  <template slot-scope="scope">
                    <div>
                      〔
                      {{
                        scope.row.exportDocumentLevel
                          ? convertToChinaNum(scope.row.exportDocumentLevel)
                          : ""
                      }}〕{{ scope.row.id }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="exportDocumentTemplatesNum"
                  label="导出状态"
                  min-width="80"
                  ><template slot-scope="scope">
                    <span v-if="scope.row.exportStatus === '0'">未执行</span>
                    <span v-if="scope.row.exportStatus === '1'">执行中</span>
                    <span v-if="scope.row.exportStatus === '2'">成功</span>
                    <span v-if="scope.row.exportStatus === '3'">异常</span>
                  </template>
                </el-table-column>
                <el-table-column prop="createBy" label="操作人" min-width="100">
                </el-table-column>

                <el-table-column
                  prop="createTime"
                  label="导出时间范围"
                  min-width="150"
                >
                </el-table-column>

                <el-table-column
                  prop="createTime"
                  label="初次操作时间"
                  min-width="150"
                >
                </el-table-column>

                <el-table-column fixed="right" label="操作" width="240">
                  <template slot-scope="scope">
                    <!-- <el-button
                      size="mini"
                      type="text"
                      :disabled="$store.state.user.isDownloadmgt"
                      @click="exportDocumentpost(scope.row)"
                    >
                      导出
                    </el-button> -->
                    <el-popconfirm
                      title="是否下载该文档?"
                      @onConfirm="exportDocumentpost(scope.row)"
                    >
                      <el-button
                        size="mini"
                        slot="reference"
                        type="text"
                        :disabled="$store.state.user.isDownloadmgt"
                      >
                        导出
                      </el-button>
                    </el-popconfirm>
                    <el-divider direction="vertical"></el-divider>
                    <!-- 
                    <el-button
                      size="mini"
                      type="text"
                      @click="
                        deleteempiricalmaterialRecord(scope.$index, scope.row)
                      "
                      >删除</el-button
                    > -->
                    <el-popconfirm
                      title="此操作将删除该项目, 是否继续?"
                      @onConfirm="deleteempiricalmaterialRecord(scope.row)"
                    >
                      <el-button slot="reference" size="mini" type="text"
                        >删除</el-button
                      >
                    </el-popconfirm>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="emr-container-pag">
              <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="queryData.pageNum"
                :page-sizes="[5, 10, 15, 20]"
                :page-size="queryData.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalNum"
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </MainCard>

    <MainCard v-show="Boolean(exportRecordshow)">
      <div class="emr-container-title-box">
        <div>
          <el-button
            type="text"
            @click="exportRecordshow = false"
            icon="el-icon-back"
            >返回</el-button
          >
          <el-divider direction="vertical"></el-divider>
          <span>文档预览</span>
        </div>

        <div>
          <span>
            <svg-icon icon-class="icon_10" />评价等级：<b>
              {{ exportDocumentLevel }}级
            </b></span
          >
          <el-popconfirm
            title="是否下载该文档?"
            @onConfirm="exportDocumentpost()"
          >
            <el-button
              type="primary"
              size="mini"
              slot="reference"
              :disabled="$store.state.user.isDownload"
              :icon="
                $store.state.user.isDownload
                  ? 'el-icon-loading'
                  : 'el-icon-folder-opened'
              "
              >{{
                $store.state.user.isDownload
                  ? "正在导出文档" + $store.state.user.isDownload + "%"
                  : "导出文档"
              }}
            </el-button>
          </el-popconfirm>
        </div>
      </div></MainCard
    >
    <div class="source-mgt-main" v-show="Boolean(this.exportRecordshow)">
      <div class="emr-container-left">
        <div>
          <div>
            文档目录
            <div></div>
          </div>

          <div
            class="aboutme"
            v-if="
              activeporject.personInCharge.includes($store.state.user.loginId)
            "
          >
            是否和我相关
            <el-switch
              @change="getLeftTreeList(exportDocumentLevel)"
              v-model="aboutme"
              active-color="#4969de"
              inactive-color="#aaa"
            >
            </el-switch>
          </div>
        </div>

        <!-- <div>
          <span><i></i>完成</span>
          <span style="color: #888888"
            ><i style="background: #888888"></i>未分配({{
              statisticaldata.alarmnum
            }})</span
          >
          <span style="color: #ef4f4f"
            ><i style="background: #ef4f4f"></i>未完成 ({{
              statisticaldata.errornum
            }})</span
          >
        </div> -->

        <div>
          <i class="el-icon-arrow-down"></i><i class="el-icon-arrow-up"></i>
        </div>
        <el-card shadow="never" v-loading="isLoading">
          <el-tree
            :data="treeData"
            default-expand-all
            node-key="id"
            ref="tree"
            highlight-current
            :props="defaultProps"
            @node-click="handleClick"
          >
            <template #default="props">
              <el-tooltip
                v-if="props.node.label.length > 17"
                class="item"
                effect="dark"
                :content="props.node.label"
                placement="right"
              >
                <span>
                  {{ props.node.label.substring(0, 15) }}...
                  <svg-icon
                    v-show="props.data.taskStatus === '2'"
                    icon-class="icon_success"
                    style="font-size: 24px"
                  ></svg-icon>
                </span>
              </el-tooltip>
              <span v-else>
                {{ props.node.label }}
                <!-- {{props.data.taskStatus }} -->
                <svg-icon
                  v-show="props.data.taskStatus === '2'"
                  icon-class="icon_success"
                  style="font-size: 24px"
                ></svg-icon>
              </span>
            </template>
          </el-tree>
        </el-card>
      </div>
      <div
        class="emr-container-right"
        ref="word"
        v-loading="fileLoading"
        element-loading-text="加载中,请稍后"
      >
        <MainCard>
          <div
            id="previewcontainer"
            :style="'width:' + previewcontainerwidth + 'px;'"
          ></div>
        </MainCard>
      </div>
    </div>
  </div>
</template>

<script>
import {
  queryDirectoryTree,
  previewWord,
  queryempiricalmaterialrecord,
  exportAsyncmgt,
  deleteempiricalmaterialRecord,
} from "@/api/empirical-material-mgt";
import { queryAllow } from "@/api/document-management/dictionary-configuration";
import { queryAllSysConfig } from "@/api/sys-config";
export default {
  data() {
    return {
      // 左侧菜单树状态
      isLoading: true,
      // 右侧预览状态
      fileLoading: true,
      // 预览及导出等级关联
      exportDocumentLevel: "",
      // 查询等级
      levelCodeData: [], //等级
      // 树状菜单数据
      treeData: [],
      // 查询时间数据
      dataval: [],
      aboutme: true,

      tableData: [], // 表格数据
      totalNum: 1,
      previewcontainerwidth: 0,
      previewcontainerheight: 0,
      // 预览表单
      queryWord: {
        directoryCode: "",
        directoryName: "",
        evaluationContentId: "",
      },
      exportRecordshow: "",
      exportDocumentTypelist: ["0"], //导出文档类型列表

      statisticaldata: {
        alarmnum: 0,
        errornum: 0,
      },
      // 数据菜单方法
      defaultProps: {
        children: "children",
        label: "label",
        disabled: function (data) {
          if (data.children) {
            return true;
          } else {
            return false;
          }
        },
      },

      queryData: {
        // 查询数据
        startTime: "",
        endTime: "",
        exportDocumentLevel: "",
        pageNum: 1,
        pageSize: 10,
      },
      option: {
        disabledDate: (time) => {
          return time.getTime() < Date.now() - 1 * 24 * 3600 * 1000;
        },
      },

      // 时间快捷选择快捷方式
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(new Date(new Date().setHours(0, 0, 0, 0)));
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      // projectList: [],
      // selectedProject: "",
    };
  },
  created() {
    if (this.$route.params.id === undefined) {
      this.activeporject = JSON.parse(sessionStorage.getItem("projectactive"));
    } else {
      this.activeporject = this.$route.params;
    }
    // 进入页面初始化查询等级
    queryAllow({}).then((res) => {
      const filteredData = res.data.list.filter(
        (item) => parseInt(item.levelCode) <= this.activeporject.levelCode
      );
      this.levelCodeData = filteredData;
      if (res.data.list.length) {
        this.exportDocumentLevel = this.activeporject.levelCode;
        this.getLeftTreeList(this.exportDocumentLevel);
        // 进入页面初始化查询
        this.queryempiricalmaterialrecord();
      }
    });
  },
  methods: {
    // 改变页面显示条数
    handleSizeChange(val) {
      this.queryData.pageSize = val;
      this.queryempiricalmaterialrecord();
    },
    // 改变页数
    handleCurrentChange(val) {
      this.queryData.pageNum = val;
      this.queryempiricalmaterialrecord();
    },
    // 查询文档导出记录
    queryempiricalmaterialrecord() {
      queryempiricalmaterialrecord({
        ...this.queryData,
        projectId: this.activeporject.id,
      }).then((res) => {
        if (res.status === 0) {
          this.tableData = res.data.list;
          this.tableData.forEach((item) => {
            Object.assign(item, { downloadbutton: false });
          });
          this.totalNum = res.data.total;
        }
      });
    },
    // 获取左侧树列表数据
    getLeftTreeList(val) {
      this.isLoading = true;
      this.treeData = []; // 清空左侧树数据
      queryDirectoryTree({
        levelCode: val,
        projectId: this.activeporject.id,
        needNotAllocationTask: false,
        userAccount: this.aboutme ? this.$store.state.user.loginId : null,
      }).then((res) => {
        // const newData = res.data.map((item) => {
        //   return {
        //     label: item["serialNum"] + "、   " + item["directoryName"],
        //     directoryCode: item["directoryCode"],
        //     directoryName: item["directoryName"],
        //     issubmit: false,
        //     children: item.secondLevels.map((item2) => {
        //       return {
        //         label: item2["serialNum"] + "、   " + item2["directoryName"],
        //         directoryCode: item2["directoryCode"],
        //         directoryName: item2["directoryName"],
        //         issubmit: false,
        //         children: item2.thirdLevels.map((item3) => {
        //           return {
        //             label:
        //               item3["directoryCode"] +
        //               item3["directoryName"] +
        //               (item3["evaluationCategory"] === "0"
        //                 ? "(基本)"
        //                 : "(选择)"),
        //             taskStatus: item3["taskStatus"],
        //             issubmit: true,
        //             directoryCode: item3["directoryCode"],
        //             directoryName: item3["directoryName"],
        //             evaluationContentId: item3["evaluationContentId"],
        //             levelCode: item3["levelCode"],
        //             personInCharge: item3["personInCharge"],
        //             id: item3["id"],
        //             taskStatus: item3["taskStatus"],
        //           };
        //         }),
        //       };
        //     }),
        //   };
        // });

        const newData = [];
        res.data.forEach((item) => {
          newData.push({
            label: item["directoryName"],
            directoryCode: item["directoryCode"],
            directoryName: item["directoryName"],
            emrRuleType: item["emrRuleType"],
            issubmit: false,
            children: [],
            secondLevels: item.secondLevels,
          });
        });

        newData.forEach((item) => {
          item.secondLevels.forEach((item2) => {
            item2.thirdLevels.forEach((item3) => {
              item.children.push({
                label:
                  item3["directoryCode"] +
                  item3["directoryName"] +
                  (item3["evaluationCategory"] === "0" ? "(基本)" : "(选择)"),
                taskStatus: item3["taskStatus"],
                issubmit: true,
                directoryCode: item3["directoryCode"],
                directoryName: item3["directoryName"],
                evaluationContentId: item3["evaluationContentId"],
                levelCode: item3["levelCode"],
                personInCharge: item3["personInCharge"],
                id: item3["id"],
                taskStatus: item3["taskStatus"],
              });
            });
          });
        });
        this.treeData = newData;
        this.isLoading = false;
      });
    },
    // 点击树形结构 表 或 view
    handleClick(data1, data2, data3) {
      if (data1.issubmit) {
        this.fileLoading = true;
        this.queryWord = {
          directoryCode: data1.directoryCode,
          directoryName: data1.directoryName,
          evaluationContentId: data1.evaluationContentId,
        };
        queryAllSysConfig().then((res) => {
          for (let key in res.data) {
            if (key === "empiricalmaterial.word.horizontal") {
              this.previewcontainerwidth = res.data[key] === "Y" ? 1130 : 800;
              this.previewcontainerheight = res.data[key] === "Y" ? 800 : 1130;
            }
          }
        });
        previewWord({
          ...this.queryWord,
          projectId: this.activeporject.id,
        }).then((res) => {
          document.getElementById("previewcontainer").innerHTML = res;
          this.fileLoading = false;
        });
      }
    },

    //控制树状颜色
    // renderContent(h, { node, data, store }) {
    //   if (data.taskStatus === "0") {
    //     return <span style="color:green;fontSize:12px">{node.label}</span>;
    //   } else if (data.taskStatus === "1") {
    //     return <span style="color:orange;fontSize:12px">{node.label}</span>;
    //   } else {
    //     return <span style="fontSize:12px">{node.label}</span>;
    //   }
    // },
    // 导出文档操作
    async exportDocumentpost(data) {
      await exportAsyncmgt({
        levelCode: this.exportDocumentLevel,
        projectId: this.activeporject.id,
        id: Boolean(data) === false ? null : data.id,
      }).then((res) => {
        if (res.status === 0) {
          this.$store.commit("user/SET_ISDOWNLOADMGT", true);
        } else {
          this.$message({
            type: "error",
            message: res.msg,
          });
        }
      });
    },

    // 时间改变
    changedataval() {
      if (this.dataval.length > 1) {
        this.queryData.startTime = this.dataval[0];
        this.queryData.endTime = this.dataval[1];
      } else {
        this.queryData.startTime = "";
        this.queryData.endTime = "";
      }
      this.queryempiricalmaterialrecord();
    },
    handle: function () {
      var startAt = (new Date(this.date) * 1000) / 1000;
      if (startAt < Date.now()) {
        this.date = new Date();
      }
    },

    // 等级
    convertToChinaNum(num) {
      let returnstr = "";
      this.levelCodeData.forEach((item) => {
        if (Number(item.levelCode) === Number(num)) {
          returnstr = item.levelName;
        }
      });
      return returnstr;
    },
    deleteempiricalmaterialRecord(v) {
      deleteempiricalmaterialRecord(v.id).then((res) => {
        if (res.status === 0) {
          this.$message({
            message: "删除成功!",
            type: "success",
          });
          this.queryempiricalmaterialrecord();
        } else {
          this.$message({
            message: res.msg,
            type: "error",
          });
        }
      });
    },
    // 重置、
    resetForm() {
      this.queryData = {
        startTime: null,
        endTime: null,
        exportDocumentLevel: null,
        pageNum: 1,
        pageSize: 10,
      };
      queryempiricalmaterialrecord({
        ...this.queryData,
        projectId: this.activeporject.id,
      }).then((res) => {
        if (res.status === 0) {
          this.tableData = res.data.list;
          this.tableData.forEach((item) => {
            Object.assign(item, { downloadbutton: false });
          });
          this.totalNum = res.data.total;
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
@import "@/styles/emr-styles/emr-main-table.scss";

.emr-container {
  margin-bottom: 40px;
  .emr-container-search {
    display: flex;
    min-width: 1200px;
    justify-content: center;
    height: 100%;
    .emr-container-search-left {
      width: 300px;
      border-right: 1px solid #dbdde1;
      padding: 20px 30px;
      .el-form-item {
        // text-align: center;
        .el-select {
          width: 100%;
        }
        .el-date-editor {
          width: 100%;
        }
      }
    }
    .emr-container-search-right {
      padding: 20px 30px;
      flex: 1;
    }
  }
  .emr-container-title-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    > div:nth-child(1) {
      font-size: 14px;
      .el-divider {
        margin: 0px 10px;
      }
      .el-button {
        color: #666666;
      }
      span {
        color: #9da4ba;
      }
    }
    > div:nth-child(2) {
      font-size: 14px;
      > span {
        margin: 0px 10px;
        svg {
          margin: 0px 4px;
        }
      }
    }
  }
}

.source-mgt-main {
  display: flex;
  margin-top: 10px;

  .emr-container-left {
    width: 20%;
    min-width: 320px;
    border-right: 1px solid #dbdde1;
    padding-right: 10px;

    > div:nth-child(1) {
      display: flex;
      justify-content: space-between;
      align-items: center;
      line-height: 40px;
      height: 40px;
      > div:nth-child(1) {
        font-weight: bold;
        font-size: 15px;
        color: #4969de;
      }

      .aboutme {
        margin-left: 20px;
        color: #aaa;
        font-size: 14px;
      }
    }
    // > div:nth-child(2) {
    //   background: #ffffff;
    //   box-shadow: 0px 3px 9px 0px rgba(0, 0, 0, 0.1);
    //   border-radius: 9px;
    //   border: 1px solid #e1e1e1;
    //   padding: 8px 8px;
    //   display: flex;
    //   justify-content: space-around;
    //   font-size: 12px;
    //   margin: 20px 0px;
    //   span {
    //     color: #606266;
    //     display: flex;
    //     justify-content: center;
    //     height: 20px;
    //     line-height: 20px;
    //     i {
    //       display: block;
    //       margin-top: 5px;
    //       margin-right: 5px;
    //       height: 10px !important;
    //       width: 10px !important;
    //       background: #606266;
    //     }
    //   }
    // }
    > div:nth-child(2) {
      margin-top: 10px;
      i {
        border: 1px solid #cdcdcd;
        padding: 4px;
        background: #ffffff;
        border-radius: 3px;
        margin-right: 10px;
        cursor: pointer;
      }
    }
    > div:nth-child(3) {
      height: 70vh;
      overflow: scroll;
      background: transparent;
      background-repeat: 10px;
      padding: 10px;
      .el-tree {
        background: transparent;
      }
    }
  }
  .emr-container-right {
    flex: 1;
    margin-left: 20px;
    margin-top: 5vh;
    height: calc(76vh);
    text-align: center;
    display: flex;
    justify-content: space-around;
    .remark {
      display: flex;
      justify-content: center;
      margin-bottom: 20px;
      .el-input {
        width: 500px;
        margin-right: 20px;
      }
    }
    #previewcontainer {
      display: flex;
      justify-content: center;
      overflow-y: scroll;
      overflow-x: scroll;
      ::v-deep div {
        width: 100% !important;
        margin: 0px !important;
        ::v-deep span {
          width: 100% !important;
          word-break: break-all !important;
          color: red !important;
        }
      }
    }
  }
}
//checkBox自定义禁用样式
::v-deep .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
  border-color: #fff;
}
::v-deep .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
  background-color: #409eff;
  border-color: #dcdfe6;
}
::v-deep .el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #409eff;
}
.header {
  ::v-deep .el-input__inner {
    border: none;
    box-shadow: none;
    font-size: 20px;
    color: red;
    font-weight: bold;
  }
  ::v-deep .el-input {
    margin-bottom: 20px;
  }
}

.el-tree {
  font-size: 13px;
}
</style>
