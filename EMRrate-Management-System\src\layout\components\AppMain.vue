<template>
  <section
    class="app-main-container"
    :style="{
      padding: this.$route.matched[0].path === '/home-page' ? '0px' : '20px',
      paddingBottom: '0px'
    }">
    <transition
      name="fade-transform"
      mode="out-in">
      <router-view :key="key" />
    </transition>
    <div class="footer-info">
      <span>技术支持：佳缘科技股份有限公司</span
      ><span>联系电话：028-62122223</span><span>版本：V2.0</span>
    </div>
  </section>
</template>

<script>
export default {
  name: 'AppMain',
  computed: {
    key() {
      return this.$route.path
    }
  },
}
</script>

<style lang="scss">
.app-main-container {
  background-color: #f3f4f5;
  overflow-y: auto;
  .footer-info {
    font-size: 12px;
    position: absolute;
    bottom: 14px;
    right: 20px;
    span {
      margin-left: 20px;
      color: #888;
    }
  }
}
</style>
