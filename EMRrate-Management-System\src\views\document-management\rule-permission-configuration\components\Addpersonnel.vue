<template>
  <el-dialog
    v-dialogDrag
    :visible.sync="dialogFormVisible"
    @closed="handlerClose"
    :close-on-click-modal="false"
    width="530px"
    :show-close="false"
  >
    <div class="dialog-new-title">
      <h4>添加项目成员</h4>
      <i class="el-icon-close" @click="handlerClose()"></i>
    </div>
    <div class="dialog-body">
      <div class="dialog-left">
        <div class="listname"><b>成员列表</b>（{{ useralllist.length }}）</div>
        <div class="mainlist">
          <!-- <el-input
            placeholder="查找人员…"
            suffix-icon="el-icon-search"
            v-model="input1"
            @input="queryUserInfoList()"
          >
          </el-input> -->
          <div
            @click="addselect(item)"
            v-for="item in useralllist"
            :key="item.loginId"
            class="itemuser"
          >
            {{ item.userName }}
          </div>
        </div>
      </div>
      <img src="@/assets/emrimg/onright.png" alt="" />
      <div class="dialog-right">
        <div class="listname">
          <b>已选</b>（{{ emrSysUserGroupList.length }}）
        </div>
        <div class="mainlist">
          <div
            @click="delselect(item)"
            v-for="item in emrSysUserGroupList"
            :key="item.loginId"
            class="itemuser"
          >
            {{ item.userName }}
            <img src="@/assets/emrimg/delicon.png" alt="" />
          </div>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <span class="dialog-footer-button">
        <el-button size="mini" @click="dialogFormVisible = false"
          >取 消</el-button
        >
        <el-button size="mini" type="primary" @click="savesysUserGroup()"
          >保存</el-button
        ></span
      >
    </div>
  </el-dialog>
</template>

<script>
import {
  savesysUserGroup,
  queryUserInfoList,
} from "@/api/document-management/catalogue-configuration";
export default {
  data() {
    return {
      dialogFormVisible: false, // 弹框状态
      useralllist: [],
      input1: "",
      emrSysUserGroupList: [],
      groupName: "",
    };
  },
  created() {
    if (this.$route.params.id === undefined) {
      let projectactive = JSON.parse(sessionStorage.getItem("projectactive"));
      this.useralllist =
        projectactive.projectMembers === ""
          ? []
          : JSON.parse(projectactive.projectMembers);
    } else {
      this.useralllist =
        this.$route.params.projectMembers === ""
          ? []
          : JSON.parse(this.$route.params.projectMembers);
    }
  },
  methods: {
    // 处理dialog打开时
    async handlerOpen(type, namedata) {
      if (type === "0") {
        this.groupName = "病历文档";
      }
      if (type === "1") {
        this.groupName = "基础数据";
      }
      if (type === "2") {
        this.groupName = "病历数据";
      }
      if (type === "3") {
        this.groupName = "质量数据";
      }
      // this.queryUserInfoList();
      this.emrSysUserGroupList =
        namedata === undefined ? [] : JSON.parse(JSON.stringify(namedata));
      this.dialogFormVisible = true;
    },
    // queryUserInfoList() {
    //   this.useralllist = [];
    //   queryUserInfoList({
    //     current: 1,
    //     size: 99999,
    //     userName: this.input1,
    //   }).then((res) => {
    //     this.useralllist = res.data.list;
    //   });
    // },
    // 选择人员
    addselect(data) {
      if (
        this.emrSysUserGroupList.filter((item) =>
          item.loginId.includes(data.loginId)
        ).length === 0
      ) {
        this.emrSysUserGroupList.push(data);
      } else {
        this.$message({
          message: "该人员已选择，请选择其他人员",
          type: "error",
        });
      }
    },
    // 选择人员
    delselect(item) {
      this.emrSysUserGroupList.splice(
        this.emrSysUserGroupList.indexOf(item),
        1
      );
    },
    // 保存任务组
    savesysUserGroup() {
      let nwearr = [];
      this.emrSysUserGroupList.forEach((item) => {
        nwearr.push({
          groupName: this.groupName,
          id: item.id,
          loginId: item.loginId,
          userName: item.userName,
        });
      });
      savesysUserGroup({
        groupName: this.groupName,
        userList: nwearr,
        projectId: this.$route.params.id,
      })
        .then((res) => {
          if (res.status === 0) {
            this.$message({
              message: "保存成功!",
              type: "success",
            });
            this.handlerClose();
            this.$parent.$parent.getUserQueryList();
          } else {
            this.$message({
              message: res.msg,
              type: "error",
            });
          }
        })
        .catch((err) => {
          this.$message({
            message: err.msg,
            type: "error",
          });
        });
    },
    // 处理dialog关闭后
    handlerClose() {
      this.emrSysUserGroupList = [];
      this.dialogFormVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/emr-styles/emr-dialog.scss";
::v-deep .el-dialog__body {
  height: 100%;
  padding: 0px;
  .dialog-new-title {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 10px 20px;
  }
  .dialog-body {
    display: flex;
    justify-content: space-around;
    align-items: center;
    img {
      height: 20px;
    }
    .dialog-left {
      height: 100%;
    }
    .dialog-right {
      position: relative;
    }
    .listname {
      font-size: 14px;
      color: #333333;
      margin-bottom: 10px;
    }
    .mainlist {
      width: 224px;
      height: 341px;
      background: #ffffff;
      border-radius: 9px;
      border: 1px solid #e3e6e8;
      overflow: scroll;
      .el-input {
        margin: 10px 10%;
        width: 80%;
      }
      .itemuser {
        line-height: 30px;
        height: 30px;
        font-size: 13px;
        color: #333333;
        margin: 0px 10%;
        border-radius: 6px;
        padding: 0px 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        img {
          width: 14px;
          height: 14px;
        }
      }
      .itemuser:hover {
        background: #eef1f9;
      }
    }
  }
}
</style>
