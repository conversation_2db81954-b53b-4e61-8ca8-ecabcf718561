@charset "utf-8";

* {
  margin: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  outline: 0;
}

html {
  -webkit-text-size-adjust: 100%;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-text-size-adjust: none;
  overflow-x: hidden;
  background-color: #f6f8f9;
}

img {
  border: 0;
  -ms-interpolation-mode: bicubic;
  vertical-align: middle;
  font-size: 0;
}

.windowLoading {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transform: translateY(-10%);
}

.windowLoading .loadingText {
  // font-family: Noto Sans SC;
  font-size: 18px;
  font-weight: 700;
  text-align: center;
  margin-top: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.windowLoading .loadingText .syname {
  color: #147ae3;
  margin: 0 4px;
}

.footcopy {
  position: fixed;
  bottom: 24px;
  height: 32px;
  width: 100%;
  background: url(~@/assets/login/logo_jykj.svg) no-repeat center;
  background-size: contain;
  opacity: 0.15;
}

.windowLoading .loader {
  width: 38vh;
  height: 38vh;
  position: absolute;
  opacity: 0.1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loader .face {
  position: absolute;
  border-radius: 50%;
  border-style: solid;
  animation: rotatet 1.6s linear normal infinite;
}

.loader .face:nth-child(1) {
  width: 100%;
  height: 100%;
  color: #0080fe;
  border-color: currentColor transparent transparent currentColor;
  border-width: 0.2em 0.2em 0em 0em;
  --deg: -40deg;
}

.loader .face:nth-child(2) {
  width: 80%;
  height: 80%;
  color: #3addba;
  border-color: currentColor currentColor transparent transparent;
  border-width: 0.2em 0em 0em 0.2em;
  --deg: -140deg;
  animation-direction: reverse;
}

.loader .face .circle {
  position: absolute;
  width: 50%;
  height: 0.25em;
  top: 50%;
  left: 50%;
  background-color: transparent;
  transform: rotate(var(--deg));
  transform-origin: left;
}

.loader .face .circle::before {
  position: absolute;
  top: -0.5em;
  right: -0.4em;
  content: '';
  width: 0.6em;
  height: 0.6em;
  background-color: currentColor;
  border-radius: 50%;
}

@keyframes rotatet {

  0%,
  100% {
    transform: perspective(100px) rotate(0deg);
  }

  40% {
    transform: perspective(150px) rotate(180deg);
  }

  60% {
    transform: perspective(150px) rotate(270deg);
  }

  100% {
    transform: perspective(100px) rotate(360deg);
  }
}

/* wu-loader */
.wu-loader {
  width: 72px;
  height: 72px;
  border-radius: 16px;
  position: relative;
  overflow: hidden;
}

.wu-loader .syimg {
  display: inline-block;
  vertical-align: middle;
  background-repeat: no-repeat;
  background-size: 72px 72px;
  float: left;
  width: 24px;
  height: 24px;
  animation: wu-cubeGridScaleDelay 1.6s infinite ease-in-out;
}

.wu-loader .wu-cube_1 {
  background-position: 0 0;
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}

.wu-loader .wu-cube_2 {
  background-position: -24px 0;
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}

.wu-loader .wu-cube_3 {
  background-position: -48px 0;
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s;
}

.wu-loader .wu-cube_4 {
  background-position: 0 -24px;
  -webkit-animation-delay: 0.1s;
  animation-delay: 0.1s;
}

.wu-loader .wu-cube_5 {
  background-position: -24px -24px;
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}

.wu-loader .wu-cube_6 {
  background-position: -48px -24px;
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}

.wu-loader .wu-cube_7 {
  background-position: 0 -48px;
  -webkit-animation-delay: 0s;
  animation-delay: 0s;
}

.wu-loader .wu-cube_8 {
  background-position: -24px -48px;
  -webkit-animation-delay: 0.1s;
  animation-delay: 0.1s;
}

.wu-loader .wu-cube_9 {
  background-position: -48px -48px;
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}

.loaddots {
  margin-left: 12px;
  width: 4px;
  aspect-ratio: 1;
  border-radius: 50%;
  animation: ratio 1s infinite linear alternate;
}

@keyframes wu-cubeGridScaleDelay {

  0%,
  70%,
  100% {
    -webkit-transform: scale3D(1, 1, 1);
    transform: scale3D(1, 1, 1);
  }

  35% {
    -webkit-transform: scale3D(0, 0, 1);
    transform: scale3D(0, 0, 1);
  }
}

@keyframes ratio {
  0% {
    box-shadow: 7px 0 #333, -7px 0 #0002;
    background: #333;
  }

  33% {
    box-shadow: 7px 0 #333, -7px 0 #0002;
    background: #0002;
  }

  66% {
    box-shadow: 7px 0 #0002, -7px 0 #333;
    background: #0002;
  }

  100% {
    box-shadow: 7px 0 #0002, -7px 0 #333;
    background: #333;
  }
}
