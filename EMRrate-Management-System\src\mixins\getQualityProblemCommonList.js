import { queryCodeValueContent } from "@/api/codeValueMgt/codeValueContent"
const mixin = {
  created() {
    this.getCodeValueContent("qstnClsfCdData", "qstnClsfCd")
    this.getCodeValueContent("qstnRsnClsfCdData", "qstnRsnClsfCd")
    this.getCodeValueContent("rctfctnSchemTypCdData", "rctfctnSchemTypCd")
  },
  data() {
    return {
      qstnClsfCdData: [], // 问题分类列表数据
      qstnRsnClsfCdData: [], // 问题原因列表数据
      rctfctnSchemTypCdData: [], // 整改方式列表数据
    }
  },
  methods: {
    // 获取码值内容
    async getCodeValueContent(dataName, param) {
      let { status, data } = await queryCodeValueContent({
        contentKey: "",
        typeCode: param,
        pageNum: 1,
        pageSize: 100,
      })
      if (status !== 0) {
        this.$message({
          message: res.msg,
          type: "error",
        })
        return
      }
      this[dataName] = data.list
    },
  },
}
export default mixin
