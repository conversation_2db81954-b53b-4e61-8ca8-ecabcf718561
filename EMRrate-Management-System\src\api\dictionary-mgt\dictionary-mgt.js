import request from '@/utils/request'

/* 1. 字典管理 */

// 查询字典管理列表
export function getDictManagementList(data) {
  return request({
    url: '/dictManager/getDictManagementList',
    method: 'post',
    data
  })
}
// 同步主数据系统数据
export function syncDictData(params) {
  return request({
    url: '/dictManager/syncDictData',
    method: 'get',
    params
  })
}
// 查询系统信息
export function getSysInfoList(params) {
  return request({
    url: '/dictManager/getSysInfoList',
    method: 'get',
    params
  })
}
