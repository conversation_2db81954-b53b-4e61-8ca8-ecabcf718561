<template>
  <div class="form">
    <el-form
      :model="editData"
      label-position="right"
      ref="ruleForm"
      label-width="100px"
      class="demo-ruleForm"
    >
      <el-row>
        <el-col :span="8">
          <el-form-item label="问题分类:" prop="qstnClsfCd">
            <el-radio-group v-model="editData.qstnClsfCd">
              <el-radio
                v-for="item in qstnClsfCdData"
                :disabled="isDisabled"
                :key="item.contentKey"
                :label="item.contentKey"
                >{{ item.contentValue }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="问题原因:" prop="qstnRsnClsfCd">
            <el-select
              v-model="editData.qstnRsnClsfCd"
              :disabled="isDisabled"
              clearable
            >
              <el-option
                v-for="item in qstnRsnClsfCdData"
                :key="item.id"
                :label="item.contentValue"
                :value="item.contentKey"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="整改方式:" prop="rctfctnSchemTypCd">
            <el-select
              v-model="editData.rctfctnSchemTypCd"
              :disabled="isDisabled"
              clearable
            >
              <el-option
                v-for="item in rctfctnSchemTypCdData"
                :key="item.id"
                :label="item.contentValue"
                :value="item.contentKey"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="整改意见:" prop="rctfctnOpinions">
        <el-input
          type="textarea"
          :disabled="isDisabled"
          v-model="editData.rctfctnOpinions"
        ></el-input>
      </el-form-item>

      <el-form-item
        v-if="btnType !== '1'"
        label="推送接收方:"
        prop="pushMessageReceivers"
      >
        <el-row
          v-for="(item, index) in editData.pushMessageReceivers"
          :key="index"
        >
          <el-col :span="4">
            <el-radio-group :disabled="isDisabled" v-model="item.accountType">
              <el-radio label="0">邮箱</el-radio>
              <el-radio label="1">短信</el-radio>
            </el-radio-group>
          </el-col>
          <el-col :span="12" style="padding-left: 20px">
            <el-input :disabled="isDisabled" v-model="item.account"></el-input>
          </el-col>
          <el-col :span="8" style="padding-left: 20px; margin-bottom: 10px">
            <el-button
              v-if="
                editData.pushMessageReceivers.length - 1 === index ||
                editData.pushMessageReceivers.length === 0
              "
              :disabled="isDisabled"
              type="primary"
              @click="addOrdeleteReceiver()"
              >新增</el-button
            >
            <el-button
              v-if="editData.pushMessageReceivers.length > 1"
              :disabled="isDisabled"
              type="danger"
              @click="addOrdeleteReceiver(index)"
              >删除</el-button
            >
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>
    <div v-if="btnType === '5'" class="operation-record">
      <span>操作记录：</span>
      <div class="block">
        <el-timeline :reverse="true">
          <el-timeline-item
            v-for="(activity, index) in editData.dataqulityQstnOperationRecords"
            :color="
              index === editData.dataqulityQstnOperationRecords.length - 1
                ? '#0f0'
                : ''
            "
            :key="index"
          >
            <span>{{ activity.operationContent }}</span>
            <span style="margin-left: 20px; color: #999">{{
              "操作人：" + activity.operationPerson
            }}</span>
            <span style="margin-left: 20px; color: #999">{{
              activity.operationTime
            }}</span>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>
  </div>
</template>

<script>
import getQualityProblemCommonList from "@/mixins/getQualityProblemCommonList"
export default {
  mixins: [getQualityProblemCommonList],
  methods: {
    // 新增或删除推送接收方
    addOrdeleteReceiver(index) {
      if (index === undefined) {
        this.editData.pushMessageReceivers.push({
          account: "",
          accountType: "",
        })
        return
      }
      this.editData.pushMessageReceivers.splice(index, 1)
    },
  },
  props: {
    editData: {
      type: Object,
    },
    isDisabled: {
      type: Boolean,
    },
    btnType: {
      type: String,
    },
  },
}
</script>

<style lang="scss" scoped>
.form {
  padding-right: 30px;

  .operation-record {
    display: flex;
    margin: 50px 0 0 26px;

    > span {
      white-space: nowrap;
    }
  }
}
</style>
