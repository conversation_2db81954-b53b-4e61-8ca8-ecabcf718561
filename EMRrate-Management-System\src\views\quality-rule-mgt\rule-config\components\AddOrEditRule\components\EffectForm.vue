<template>
  <div>
    <!-- 代码有效性 -->
    <template v-if="formData.checkRuleType === 'DMYXX'">
      <el-form-item label="检测表或视图" prop="checkRuleTableOrView">
        <el-autocomplete
          ref="checkRuleTableOrView"
          v-model="formData.checkRuleTableOrView"
          :fetch-suggestions="querySearchAsync"
          value-key="tableOrViewName"
          @select="handlerTableOrViewSelect($event, 'checkRuleTableOrView')"
          placeholder="请输入内容"
          clearable
        ></el-autocomplete>
      </el-form-item>

      <el-form-item label="字典表或视图" prop="stCheckRuleTableOrView">
        <el-autocomplete
          ref="stCheckRuleTableOrView"
          v-model="formData.stCheckRuleTableOrView"
          :fetch-suggestions="querySearchDictAsync"
          value-key="mappingTabal"
          @select="handlerTableOrViewSelect($event, 'dictTableOrView')"
          placeholder="请输入内容"
          clearable
        ></el-autocomplete>
      </el-form-item>

      <el-form-item label="检测字段" prop="checkRuleColumn">
        <el-input
          :disabled="isCheckRuleColumnDisabled"
          @click.native="handlerclick('checkRuleColumn')"
          v-model="formData.checkRuleColumn"
        >
          <el-button slot="append">
            <SelectFieldList
              type="checkRuleColumn"
              selectType="radio"
              @click.native.stop="handlerclick('checkRuleColumn')"
              :isDisabled="isCheckRuleColumnDisabled"
              :checkRuleColumnData="checkRuleColumnData"
              @backfillSelectedData="backfillSelectedData"
            />
          </el-button>
        </el-input>
      </el-form-item>
      <el-form-item label="字典字段" prop="stCheckRuleColumn">
        <el-input
          :disabled="isDictDisabled"
          @click.native="handlerclick('stCheckRuleColumn')"
          v-model="formData.stCheckRuleColumn"
        >
          <el-button slot="append">
            <SelectFieldList
              type="dictTableFiled"
              selectType="radio"
              @click.native.stop="handlerclick('stCheckRuleColumn')"
              :isDisabled="isDictDisabled"
              :dictTableFiledData="dictTableFiledData"
              @backfillSelectedData="backfillSelectedData"
            />
          </el-button>
        </el-input>
      </el-form-item>
      <el-form-item label="映射类型" prop="cdVal">
        <el-select v-model="formData.cdVal" placeholder="请选择映射类型">
          <!-- <el-option label="全部" value=""></el-option> -->
          <el-option label="互联互通字典映射" value="1"></el-option>
          <el-option label="院内字典映射" value="2"></el-option>
          <el-option label="三医监管" value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="明细表WHERE条件" prop="checkRuleWhere">
        <el-input type="textarea" v-model="formData.checkRuleWhere"></el-input>
      </el-form-item>
      <el-form-item label="问题明细显示字段" prop="pbSubsidiaryColumns">
        <el-input
          :disabled="isDisabled"
          @click.native="handlerclick"
          v-model="formData.pbSubsidiaryColumns"
        >
          <el-button slot="append">
            <SelectFieldList
              type="pbSubsidiaryColumns"
              selectType="checkBox"
              @click.native.stop="handlerclick"
              :isDisabled="isDisabled"
              :checkRuleColumnData="checkRuleColumnData"
              @backfillSelectedData="backfillSelectedData"
            />
          </el-button>
        </el-input>
      </el-form-item>
      <slot></slot>
    </template>
    <!-- 范围有效性 -->
    <template v-if="formData.checkRuleType === 'FWYXX'">
      <el-form-item label="检测表或视图" prop="checkRuleTableOrView">
        <el-autocomplete
          ref="checkRuleTableOrView"
          v-model="formData.checkRuleTableOrView"
          :fetch-suggestions="querySearchAsync"
          value-key="tableOrViewName"
          @select="handlerTableOrViewSelect($event, 'checkRuleTableOrView')"
          placeholder="请输入内容"
          clearable
        ></el-autocomplete>
      </el-form-item>
      <el-form-item label="检测字段" prop="checkRuleColumn">
        <el-input
          :disabled="isCheckRuleColumnDisabled"
          @click.native="handlerclick('checkRuleColumn')"
          v-model="formData.checkRuleColumn"
        >
          <el-button slot="append">
            <SelectFieldList
              type="checkRuleColumn"
              selectType="radio"
              @click.native.stop="handlerclick('checkRuleColumn')"
              :isDisabled="isCheckRuleColumnDisabled"
              :checkRuleColumnData="checkRuleColumnData"
              @backfillSelectedData="backfillSelectedData"
            />
          </el-button>
        </el-input>
      </el-form-item>
      <el-form-item label="监测类型" prop="checkObjectType">
        <el-radio-group v-model="formData.checkObjectType">
          <el-radio label="0">数值</el-radio>
          <el-radio label="1">日期</el-radio>
          <el-radio label="2">字符串</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-row>
        <el-col :span="16">
          <div>
            <el-form-item label="上限值" prop="maxValue">
              <el-input v-model="formData.maxValue"></el-input>
            </el-form-item>
          </div>
        </el-col>
        <el-col :span="2">
          <div style="margin-left: -100px">
            <el-form-item prop="maxValueContain">
              <el-checkbox
                true-label="0"
                false-label="1"
                v-model="formData.maxValueContain"
                >包含</el-checkbox
              >
            </el-form-item>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="16">
          <div>
            <el-form-item label="下限值" prop="minValue">
              <el-input v-model="formData.minValue"></el-input>
            </el-form-item>
          </div>
        </el-col>
        <el-col :span="2">
          <div style="margin-left: -100px">
            <el-form-item prop="minValueContain">
              <el-checkbox
                true-label="0"
                false-label="1"
                v-model="formData.minValueContain"
                >包含</el-checkbox
              >
            </el-form-item>
          </div>
        </el-col>
      </el-row>
      <el-form-item label="明细表WHERE条件" prop="checkRuleWhere">
        <el-input type="textarea" v-model="formData.checkRuleWhere"></el-input>
      </el-form-item>
      <el-form-item label="问题明细显示字段" prop="pbSubsidiaryColumns">
        <el-input
          :disabled="isDisabled"
          @click.native="handlerclick"
          v-model="formData.pbSubsidiaryColumns"
        >
          <el-button slot="append">
            <SelectFieldList
              type="pbSubsidiaryColumns"
              selectType="checkBox"
              @click.native.stop="handlerclick"
              :isDisabled="isDisabled"
              :checkRuleColumnData="checkRuleColumnData"
              @backfillSelectedData="backfillSelectedData"
            />
          </el-button>
        </el-input>
      </el-form-item>
      <slot></slot>
    </template>
    <!-- 自定义有效性 -->
    <template v-if="formData.checkRuleType === 'ZDYYXX'">
      <CustomCheckRuleType :formData.sync="formData" />
    </template>
  </div>
</template>


<script>
import tableViewAndField from "@/mixins/tableViewAndField"
import CustomCheckRuleType from "@/components/CustomCheckRuleType/index.vue"
import SelectFieldList from "./common/SelectFieldList.vue"
import {
  getDictTable,
  getDictTableFiled,
} from "@/api/qualityRuleMgt/ruleConfig"
export default {
  data() {
    return {
      isCheckRuleColumnDisabled: true,
      isStCheckRuleColumnDisabled: true,
      isDictDisabled: true,
      dictTableData: [], // 字典中的表数据
      dictTableFiledData: [], // 字典表字段数据
    }
  },
  components: {
    CustomCheckRuleType,
    SelectFieldList,
  },
  mixins: [tableViewAndField],
  props: {
    formData: {
      type: Object,
    },
    dataSourceId: {
      type: Number,
    },
  },
  mounted() {
    this.queryCheckRuleColumnData.tableName = this.formData.checkRuleTableOrView
    this.queryCheckRuleColumnData.dataSourceId = this.dataSourceId
    this.getCheckRuleColumnData()
    this.getDictTableFiled()
  },
  watch: {
    formData: {
      handler(val) {
        // 只要表单有值变化就清空获取的SQL数据
        this.$emit("clearCheckSQLData")
        // 检测表或视图
        if (val.checkRuleTableOrView) {
          this.isDisabled = false
          this.isCheckRuleColumnDisabled = false
        } else {
          this.clearPartData("checkRuleTableOrView")
        }
        // 字典表或视图
        if (val.stCheckRuleTableOrView) {
          this.isDictDisabled = false
        } else {
          this.clearPartData("dictTableOrView")
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    //handlerClear
    clearPartData(type) {
      if (type === "checkRuleTableOrView") {
        this.isDisabled = true
        this.isCheckRuleColumnDisabled = true
        this.checkRuleColumnData = []
        this.formData.checkRuleColumn = ""
        this.formData.pbSubsidiaryColumns = ""
      } else if (type === "dictTableOrView") {
        this.isDictDisabled = true
        this.dictTableFiledData = []
        this.formData.stCheckRuleColumn = ""
      }
    },
    // 先选择表或视图
    handlerclick(type) {
      if (this.isCheckRuleColumnDisabled && type === "checkRuleColumn") {
        this.$message({
          type: "warning",
          message: "请先选择主表或视图",
        })
        this.$refs["checkRuleTableOrView"].focus()
      } else if (
        this.isStCheckRuleColumnDisabled &&
        type === "stCheckRuleColumn"
      ) {
        this.$message({
          type: "warning",
          message: "请先选择副表或视图",
        })
        this.$refs["stCheckRuleTableOrView"].focus()
      } else if (this.isDisabled) {
        this.$message({
          type: "warning",
          message: "请先选择主表或视图",
        })
        this.$refs["checkRuleTableOrView"].focus()
      }
    },
    // 当主表或视图或副表或视图选择时
    handlerTableOrViewSelect(e, type) {
      if (type === "checkRuleTableOrView") {
        this.queryCheckRuleColumnData.tableName = e.tableOrViewName
        this.queryCheckRuleColumnData.dataSourceId = this.dataSourceId
        this.getCheckRuleColumnData()
      } else if (type === "dictTableOrView") {
        this.getDictTableFiled()
      }
    },
    // 获取字典表字段
    getDictTableFiled() {
      getDictTableFiled({
        pageNum: 1,
        pageSize: 9999,
        fieldName: "",
      }).then((res) => {
        if (res.status === 0) {
          let dictTableFiledDataArr = res.data.list
          dictTableFiledDataArr.forEach((element, index) => {
            element.id = index
          })
          this.dictTableFiledData = dictTableFiledDataArr
        }
      })
    },
    // 异步搜索 字典表或视图的表
    querySearchDictAsync(queryString, cb) {
      this.dictTableData = []
      getDictTable({
        pageNum: 1,
        pageSize: 9999,
        tableName: queryString,
      }).then((res) => {
        if (res.status === 0) {
          res.data.list.forEach((element, index) => {
            this.dictTableData.push({
              id: index,
              mappingTabal: element.mappingTabal,
            })
          })
          cb(this.dictTableData)
        }
      })
    },
    backfillSelectedData(val, type) {
      if (type === "checkRuleColumn") {
        this.formData.checkRuleColumn = val.join(",")
      } else if (type === "pbSubsidiaryColumns") {
        this.formData.pbSubsidiaryColumns = val.join(",")
      } else if (type === "dictTableFiled") {
        this.formData.stCheckRuleColumn = val.join(",")
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.selected {
  margin-bottom: 20px;
}
.el-form-item .el-form-item {
  display: flex;
  flex-direction: column;
  text-align: center;
}
</style>
