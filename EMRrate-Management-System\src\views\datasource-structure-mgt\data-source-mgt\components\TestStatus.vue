<template>
  <div
    class="testStatus"
    :style="{ backgroundColor: bgc }">
    <div
      v-if="testResultStatus == -1"
      class="text-tip">
      点击测试连接数据库
    </div>
    <template v-else>
      <div
        v-show="testResultStatus == 0"
        class="loading">
        <i class="el-icon-loading"></i><span>连接数据源...</span>
      </div>
      <div
        v-show="testResultStatus == 1"
        class="fail">
        <i class="el-icon-error"></i>
        <div class="result">
          <div>连接失败</div>
          <div>{{ testFailMsg }}</div>
        </div>
      </div>
      <div
        v-show="testResultStatus == 2"
        class="success">
        <i class="el-icon-success"></i><span>连接成功</span>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  props: ['testResultStatus', 'testFailMsg'],
  computed: {
    bgc() {
      const colors = ['#f8f8f8', '#fff0f0', '#e4fcea', '#f8f8f8']
      return colors[this.testResultStatus]
    }
  }
}
</script>

<style lang="scss" scoped>
.testStatus {
  height: 100%;
  padding: 10px;
  background-color: #f8f8f8;
  position: relative;
  overflow: auto;
  .loading {
    color: #395bd5;
  }
  .fail {
    color: red;
    display: flex;

    .result {
      div {
        line-height: 1.5;
      }
    }
  }
  .success {
    color: #3bc15b;
  }
  i {
    font-size: 18px;
    vertical-align: middle;
    margin-right: 6px;
  }
  .text-tip {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 14px;
    color: #b6b6b6;
    text-align: center;
  }
}
</style>
