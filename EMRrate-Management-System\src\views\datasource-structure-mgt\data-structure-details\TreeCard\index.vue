<template>
  <div class="tree-card-container">
    <div class="title">{{ title }}</div>
    <div
      class="content"
      :style="{ width: widthSize }">
      <slot />
    </div>
  </div>
</template>
<script>
export default {
  props: {
    title: {
      type: String,
      default: '标题'
    },
    widthSize: {
      type: String,
      default: 'auto'
    }
  }
}
</script>
<style lang="scss" scoped>
.tree-card-container {
  position: relative;
  .title {
    box-sizing: border-box;
    position: absolute;
    top: -30px;
    left: 0;
    min-width: 160px;
    height: 36px;
    background-color: pink;
    background: url('~@/assets/tree/tree_card_title_bg_01.jpg') left top
        no-repeat,
      url('~@/assets/tree/tree_card_title_bg_03.jpg') right top no-repeat,
      url('~@/assets/tree/tree_card_title_bg_02.jpg') center no-repeat;
    text-align: center;
    z-index: 0;
    line-height: 30px;
    padding-left: 20px;
    padding-right: 40px;
    font-size: 14px;
    font-weight: 600;
    color: #333;
    overflow: hidden;
    &::before {
      content: '';
      position: absolute;
      width: 800px;
      height: 4.6px;
      bottom: 0px;
      left: 1.6px;
      background-color: white;
      z-index: 1;
    }
  }
  .content {
    overflow-y: scroll;
    overflow-x: scroll;
    border: 1.6px solid #e7eaec;
    border-radius: 8px;
    border-top-left-radius: 0;
    height: 500px;
    padding-top: 20px;
  }
}
</style>
