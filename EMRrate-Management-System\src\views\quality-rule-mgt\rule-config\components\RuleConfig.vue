<template>
  <div class="rule-config-container">
    <el-dialog
      v-dialogDrag
      title="规则配置"
      :visible.sync="dialogFormVisible"
      @open="handlerOpen"
      @closed="handlerClosed"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="rule-config-main">
        <div class="tips">
          <span>系统名称：{{ row.sysName }}</span>
          <span>数据库名称：{{ row.databaseName }}</span>
          <el-divider></el-divider>
        </div>
        <div class="rule-config-header">
          <el-form :model="queryData" ref="queryData" :inline="true">
            <el-form-item label="规则大类" prop="checkRuleFatherType">
              <el-select
                v-model="queryData.checkRuleFatherType"
                @change="handlerChange"
              >
                <el-option label="全部" value=""></el-option>
                <el-option
                  v-for="item in checkRuleFatherTypeData"
                  :key="item.id"
                  :label="item.contentValue"
                  :value="item.contentKey"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="规则小类" prop="checkRuleType">
              <el-select v-model="queryData.checkRuleType" placeholder="请选择">
                <el-option label="全部" value=""></el-option>
                <el-option
                  v-for="item in checkRuleTypeData"
                  :key="item.id"
                  :label="item.contentValue"
                  :value="item.contentKey"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="问题分类" prop="qstType">
              <el-select v-model="queryData.qstType">
                <el-option label="全部" value=""></el-option>
                <el-option
                  v-for="item in problemTypeData"
                  :key="item.id"
                  :label="item.contentValue"
                  :value="item.contentValue"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="检测表或视图" prop="checkRuleTableOrView">
              <el-autocomplete
                ref="abc"
                v-model="queryData.checkRuleTableOrView"
                :fetch-suggestions="querySearchAsync"
                value-key="tableOrViewName"
                placeholder="请输入内容"
                @select="handlerTableOrViewSelect"
                clearable
              ></el-autocomplete>
            </el-form-item>

            <el-form-item label="检测字段" prop="checkRuleColumn">
              <el-select
                v-model="queryData.checkRuleColumn"
                placeholder="请选择"
                :disabled="isDisabled"
                @click.native="handlerclick"
                filterable
              >
                <el-option label="全部" value=""></el-option>
                <el-option
                  v-for="item in checkRuleColumnData"
                  :key="item.id"
                  :label="item.fieldName"
                  :value="item.fieldName"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="规则名称" prop="checkRuleName">
              <el-input
                v-model="queryData.checkRuleName"
                placeholder="请输入规则名称"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                @click="searchConfigPageList"
                icon="el-icon-search"
                >搜索</el-button
              >
              <el-button
                type="primary"
                icon="el-icon-plus"
                @click="handleShowDialog(1)"
                >新增
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <div class="rule-config-table">
          <el-table
            :data="tableData"
            style="width: 100%"
            border
            v-loading="loading"
            :header-cell-style="{ background: '#F5F7FA', color: '#606266' }"
          >
            <el-table-column prop="checkRuleFatherType" label="规则大类">
              <template slot-scope="scope">
                <span
                  v-for="item in checkRuleFatherTypeData"
                  :key="item.contentKey"
                >
                  <span
                    v-if="scope.row.checkRuleFatherType === item.contentKey"
                  >
                    {{ item.contentValue }}
                  </span>
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="checkRuleType" label="规则小类" width="100">
              <template slot-scope="scope">
                <span v-for="item in checkRuleTypeData" :key="item.contentKey">
                  <span v-if="scope.row.checkRuleType == item.contentKey">
                    {{ item.contentValue }}
                  </span>
                </span>
              </template>
            </el-table-column>
            <el-table-column
              prop="checkRuleId"
              label="规则编号"
            ></el-table-column>
            <el-table-column
              prop="checkRuleName"
              label="规则名称"
            ></el-table-column>
            <el-table-column
              prop="checkRuleDesc"
              label="规则说明"
            ></el-table-column>
            <el-table-column prop="qstType" label="问题分类"></el-table-column>
            <el-table-column
              width="200"
              prop="checkRuleTableOrView"
              label="检测表名"
            ></el-table-column>
            <el-table-column
              width="200"
              prop="checkRuleColumn"
              label="检测字段名"
            >
            </el-table-column>
            <el-table-column prop="checkRuleStatus" label="是否启用">
              <template slot-scope="scope">
                <span v-if="scope.row.checkRuleStatus === '0'">是</span>
                <span v-else>否</span>
              </template>
            </el-table-column>
            <el-table-column
              width="200"
              prop="createDate"
              label="创建时间"
            ></el-table-column>
            <el-table-column
              width="200"
              prop="updateDate"
              label="更新时间"
            ></el-table-column>
            <el-table-column label="操作" width="400" fixed="right">
              <template slot-scope="scope">
                <el-button
                  v-if="scope.row.recodeCount > 0"
                  size="mini"
                  type="primary"
                  icon="el-icon-view"
                  @click="handleShowDialog(3, scope.row)"
                  >查看</el-button
                >
                <el-button
                  v-else
                  size="mini"
                  type="primary"
                  icon="el-icon-edit-outline"
                  @click="handleShowDialog(0, scope.row)"
                  >编辑</el-button
                >
                <el-button
                  size="mini"
                  type="primary"
                  icon="el-icon-document-copy"
                  @click="handleShowDialog(2, scope.row)"
                  >复制</el-button
                >
                <el-button
                  v-if="scope.row.checkRuleStatus === '1'"
                  size="mini"
                  type="success"
                  @click="enableOrNot(scope.row)"
                  >启用</el-button
                >
                <el-button
                  v-else
                  size="mini"
                  type="danger"
                  @click="enableOrNot(scope.row)"
                  >禁用</el-button
                >
                <el-button
                  size="mini"
                  type="danger"
                  icon="el-icon-delete"
                  @click="deleteRule(scope.$index, scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="rule-config-pag">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryData.pageNum"
          :page-sizes="[5, 10, 15, 20]"
          :page-size="queryData.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalNum"
        >
        </el-pagination>
      </div>
    </el-dialog>
    <!-- 新增/编辑规则 -->
    <AddOrEditRule
      ref="addOrEditRule"
      :problemTypeData="problemTypeData"
      :btnType="btnType"
      v-bind="row"
      :row1="row1"
    />
  </div>
</template>

<script>
import tableViewAndField from "@/mixins/tableViewAndField"
import getCodeValueContent from "@/mixins/getCodeValueContent"
import {
  queryConfigPage,
  deleteRule,
  updateRuleStatus,
} from "@/api/qualityRuleMgt/ruleConfig"
import AddOrEditRule from "./AddOrEditRule/index.vue"
import { queryCodeValueContent } from "@/api/codeValueMgt/codeValueContent"
export default {
  components: {
    AddOrEditRule,
  },
  mixins: [tableViewAndField, getCodeValueContent],
  data() {
    return {
      dialogFormVisible: false,
      queryData: {
        // 查询数据
        checkRuleColumn: "", // 检核字段
        checkRuleFatherType: "", // 检核规则父类型，完整性，一致性，准确性，有效性，规范性，及时性
        checkRuleName: "", // 检核规则名称
        checkRuleTableOrView: "", // 	检查表或视图
        checkRuleType: "", // 检核规则类型，整体完整性，条件完整性，一致性，唯一性，计算正确性，代码有效性，范围有效性，长度规范性，及时性，自定义（每个大类一个）
        dbNm: "", // 数据库名称
        pageNum: 1,
        pageSize: 10,
        qstType: "", // 问题分类编码：提示，预警，严重
        sysCode: "", // 检核系统代码
        sysName: "", // 检核系统名称
      },
      btnType: 1, // 1 代表新增 0 代表编辑
      queryCheckRuleFatherTypeData: {
        // 查询规则大类列表数据
        contentKey: "",
        typeCode: "checkRuleFatherType",
      },
      queryCheckRuleTypeData: {
        typeCode: "checkRuleType",
        contentKey: "",
        pageNum: 1,
        pageSize: 100,
      },
      row1: {},
      checkRuleFatherTypeData: [], // 规则大类列表数据
      checkRuleTypeData: [], // 规则小类列表数据
      tableData: [], // 表格数据
      problemTypeData: [], // 问题分类数据
      totalNum: 1,
      loading: false,
    }
  },
  props: {
    row: {
      type: Object,
    },
  },
  watch: {
    queryData: {
      handler(newVal) {
        if (!newVal.checkRuleTableOrView) {
          this.isDisabled = true
          this.queryData.checkRuleColumn = ""
        }
      },
      deep: true,
    },
  },
  methods: {
    // 当diolog打开时渲染表格数据及下拉数据
    handlerOpen() {
      this.queryData.dbNm = this.row.databaseName
      this.queryData.sysCode = this.row.sysCode
      this.queryData.sysName = this.row.sysName
      // 初始化已关联子任务表格数据
      this.getCheckRuleFatherType()
      this.getCheckRuleType()
      this.getConfigPageList()
      // 获取问题分类字典数据
      this.getCodeValueContent("problemTypeData", "problemType")
    },

    // 启用或禁用
    enableOrNot(row) {
      this.$confirm(
        `此操作将${
          row.checkRuleStatus === "1" ? `启用` : "禁用"
        }规则, 是否继续?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          updateRuleStatus({
            checkRuleId: row.checkRuleId,
            checkRuleStatus: row.checkRuleStatus === "1" ? "0" : "1",
          }).then((res) => {
            if (res.status !== 0) {
              this.$message({
                message: res.msg,
                type: "error",
              })
              return
            }
            this.$message({
              type: "success",
              message: `${row.checkRuleStatus === "1" ? `启用` : "禁用"}成功`,
            })
            this.getConfigPageList()
          })
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消操作",
          })
        })
    },
    // 查询配置页面列表
    getConfigPageList() {
      this.loading = true
      queryConfigPage(this.queryData).then((res) => {
        if (res.status === 0) {
          this.tableData = res.data.list
          this.totalNum = res.data.total
          this.loading = false
        }
      })
    },
    // 删除单条规则配置
    deleteRule(index, row) {
      this.$confirm("此操作将删除规则, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteRule({ checkRuleId: row.checkRuleId }).then((res) => {
            if (res.status !== 0) {
              this.$message({
                message: res.msg,
                type: "error",
              })
              return
            }
            this.$message({
              type: "success",
              message: "删除成功",
            })
            this.getConfigPageList()
          })
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          })
        })
    },
    // 获取规则大类
    getCheckRuleFatherType() {
      queryCodeValueContent(this.queryCheckRuleFatherTypeData).then((res) => {
        if (res.status === 0) {
          this.checkRuleFatherTypeData = res.data.list
        }
      })
    },
    // 获取规则小类
    getCheckRuleType() {
      queryCodeValueContent(this.queryCheckRuleTypeData).then((res) => {
        if (res.status === 0) {
          this.checkRuleTypeData = res.data.list
        }
      })
    },
    // 当规则大类进行选择时
    handlerChange(val) {
      if (val == "") {
        this.queryCheckRuleTypeData.typeCode = "checkRuleType"
        this.getCheckRuleType()
      } else {
        this.queryCheckRuleTypeData.typeCode = val
        this.getCheckRuleType()
      }
      this.queryData.checkRuleType = ""
    },
    // 先选择表或视图
    handlerclick() {
      if (this.isDisabled) {
        this.$message({
          type: "warning",
          message: "请先选择检测表或视图内容",
        })
        this.$refs["abc"].focus()
      }
    },
    // 当当前系统和数据库的表选择时
    handlerTableOrViewSelect(val) {
      this.isDisabled = false
      this.queryCheckRuleColumnData.tableName = val.tableOrViewName
      this.queryCheckRuleColumnData.dataSourceId = this.row
        ? this.row.dataSourceId
        : this.dataSourceId
      this.getCheckRuleColumnData()
    },
    // 搜索规则配置界面列表
    searchConfigPageList() {
      this.getConfigPageList()
    },
    // 改变页面显示条数
    handleSizeChange(val) {
      this.queryData.pageSize = val
      this.getConfigPageList()
    },
    // 改变页数
    handleCurrentChange(val) {
      this.queryData.pageNum = val
      this.getConfigPageList()
    },
    // 处理打开Dialog
    handleShowDialog(index, row) {
      this.btnType = index
      if (index !== 1) {
        this.row1 = JSON.parse(JSON.stringify(row))
        if (index === 2) {
          this.row1.checkRuleId && (this.row1.checkRuleId = "")
        }
      }
      this.$refs.addOrEditRule.dialogFormVisible = true
    },
    // Dialog 关闭动画结束时的回调
    handlerClosed() {
      this.$refs["queryData"].resetFields()
      this.isDisabled = true
      this.tableData = []
      this.checkRuleTableOrViewData = []
    },
  },
}
</script>

<style lang="scss" scoped>
.rule-config-container {
  .rule-config-main {
    .rule-config-header {
      .button {
        margin-left: 20px;
      }
    }

    margin: 10px;

    .rule-config-table {
      margin-top: 20px;
    }

    .tips {
      margin-bottom: 30px;

      > span {
        margin-right: 90px;
        color: rgba($color: #000000, $alpha: 0.6);
        font-size: 14px;
      }
    }
  }
}

::v-deep .el-dialog .el-dialog__body {
  height: 78vh;
  overflow: auto;
}

::v-deep .el-dialog {
  top: -60px;
}
</style>
