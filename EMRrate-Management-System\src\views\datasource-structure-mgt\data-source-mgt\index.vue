<template>
  <MainCard>
    <div class="source-mgt-container">
      <HeaderSearch>
        <template v-slot:left>
          <el-button
            type="primary"
            @click="handlerAddClick"
            >添加数据源</el-button
          >
        </template>
        <template v-slot:right>
          <el-form
            :model="queryData"
            ref="ruleForm"
            inline>
            <el-form-item
              label="数据源名称"
              prop="dataSourceName">
              <el-input
                v-model="queryData.dataSourceName"
                placeholder="请输入数据源名称"></el-input>
            </el-form-item>
            <el-form-item
              label="数据库类型"
              prop="databaseType">
              <el-select
                v-model="queryData.databaseType"
                clearable
                placeholder="-请选择-">
                <el-option
                  v-for="item in databaseTypeData"
                  :key="item.id"
                  :label="item.contentValue"
                  :value="item.contentKey"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                @click="searchDataSource"
                >查询</el-button
              >
            </el-form-item>
            <!-- <el-form-item>
            <el-button
              type="danger"
              @click="deleteDataSource"
              icon="el-icon-delete"
              >删除数据源</el-button
            >
          </el-form-item> -->
            <el-form-item>
              <el-button @click="resetForm('ruleForm')">重置</el-button>
            </el-form-item>
          </el-form>
        </template>
      </HeaderSearch>
      <div class="source-mgt-main">
        <div class="source-mgt-table">
          <el-table
            :data="tableData"
            ref="sourceMgtTable"
            style="width: 100%"
            v-loading="loading"
            :header-cell-style="{ background: '#fff', color: '#606266' }">
            <!-- <el-table-column
              fixed="left"
              type="selection"
              width="55">
            </el-table-column> -->
            <el-table-column
              prop="dataSourceId"
              width="100"
              v-if="true"
              label="数据源ID">
              <template slot-scope="scope">
                <el-link
                  type="primary"
                  @click="
                    $router.push({
                      name: 'Data-structure-details',
                      params: {
                        dataSourceId: scope.row.dataSourceId,
                        dataSourceName: scope.row.dataSourceName
                      }
                    })
                  "
                  >{{ scope.row.dataSourceId }}</el-link
                >
              </template>
            </el-table-column>
            <el-table-column
              prop="dataSourceName"
              label="数据源名称">
              <template slot-scope="scope">
                <div>{{ scope.row.dataSourceName }}</div>
                <div style="color: #a6a6a6">
                  描述：{{ scope.row.dataSourceDescribe }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="databaseName"
              label="采集数据库名称">
            </el-table-column>
            <el-table-column
              prop="databaseType"
              label="数据库类型">
            </el-table-column>
            <el-table-column label="数据库链接">
              <template slot-scope="scope">
                <div
                  class="dataBaseLink"
                  @click="handleShowDialog(scope.row, 2)">
                  <svg-icon
                    icon-class="icon_url"
                    class="icon_url"
                    style="font-size: 60px" />
                </div>
              </template>
            </el-table-column>
            <el-table-column label="结构设置">
              <template slot-scope="scope">
                <div
                  class="dataBaseLink"
                  @click="handleShowDialog(scope.row, 3)">
                  <div
                    v-if="scope.row.configNum != 0"
                    class="structure-set">
                    <svg-icon
                      icon-class="icon_scale3"
                      class="icon_url"
                      style="font-size: 14px; margin-right: 6px" />
                    <span>打开</span>
                  </div>
                  <div
                    v-else
                    class="not-config">
                    未配置 &gt;
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="updateTime"
              label="最新更新时间">
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              width="220">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  @click="handleShowDialog(scope.row, 0)"
                  >编辑</el-button
                >
                <span style="margin: 0 2px">
                  <el-divider direction="vertical"></el-divider>
                </span>
                <el-button
                  type="text"
                  @click="deleteDataSource(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="source-mgt-pag">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="queryData.pageNum"
            :page-sizes="[5, 10, 15, 20]"
            :page-size="queryData.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalNum">
          </el-pagination>
        </div>
      </div>
      <!-- 配置数据源 -->
      <ConfigDataSourceDialog
        ref="configDataSourceDialog"
        :btnType="btnType"
        @updatedataSourceList="queryDataSourceList"
        :databaseTypeData="databaseTypeData"
        :row="row" />
      <!-- 结构设置 -->
      <StructureConfig
        v-if="isShowConfig"
        @dialogChange="handleDialogChange"
        ref="structureConfig"
        :row="row" />
    </div>
  </MainCard>
</template>

<script>
import {
  queryDataSourceList,
  deleteDataSource
} from '@/api/dataSourceStructureMgt/dataSourceMgt'
import ConfigDataSourceDialog from './components/ConfigDataSourceDialog.vue'
import StructureConfig from './components/StructureConfig.vue'
import getCodeValueConten from '@/mixins/getCodeValueContent'
export default {
  mixins: [getCodeValueConten],
  data() {
    return {
      queryData: {
        // 查询数据
        dataSourceName: '',
        databaseType: '',
        pageNum: 1,
        pageSize: 10
      },
      isShowConfig: false, // 结构配置页面是否销毁，节省性能
      btnType: 1, // 1 代表新增 0 代表编辑 2代表数据库链接 3代表结构设置
      tableData: [], // 表格数据
      totalNum: 1,
      loading: false,
      env: process.env.NODE_ENV, // 环境变量
      databaseTypeData: [],
      dataSourceIds: [], // 需要删除的数据源的ID
      row: {} // 点击编辑或结构设置时整行的数据
    }
  },
  components: {
    ConfigDataSourceDialog,
    StructureConfig
  },
  created() {
    // 进入页面初始化查询
    this.queryDataSourceList()
    this.getCodeValueContent('databaseTypeData', 'DBType')
  },
  methods: {
    // 查询数据源列表
    queryDataSourceList() {
      this.loading = true
      queryDataSourceList(this.queryData).then((res) => {
        if (res.status === 0) {
          this.tableData = res.data.list
          this.totalNum = res.data.total
          this.loading = false
        }
      })
    },
    // 结构配置弹窗隐藏时
    handleDialogChange() {
      this.isShowConfig = false
    },
    handlerAddClick() {
      this.btnType = 1
      this.$refs.configDataSourceDialog.dialogFormVisible = true
    },
    // 搜索任务组
    searchDataSource() {
      this.queryDataSourceList()
    },
    // 改变页面显示条数
    handleSizeChange(val) {
      this.queryData.pageSize = val
      this.queryDataSourceList()
    },
    // 改变页数
    handleCurrentChange(val) {
      this.queryData.pageNum = val
      this.queryDataSourceList()
    },
    // 重置
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.queryDataSourceList()
    },
    // 驱动文件上传成功时
    handleSuccess(res, file, fileList) {
      if (res.status === 0) {
        this.$message({
          message: '上传驱动文件成功!',
          type: 'success'
        })
      } else if (res.status === -1) {
        this.$message({
          message: res.msg,
          type: 'warning'
        })
      }
    },
    // 选择项发生变化时
    handleSelectionChange(val) {
      this.dataSourceIds = val.map((item) => {
        return item.dataSourceId
      })
    },
    // 删除数据源
    deleteDataSource({ dataSourceId }) {
      this.$confirm('此操作将删除数据源, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteDataSource({ dataSourceIds: [dataSourceId] }).then((res) => {
            this.loading = true
            if (res.status === 0) {
              this.$message({
                message: '删除成功!',
                type: 'success'
              })
              this.queryDataSourceList()
            } else {
              this.$message({
                message: res.msg,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 打开编辑数据源
    handleShowDialog(row, type) {
      if (type !== 1) {
        this.btnType = type
        this.row = JSON.parse(JSON.stringify(row))
        this.$refs.configDataSourceDialog.dialogFormVisible = true
      }
    }
  }
}
</script>

<style scoped lang="scss">
.source-mgt-container {
  .source-mgt-main {
    .source-mgt-dialog {
      .mgt-dialog-upload {
        margin-left: 50px;
      }
    }
    .source-mgt-pag {
      margin-top: 10px;
    }
  }
  .dataBaseLink {
    cursor: pointer;
    .icon_url {
      fill: #5270dd;
      &:hover {
        fill: #7590ee;
      }
    }
    .not-config {
      color: red;
      &:hover {
        text-decoration: underline;
      }
    }
  }
  .structure-set {
    width: 68px;
    height: 26px;
    border-radius: 6px;
    text-align: center;
    line-height: 26px;
    border: 1px solid #d6d9e1;
    &:hover {
      background-color: #f3f3f3;
    }
  }
}
</style>
