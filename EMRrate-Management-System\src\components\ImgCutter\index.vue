<template>
  <el-dialog v-dialogDrag
    title="图片编辑"
    :visible.sync="visibleCt"
    top="10vh"
    
    class="modal"
    width="800px"
  >
    <ImgCutter
      ref="imgCutterModal"
      label="选择图片"
      fileType="png"
      :crossOrigin="true"
      crossOriginHeader="*"
      rate=""
      toolBgc="none"
      :showChooseBtn="true"
      :lockScroll="true"
      :boxWidth="780"
      :boxHeight="438"
      :cutWidth="cutWidth"
      :cutHeight="cutHeight"
      :sizeChange="true"
      :moveAble="true"
      :imgMove="true"
      :originalGraph="false"
      :smallToUpload="true"
      :saveCutPosition="true"
      :scaleAble="true"
      :previewMode="true"
      @cutDown="cutDown"
      @cancel="cancel"
    >
    </ImgCutter>
  </el-dialog>
</template>
<script>
//control component
import ImgCutter from "./cutter";
export default {
  name: "ImageCutter",
  props: {
    visible: {
      type: Boolean,
      default: false,
      required: false,
    },
    cutWidth: {
      type: Number,
      default: 200,
      required: false,
    },
    cutHeight: {
      type: Number,
      default: 200,
      required: false,
    },
  },
  components: {
    ImgCutter,
  },
  data() {
    return {};
  },
  methods: {
    cutDown: function (res) {
      this.$emit("OK", res);
      this.visibleCt = false;
    },
    cancel: function () {
      this.visibleCt = false;
    },
  },
  computed: {
    visibleCt: {
      get: function () {
        return this.visible;
      },
      set: function (newValue) {
        this.$emit("update:visible", newValue);
      },
    },
  },
  watch:{
    cutWidth(newQuestion, oldQuestion){
      console.log("newQuestion, oldQuestion",newQuestion, oldQuestion)
    }
  }
};
</script>
<style lang="scss">
.modal {
  
}
</style>