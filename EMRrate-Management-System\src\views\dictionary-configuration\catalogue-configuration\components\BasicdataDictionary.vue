<template>
  <div class="emr-container-main">
    <div class="source-mgt-table" v-loading="loading">
      <div class="emr-container-button">
        <el-button
          type="primary"
          icon="el-icon-folder-add"
          size="mini"
          @click="addnewoperation(0)"
          >添加一级目录</el-button
        >
      </div>
      <el-table
        class="emr-container-table"
        :data="tableData"
        ref="sourceMgtTable"
        v-loading="loading"
        :header-cell-style="{ background: '#fff', color: '#606266' }"
      >
        <el-table-column width="100">
          <template slot-scope="scope">
            <svg-icon
              v-if="!Boolean(scope.row.id)"
              icon-class="new"
              style="font-size: 36px"
            />
          </template>
        </el-table-column>
        <el-table-column prop="levelName" label="编号" min-width="100">
          <template slot="header">
            编号
            <el-tooltip placement="top">
              <div slot="content">
                基础编号规则：<br />
                <span class="wid30">1 </span> 全部出院病人数<br />
                <span class="wid30">2 </span> 门诊患者人次数<br />
                <span class="wid30">3 </span> 处方记录数<br />
                <span class="wid30">3.1 </span> 门诊处方数 <br />
                <span class="wid30">4 </span> 检验项目人次数 <br />
                <span class="wid30">4.1 </span> 住院检验项目人次数<br />
                <span class="wid30">4.2 </span> 门诊检验项目人次数
              </div>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <div v-if="scope.row.editstate">
              <el-input
                :class="!Boolean(scope.row.id) ? 'noidinput' : ''"
                v-model="scope.row.directoryCode"
                placeholder="请输入编号"
              ></el-input>
            </div>
            <div v-else>
              {{ scope.row.directoryCode }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="levelName" label="名称" min-width="100">
          <template slot-scope="scope">
            <div v-if="scope.row.editstate">
              <el-input
                :class="!Boolean(scope.row.id) ? 'noidinput' : ''"
                v-model="scope.row.directoryName"
                :placeholder="scope.row.parentCode ? '' : '请输入名称'"
              ></el-input>
            </div>
            <div v-else>
              <b>{{ scope.row.directoryName }}</b>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="levelName" label="单位" min-width="100">
          <template slot-scope="scope">
            <div v-if="scope.row.editstate">
              <el-input
                :class="!Boolean(scope.row.id) ? 'noidinput' : ''"
                v-model="scope.row.dataUnit"
                :placeholder="scope.row.parentCode ? '' : '请输入单位'"
              ></el-input>
            </div>
            <div v-else>
              <b>{{ scope.row.dataUnit }}</b>
            </div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="240">
          <template slot-scope="scope">
            <span v-show="!scope.row.editstate">
              <el-button
                size="mini"
                type="text"
                @click="scope.row.editstate = true"
                >编辑</el-button
              ><el-divider direction="vertical"></el-divider>
            </span>
            <span v-show="!scope.row.editstate">
              <el-popconfirm
                title="此操作将删除该目录, 是否继续?"
                @onConfirm="deleteBase(1, scope.row, scope.$index)"
              >
                <el-button slot="reference" type="text">删除</el-button>
              </el-popconfirm>
              <el-divider direction="vertical"></el-divider>
            </span>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-plus"
              v-show="!scope.row.editstate"
              @click="addnewoperation(1, scope.row, scope.$index)"
              >添加项目</el-button
            >
            <span v-show="scope.row.editstate">
              <el-button
                size="mini"
                type="primary"
                @click="
                  updateandadd(1, scope.row, scope.$index);
                  scope.row.editstate = false;
                "
                >保存</el-button
              ><el-divider direction="vertical"></el-divider>
            </span>
            <el-button
              v-show="scope.row.editstate"
              @click="editstatechange(1, scope.row, scope.$index)"
              >取消</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import {
  getBase,
  deleteBase,
  addBase,
  batchUpdateBase,
} from "@/api/document-management/catalogue-configuration";
export default {
  data() {
    return {
      tableData: [], // 表格数据
      loading: false,
    };
  },
  created() {
    // 进入页面初始化查询
    this.getBase();
  },
  methods: {
    // 查询列表
    getBase() {
      this.loading = true;
      this.tableData = [];
      getBase({}).then((res) => {
        if (res.status === 0) {
          res.data.map((v) => {
            this.tableData.push(
              Object.assign(v, {
                editstate: false,
              })
            );
          });
          this.loading = false;
        }
      });
    },

    // 不同新增操作，添加不同的目录
    addnewoperation(type, item1, index1) {
      let newobj = {
        directoryType: "1",
        directoryName: "",
        directoryCode: "",
        dataUnit: "",
        id: null,
        editstate: true,
      };
      if (type === 0) {
        this.tableData.push(newobj);
      } else if (type === 1) {
        this.tableData.splice(index1 + 1, 0, newobj);
      }
    },
    //提交保存和编辑操作
    async updateandadd(type, item1, index1) {
      if ((type === 1) & !Boolean(item1.id)) {
        await this.addBase(item1);
        this.getBase();
      } else if ((type === 1) & Boolean(item1.id)) {
        await this.batchUpdateBase(item1);
      }
    },
    // 新增操作请求
    async addBase(item) {
      await addBase(item).then((res) => {
        if (res.status !== 0) {
          this.$message({
            message: res.msg,
            type: "error",
          });
          return;
        } else {
          this.$message({
            message: "新增成功",
            type: "success",
          });
        }
      });
    },
    // 修改操作请求
    async batchUpdateBase(item) {
      await batchUpdateBase([item]).then((res) => {
        if (res.status === 0) {
          this.$message({
            message: "保存成功!",
            type: "success",
          });
        } else {
          this.$message({
            message: res.msg,
            type: "error",
          });
        }
      });
    },
    // 改变编辑状态(取消)
    editstatechange(type, item1, index1, item2, index2, item3, index3) {
      if (type === 1) {
        if (this.tableData[index1].id) {
          getBase({}).then((res) => {
            if (res.status === 0) {
              this.tableData[index1].directoryName =
                res.data[index1].directoryName;
              this.tableData[index1].directoryCode =
                res.data[index1].directoryCode;
              this.tableData[index1].dataUnit = res.data[index1].dataUnit;
              this.tableData[index1].editstate = false;
            }
          });
        } else {
          this.tableData.splice(index1, 1);
        }
      }
    },

    // 删除操作
    async deleteBase(type, item1, index1) {
      if (type === 1) {
        deleteBase({ ids: item1.id }).then((res) => {
          if (res.status === 0) {
            this.$message({
              message: "删除成功!",
              type: "success",
            });
            this.tableData.splice(index1, 1);
          } else {
            this.$message({
              message: res.msg,
              type: "error",
            });
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/emr-styles/emr-main-table.scss";

.wid30 {
  width: 30px;
  display: inline-block;
}
</style>
