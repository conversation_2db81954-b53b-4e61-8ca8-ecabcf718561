import request from '@/utils/request'

/* 1. 采集任务 */

// 新增和更新用的同一个接口

// 获取/查询采集任务组
export function queryTaskGroup(data) {
  return request({
    url: '/mateData/getScheduleJobInfo',
    method: 'post',
    data
  })
}
// 新增采集任务组
export function addTaskGroup(data) {
  return request({
    url: '/mateData/addTaskAllocationInfo',
    method: 'post',
    data
  })
}
// 更新采集任务组
export function updateTaskGroup(data) {
  return request({
    url: '/mateData/addTaskAllocationInfo',
    method: 'post',
    data
  })
}
// 删除采集任务组
export function deleteTaskGroups(params) {
  return request({
    url: '/mateData/deleteTaskAllocationInfo',
    method: 'delete',
    params
  })
}
// 立即执行采集任务
export function performTask(data) {
  return request({
    url: '/mateData/exeScheduleJob',
    method: 'post',
    data
  })
}
// 获取任务执行结果
export function getTaskExecutionResult(data) {
  return request({
    url: '/mateData/queryTaskAllocationHisInfo',
    method: 'post',
    data
  })
}

/**
 * 配置任务接口
 */

// 添加或更新子任务
export function addOrUpdateSubTask(data) {
  return request({
    url: '/subtask/addOrUpdate',
    method: 'post',
    data
  })
}

// 删除子任务
export function deleteSubTask(params) {
  return request({
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    url: '/subtask/delete',
    method: 'delete',
    params
  })
}
// 根据任务组ID查询子任务详情
export function querySubtasks(params) {
  return request({
    headers: {
      'Content-Type': 'application/json'
    },
    url: '/subtask/query',
    method: 'get',
    params
  })
}
