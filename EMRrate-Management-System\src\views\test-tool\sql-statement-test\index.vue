<template>
  <MainCard>
    <div class="source-mgt-container">
      <div class="source-mgt-header">
        <el-form :model="queryData" ref="ruleForm" label-width="100px">
          <el-form-item>
            <el-button
              type="primary"
              size="mini"
              @click="execute"
              :disabled="queryData.sql === ''"
              >SQL语句执行</el-button
            >
          </el-form-item>
          <el-form-item label="数据源:">
            <el-select v-model="queryData.dataSourceId">
              <el-option
                v-for="item in datasourcelist"
                :key="item.dataSourceId"
                :label="item.dataSourceName"
                :value="item.dataSourceId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="SQL语句:">
            <el-input
              :disabled="queryData.dataSourceId === ''"
              v-model="queryData.sql"
              type="textarea"
              :rows="8"
              placeholder="请输入内容"
              resize="both"
            >
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <div
        class="source-mgt-main"
        v-show="promptinformation || tableData.length"
      >
        <div class="source-mgt-title">测试结果:</div>
        <div class="source-mgt-table">
          <div class="promptdiv" v-if="promptinformation">
            {{ promptinformation }}
          </div>
          <el-table
            :data="tableData"
            v-show="tableData.length"
            ref="sourceMgtTable"
            style="width: 100%"
            border
            v-loading="loading"
            :header-cell-style="{ background: '#F5F7FA', color: '#606266' }"
          >
            <el-table-column
              v-for="item in tableheader"
              :key="item"
              :prop="item"
              :label="item"
              width="150"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
          </el-table>
          <!-- <div class="source-mgt-pag">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="queryData.pageNum"
            :page-sizes="[5, 10, 15, 20]"
            :page-size="queryData.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalNum"
          >
          </el-pagination>
        </div> -->
        </div>
      </div>
    </div>
  </MainCard>
</template>

<script>
import { queryrequireProjectDictionary } from "@/api/document-management/name-character";
import {
  datasourcelist,
  execute,
} from "@/api/document-management/rule-configuration";
export default {
  data() {
    return {
      queryData: {
        // 查询数据
        dataSourceId: "",
        sql: "",
      },
      totalNum: 1,
      tableData: [], // 表格数据
      loading: false,
      datasourcelist: [], //数据源列表
      tableheader: [],
      promptinformation: "",
    };
  },
  created() {
    // 进入页面初始化查询
    datasourcelist({ pageNum: 1, pageSize: 9999 }).then((res) => {
      if (res.status === 0) {
        this.datasourcelist = res.data.list;
      }
    });
  },
  methods: {
    //
    queryrequireProjectDictionary() {
      this.loading = true;
      this.queryData.startTime = this.dataval[0];
      this.queryData.endTime = this.dataval[1];
      queryrequireProjectDictionary(this.queryData).then((res) => {
        if (res.status === 0) {
          this.tableData = res.data.list;
          this.loading = false;
        }
      });
    },
    //
    execute() {
      this.tableheader = [];
      this.tableData = [];
      this.promptinformation = null;
      this.loading = true;
      execute(this.queryData).then((res) => {
        if (res.status === 0) {
          for (let k in res.data.result[0]) {
            this.tableheader.push(k);
          }
          this.tableData = res.data.result;
          this.promptinformation = res.data.msg;
          this.loading = false;
        } else {
          this.$message({
            type: "error",
            message: res.msg,
          });
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.source-mgt-container {
  margin-bottom: 40px;
  .source-mgt-header {
    display: flex;
    margin: 10px 0;
    min-width: 1200px;
    ::v-deep form {
      min-width: 50% !important;
    }
  }
  .source-mgt-main {
    // padding-left: 10px;
    display: flex;
    .source-mgt-title {
      width: 90px;
      text-align: right;
      color: #606266;
      font-size: 14px;
      margin-right: 10px;
    }
    .source-mgt-table {
      // margin-top: 10px;
      // margin-left: 100px;
      flex: 1;
    }
    .source-mgt-dialog {
      .mgt-dialog-upload {
        margin-left: 50px;
      }
    }
    .source-mgt-pag {
      margin-top: 10px;
    }
  }
}
.promptdiv {
  margin-bottom: 10px;
  color: #1b66c0;
  //  margin-left: 100px;
}
.el-table {
  // height: 600px;
  overflow: scroll;
}
</style>
