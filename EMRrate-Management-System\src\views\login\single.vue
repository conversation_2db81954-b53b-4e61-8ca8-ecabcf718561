<template>
  <div class="tips">
    <section>
      <div class="windowLoading">
        <!-- logoLoad-->
        <div class="loader">
          <div class="face">
            <div class="circle"></div>
          </div>
          <div class="face">
            <div class="circle"></div>
          </div>
        </div>
        <div class="wu-loader">
          <div
            v-for="item in 9"
            :style="`backgroundImage:url(${imageUrl})`"
            :key="item"
            :class="`syimg wu-cube_${item}`"></div>
        </div>
        <div class="loadingText">
          <div>
            正在登入<b class="syname">{{ syname }}</b
            >请稍后
          </div>
          <span class="loaddots"></span>
        </div>
      </div>
      <div
        :style="`backgroundImage:url(${footerLogo})`"
        class="footcopy"></div>
    </section>
  </div>
</template>
<script>
import { getHospitalLogoPath as _getHospitalLogoPath } from '@/api/sys-config'
export default {
  name: 'singleLogin',
  watch: {
    $route: {
      handler: function (route) {
        const { redirect, singleToken } = route.query
        if (singleToken) {
          this.redirect = redirect || ''
          this.singleToken = singleToken
          this.$store.dispatch('user/singleLogin', singleToken).then(() => {
            this.$router.push({ path: this.redirect || '/' })
          })
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      redirect: '',
      singleToken: '',
      syname: '电子病历评级文档管理系统',
      imageUrl: require('@/assets/login/emr_icon.png'),
      footerLogo: ''
    }
  },
  created() {
    _getHospitalLogoPath({ logoType: 'system.logo.hospitalImg1' }).then(
      (res) => {
        this.footerLogo = res.data
      }
    )
  }
}
</script>
<style lang="scss" scoped>
@import '~./single.scss';
</style>
