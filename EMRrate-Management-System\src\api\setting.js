import request from '@/utils/request'

//获取所有系统配置
export function querySysConfig() {
    return request({
        url: "/system/querySysConfig"
    })
}

//更新系统配置
export function updateSysConfigs(data) {
    return request({
        url: "/system/updateSysConfigs",
        method: "post",
        data
    })
}


export function upLoadTabBrand(file) {
    let form = new FormData();
    form.append("file", file)
    return request({
        url: '/system/setTabBrand',
        method: "post",
        headers: {
            'content-type': 'multipart/form-data'
        },
        data: form,
    })
}

export function upLoadIcon(file) {
    let form = new FormData();
    form.append("file", file)
    return request({
        url: '/system/setIcon',
        method: "post",
        headers: {
            'content-type': 'multipart/form-data'
        },
        data: form,
    })
}

export function upLoadLoginLogo(file) {
    let form = new FormData();
    form.append("file", file)
    return request({
        url: '/system/setLoginLogo',
        method: "post",
        headers: {
            'content-type': 'multipart/form-data'
        },
        data: form,
    })
}

export function getBrandsUrl() {
    return request({
        url: '/system/getBrandsUrl',
        headers: {
            token: ''
        }
    })
}