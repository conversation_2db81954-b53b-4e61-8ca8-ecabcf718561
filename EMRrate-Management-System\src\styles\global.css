html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td {
  margin: 0;
  padding: 0;
}

ul, li, ol, li {
  list-style: none;
}

h1 {
  position: relative;
  font-size: 24px;
  display: inline-block;
  margin-bottom: 10px;
  margin-top: 10px;
}

h1::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 2px;
  height: 7px;
  width: 100%;
  background-color: rgba(18, 145, 195, 0.6);
}
h2 {
  font-size: 20px;
}
