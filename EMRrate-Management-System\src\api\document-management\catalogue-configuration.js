import request from '@/utils/request'
import qs from 'qs'


// 查询等级字典
export function querydocumentDirectoryConfiguration(data) {
  return request({
    url: '/emr/documentDirectoryConfiguration/query',
    method: 'post',
    data
  })
}



// 新增文档目录
export function adddocumentDirectoryConfiguration(data) {
  return request({
    url: '/emr/documentDirectoryConfiguration/add',
    method: 'post',
    data
  })
}



// 删除等级字典
export function deletedocumentDirectoryConfiguration(params) {
  return request({
    url: '/emr/documentDirectoryConfiguration/delete',
    method: 'delete',
    params
  })
}



// 批量保存文档目录（整个页面的目录）
export function batchSavedocumentDirectoryConfiguration(data) {
  return request({
    url: '/emr/documentDirectoryConfiguration/batchSave',
    method: 'post',
    data
  })
}

// 查询所有文档目录
export function queryAlldocumentDirectoryConfiguration(data) {
  return request({
    url: '/emr/documentDirectoryConfiguration/queryAll',
    method: 'post',
    data
  })
}

// 批量更新文档目录(根据ID只是更新，没有新增)
export function batchUpdate(data) {
  return request({
    url: '/emr/documentDirectoryConfiguration/batchUpdate',
    method: 'post',
    data
  })
}



// 新增基础数据字典
export function addBase(data) {
  return request({
    url: '/emr/dataDictionaryDirectoryConfiguration/addBase',
    method: 'post',
    data
  })
}


//获取基础数据字典
export function getBase(params) {
  return request({
    url: '/emr/dataDictionaryDirectoryConfiguration/getBase',
    method: 'get',
    params
  })
}

// 删除基础数据字典
export function deleteBase(params) {
  return request({
    url: '/emr/dataDictionaryDirectoryConfiguration/deleteBase',
    method: 'delete',
    params
  })
}


// 批量保存文档目录（整个页面的目录）
export function batchUpdateBase(data) {
  return request({
    url: '/emr/dataDictionaryDirectoryConfiguration/batchUpdateBase',
    method: 'post',
    data
  })
}
// 批量保存文档目录（整个页面的目录）
export function batchUpdateBaseRule(data) {
  return request({
    url: '/emr/dataDictionaryDirectoryRuleConfiguration/batchUpdateBase',
    method: 'post',
    data
  })
}

//获取病历|质量数据字典
export function getMedicaRecordsOrQuality(params) {
  return request({
    url: '/emr/dataDictionaryDirectoryConfiguration/getMedicaRecordsOrQuality',
    method: 'get',
    params
  })
}

// 获取某条病历|质量数据字典
export function getOneMedicaRecordsOrQuality(params) {
  return request({
    url: '/emr/dataDictionaryDirectoryRuleConfiguration/getOneMedicaRecordsOrQuality',
    method: 'get',
    params
  })
}


// 文档目录配置使用，查询所有文档目录以及二级的要求项目（选择数据及运算公式）
export function queryDirectoryAndProject(data) {
  return request({
    url: '/emr/documentDirectoryConfiguration/queryDirectoryAndProject',
    method: 'post',
    data
  })
}



//查询目录树形
export function queryDirectoryTreerule(params) {
  return request({
    url: '/emr/rulePermissionConfiguration/queryDirectoryTree',
    method: 'get',
    params
  })
}

// 根据用户账号查询其负责的任务
export function getTaskrule(params) {
  return request({
    url: '/emr/rulePermissionConfiguration/getTask',
    method: 'get',
    params
  })
}


// 保存规则权限配置(勾选多少传多少，不传父目录，只传最后一级目录)
export function saveRulePermissions(data) {
  return request({
    url: '/emr/rulePermissionConfiguration/saveRulePermission',
    method: 'post',
    data
  })
}


//查询用户及任务情况
export function getUserListAndTasks(params) {
  return request({
    url: '/emr/rulePermissionConfiguration/getUserListAndTasks',
    method: 'get',
    params
  })
}


// 保存任务组
export function savesysUserGroup(data) {
  return request({
    url: '/emr/sysUserGroup/save',
    method: 'post',
    data
  })
}

//查询用户账号、姓名、手机号等信息
export function queryUserInfoList(data) {
  return request({
    url: '/user/queryUserInfoList',
    method: 'POST',
    data
  })
}
