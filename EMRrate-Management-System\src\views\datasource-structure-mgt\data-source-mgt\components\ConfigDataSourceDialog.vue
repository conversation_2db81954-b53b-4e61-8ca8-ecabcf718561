<template>
  <div class="config-data-source">
    <el-dialog
      v-dialogDrag
      top="20px"
      :title="btnType == 1 ? '添加数据源' : '编辑数据源'"
      :visible.sync="dialogFormVisible"
      @open="handlerOpen"
      @closed="handlerClose"
      :close-on-click-modal="false"
      width="980px"
    >
      <el-tabs
        @wheel.native="handlerMouseWheel"
        v-model="curItemName"
        tab-position="left"
        style="height: 100%"
      >
        <el-tab-pane name="0" label="数据源信息">
          <div class="form-box">
            <el-form
              :model="dataInfoForm"
              :rules="dataInfoRules"
              :label-width="formLabelWidth"
              ref="dataInfoForm"
            >
              <el-form-item label="数据源名称" prop="dataSourceName">
                <el-input
                  v-model="dataInfoForm.dataSourceName"
                  @blur="handlerBlur()"
                ></el-input>
              </el-form-item>
              <el-form-item label="采集系统代码" prop="sysCode">
                <el-input v-model="dataInfoForm.sysCode"></el-input>
              </el-form-item>
              <div @wheel.stop="() => {}">
                <el-form-item label="描述" prop="dataSourceDescribe">
                  <el-input
                    type="textarea"
                    v-model="dataInfoForm.dataSourceDescribe"
                  ></el-input>
                </el-form-item>
              </div>

              <el-form-item label="采集系统名称" prop="sysName">
                <el-input v-model="dataInfoForm.sysName"></el-input>
              </el-form-item>
              <div class="database-info-box">
                <el-form-item label="采集数据库名称" prop="databaseName">
                  <el-input v-model="dataInfoForm.databaseName"></el-input>
                </el-form-item>
                <el-form-item label="SCHEMA" prop="databaseSchema">
                  <el-input v-model="dataInfoForm.databaseSchema"></el-input>
                </el-form-item>
                <el-form-item label="数据库类型" prop="databaseType">
                  <el-select
                    style="width: 100%"
                    v-model="dataInfoForm.databaseType"
                    @change="handlerChange"
                  >
                    <el-option
                      v-for="item in databaseTypeData"
                      :key="item.id"
                      :label="item.contentValue"
                      :value="item.contentKey"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="跨库查询" prop="needCrossQuery">
                  <el-switch
                    v-model="dataInfoForm.needCrossQuery"
                    active-color="#5270DD"
                    :active-value="1"
                    :inactive-value="0"
                    :active-text="'是'"
                    :inactive-text="'否'">
                  </el-switch>
                </el-form-item>
              </div>
            </el-form>
          </div>
        </el-tab-pane>
        <el-tab-pane name="1" label="数据库设置">
          <div class="form-box">
            <el-form
              :model="databaseSetupForm"
              :rules="databaseSetupRules"
              :label-width="formLabelWidth"
              ref="databaseSetupForm"
            >
              <el-form-item
                label-width="120px"
                label="数据库访问URL"
                prop="databaseUrl"
              >
                <el-input v-model="databaseSetupForm.databaseUrl"></el-input>
              </el-form-item>
              <el-form-item
                label-width="120px"
                label="用户名"
                prop="databaseUser"
              >
                <el-input v-model="databaseSetupForm.databaseUser"></el-input>
              </el-form-item>
              <el-form-item label-width="120px" label="密码" prop="databasePwd">
                <el-input
                  type="password"
                  auto-complete="new-password"
                  name="randomName"
                  v-model="databaseSetupForm.databasePwd"
                  show-password
                ></el-input>
              </el-form-item>
              <el-form-item label="数据库驱动" prop="databaseDriver">
                <el-input v-model="databaseSetupForm.databaseDriver"></el-input>
              </el-form-item>
              <el-form-item label="驱动包路径" prop="driverFiles">
                <el-input type="text" v-model="databaseSetupForm.driverFiles">
                  <div slot="append">
                    <el-upload
                      class="upload-demo"
                      ref="upload"
                      :show-file-list="false"
                      :action="`${baseURL}/datasource/uploadDriverFile`"
                      :on-success="handleSuccess"
                      :auto-upload="true"
                    >
                      <div class="driverFiles" slot="trigger">
                        <img :src="upLoadImg" alt="" />
                        <span>上传驱动</span>
                      </div>
                    </el-upload>
                  </div>
                </el-input>
                <div class="driverFiles-list">
                  <div
                    class="list-item"
                    v-for="item in driverFilesList"
                    :key="item.id"
                  >
                    <div class="left">
                      <img :src="driverFileImg" alt="" />
                      <span>{{ item.name }}</span>
                      <span class="size">({{ item.size }}M)</span>
                      <span></span>
                    </div>
                    <div @click="deleteFileList" class="right">
                      删除
                    </div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="测试语句" prop="testsql">
                <div @wheel.stop="() => {}" class="test-box">
                  <div class="test-input">
                    <el-input
                      type="textarea"
                      resize="none"
                      :rows="4"
                      v-model="databaseSetupForm.testsql"
                    ></el-input>
                  </div>
                  <div class="test-btn">
                    <div @click="satrtTestDataSource" class="btn">
                      <svg-icon
                        icon-class="icon_link"
                        style="font-size: 14px"
                      />
                      <span>测试连接</span>
                    </div>
                  </div>
                  <div class="test-result">
                    <TestStatus
                      :testFailMsg="testFailMsg"
                      :testResultStatus="testResultStatus"
                    />
                  </div>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
        <el-tab-pane
          v-if="btnType != 1 && dialogFormVisible"
          name="2"
          label="数据结构设置"
          ><div>
            <StructureConfig
              ref="structureConfig"
              v-if="curItemName == '2'"
              :row="row"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <div
          @wheel="handlerMouseWheel"
          v-show="
            (btnType != 1 && curItemName !== '2') ||
              (btnType == 1 && curItemName !== '1')
          "
          class="mouse-prompt"
        >
          <div>
            <svg-icon icon-class="icon_mouse" style="font-size: 28px" />
          </div>
          <div class="tip">鼠标下滑设置下一项</div>
        </div>
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="saveConfig(['dataInfoForm', 'databaseSetupForm'])"
          >保存</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addDataSource,
  testDataSource as _testDataSource,
  updateDataSource
} from '@/api/dataSourceStructureMgt/dataSourceMgt'
import getCodeValueConten from '@/mixins/getCodeValueContent'
import TestStatus from './TestStatus.vue'
import StructureConfig from './StructureConfig.vue'
import upLoadImg from '@/assets/dataSource/icon_21.png'
import driverFileImg from '@/assets/dataSource/icon_22.png'
function defaultdataInfoForm() {
  return {
    // 表单数据
    dataSourceName: '',
    sysCode: '',
    dataSourceDescribe: '',
    sysName: '',
    databaseName: '',
    dataSourceId: '',
    databaseSchema: '',
    databaseType: '',
    needCrossQuery: 0
  }
}
function databaseSetupForm() {
  return {
    databaseUrl: '',
    databaseUser: '',
    databasePwd: '',
    databaseDriver: '',
    driverFiles: '',
    testsql: ''
  }
}
export default {
  components: { TestStatus, StructureConfig },
  mixins: [getCodeValueConten],
  data() {
    return {
      dialogFormVisible: false, // 弹框状态
      formLabelWidth: '120px',
      curItemName: '',
      scrolledDistance: 0,
      upLoadImg,
      driverFileImg,
      dataInfoForm: defaultdataInfoForm(),
      dataInfoRules: {
        dataSourceName: [
          { required: true, message: '请输入数据源名称', trigger: 'blur' }
        ],
        sysCode: [
          { required: true, message: '请输入采集系统代码', trigger: 'change' }
        ],
        sysName: [
          { required: true, message: '请输入采集系统名称', trigger: 'change' }
        ],
        databaseName: [
          { required: true, message: '请输入采集数据库名称', trigger: 'blur' }
        ],
        databaseSchema: [
          { required: true, message: '请输入SCHEMA', trigger: 'blur' }
        ],
        databaseType: [
          { required: true, message: '请选择数据库类型', trigger: 'change' }
        ]
      },
      databaseSetupForm: databaseSetupForm(),
      databaseSetupRules: {
        databaseUrl: [
          { required: true, message: '请输入数据库访问URL', trigger: 'blur' }
        ],
        databaseUser: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        databasePwd: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ],
        testsql: [
          { required: true, message: '请输入测试语句', trigger: 'blur' }
        ]
      },
      driverFilesList: [],
      testFailMsg: '',
      testResultStatus: -1, //-1代表还未加载 0代表加载中 1代表测试成功 2代表测试失败
      isDisabled: false,
      baseURL: process.env.VUE_APP_BASE_API
    }
  },
  props: {
    btnType: {
      type: Number
    },
    row: {
      type: Object
    },
    databaseTypeData: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    databaseTypeData: {
      handler(val) {
        if (val.length > 0) {
          this.isDisabled = false
          return
        }
        this.isDisabled = true
      }
    }
  },
  computed: {
    allFormData() {
      return { ...this.dataInfoForm, ...this.databaseSetupForm }
    }
  },
  methods: {
    // 处理鼠标滚动下一项
    handlerMouseWheel(e) {
      // 如果按住了ctrl按键进行了缩放操作则阻止滚动
      if (e.ctrlKey) {
        return
      }
      // 向下滚动到最后一项时
      if (this.btnType != 1) {
        if (e.deltaY > 0 && this.curItemName === '2') {
          this.curItemName = '2'
          return
        }
      } else {
        if (e.deltaY > 0 && this.curItemName === '1') {
          this.curItemName = '1'
          return
        }
      }

      // 向上滚动到第一项时
      if (e.deltaY < 0 && this.curItemName === '0') {
        this.curItemName = '0'
        return
      }
      let scrollTimeout
      // 累加滚动距离
      this.scrolledDistance += e.deltaY
      // 设置阈值，滚动大于该值时触发事件
      const threshold = 600
      // 清除之前设置的定时器
      if (scrollTimeout) {
        clearTimeout(scrollTimeout)
      }
      // 延迟防抖
      scrollTimeout = setTimeout(() => {
        // 检查滚动距离是否达到阈值
        if (Math.abs(this.scrolledDistance) >= threshold) {
          if (e.deltaY > 0) {
            this.curItemName = (Number(this.curItemName) + 1).toString()
          } else {
            this.curItemName = (Number(this.curItemName) - 1).toString()
          }
          this.scrolledDistance = 0
        }
      }, 100)
    },
    // 处理dialog打开时
    async handlerOpen() {
      // 根据用户点击模块定位到对应页面
      switch (this.btnType) {
        case 0:
          this.curItemName = '0'
          break
        case 1:
          this.curItemName = '0'
          break
        case 2:
          this.curItemName = '1'
          break
        case 3:
          this.curItemName = '2'
          break
      }

      if (this.btnType != 1) {
        Object.keys(this.dataInfoForm).forEach((key) => {
          this.dataInfoForm[key] = this.row[key]
        })
        Object.keys(this.databaseSetupForm).forEach((key) => {
          this.databaseSetupForm[key] = this.row[key]
        })
      }
    },
    // 当数据库类型选择时
    handlerChange(val) {
      const curItem = this.databaseTypeData.find(
        (item) => item.contentKey === val
      )
      this.$set(this.databaseSetupForm, 'databaseDriver', curItem.data1)
      this.$set(this.databaseSetupForm, 'testsql', curItem.data2)
      this.$set(this.databaseSetupForm, 'databaseUrl', curItem.data3)
    },
    // 新增时数据源名称输入失去焦点时
    handlerBlur() {
      if (this.btnType == 1) {
        if (!this.dataInfoForm.sysCode) {
          this.dataInfoForm.sysCode = this.dataInfoForm.dataSourceName
        }
        if (!this.dataInfoForm.sysName) {
          this.dataInfoForm.sysName = this.dataInfoForm.dataSourceName
        }
      }
    },
    // 驱动文件上传成功时
    handleSuccess(res, file, fileList) {
      if (res.status === 0) {
        this.databaseSetupForm.driverFiles = file.name
        this.$message({
          message: '上传成功至服务器',
          type: 'success'
        })
        console.log(file)
        this.driverFilesList = [file].map((item) => ({
          id: item.uid,
          name: item.name,
          size: (item.size / 1024 / 1024).toFixed(2)
        }))
        return
      }
      this.driverFilesList = []
      this.$message({
        message: res.msg,
        type: 'warning'
      })
    },
    // 清楚文件列表
    deleteFileList() {
      this.driverFilesList = []
    },
    // 更新数据源
    updateDataSource() {
      updateDataSource(this.allFormData).then((res) => {
        if (res.status !== 0) {
          this.$message({
            message: res.msg,
            type: 'error'
          })
          return
        }
        this.$message({
          message: '更新数据源成功',
          type: 'success'
        })
        this.$emit('updatedataSourceList')
        this.dialogFormVisible = false
      })
    },
    // 新增数据源
    addDataSource() {
      addDataSource(this.allFormData).then((res) => {
        if (res.status !== 0) {
          this.$message({
            message: res.msg,
            type: 'error'
          })
          return
        }
        this.$message({
          message: '新增数据源成功',
          type: 'success'
        })
        this.$emit('updatedataSourceList')
        this.dialogFormVisible = false
      })
    },

    // 保存
    async saveConfig(formName) {
      if (this.curItemName !== '2') {
        this.submitForm(formName)
      } else {
        // 保存数据结构设置
        await this.$refs.structureConfig.saveStructure()
        this.$emit('updatedataSourceList')
        this.dialogFormVisible = false
      }
    },

    // 新增/编辑校验
    async submitForm(formName) {
      this.$refs[formName[0]].validate((valid1) => {
        if (valid1) {
          this.$refs[formName[1]].validate((valid2) => {
            if (valid2) {
              this.testDataSource()
            } else {
              // 第二个表单未通过校验
              this.$message.error('请先完成必填项')
              // 如果当前配置界面不在数据库设置页面则跳转至该页面
              if (this.curItemName != '1') {
                this.curItemName = '1'
              }
              return false
            }
          })
        } else {
          // 第一个表单未通过校验
          this.$message.error('请先完成必填项')
          // 如果当前配置界面不在数据源信息页面则跳转至该页面
          if (this.curItemName != '0') {
            this.curItemName = '0'
          }
          return false
        }
      })
    },
    // 保存前测试数据源
    async testDataSource() {
      const { status } = await _testDataSource(this.allFormData)
      if (status !== 0) {
        this.$message({
          message: '测试数据源连接失败，请重新填写数据源',
          type: 'error'
        })
        return
      }
      if (this.btnType == '1') {
        this.addDataSource()
        return
      }
      this.updateDataSource()
    },
    // 点击测试按钮测试数据源
    satrtTestDataSource() {
      this.$refs['databaseSetupForm'].validate(async (valid) => {
        if (valid) {
          this.testResultStatus = 0
          const { status, msg } = await _testDataSource(this.allFormData)
          if (status == 0) {
            this.testResultStatus = 2
            return
          }
          this.testResultStatus = 1
          this.testFailMsg = msg
        } else {
          this.$message({
            type: 'warning',
            message: '请先完成必填项'
          })
          return false
        }
      })
    },
    // 处理dialog关闭后
    handlerClose() {
      this.dataInfoForm = defaultdataInfoForm()
      this.databaseSetupForm = databaseSetupForm()
      this.$refs['dataInfoForm'].resetFields()
      this.$refs['databaseSetupForm'].resetFields()
      this.testResultStatus = -1
    }
  }
}
</script>

<style lang="scss" scoped>
.config-data-source {
  display: inline-block;
  margin-right: 10px;
  .form-box {
    width: 80%;
    margin-left: 30px;
    padding-top: 20px;
  }
  .form-box:nth-child(1) {
    .test-box {
      width: 110%;
      height: 200px;
      border-radius: 10px;
      overflow: hidden;
      border: 1px solid #e3e6e8;
      display: flex;
      flex-direction: column;
      .test-input {
        flex: 3;
        overflow: auto;
        .el-textarea {
          height: 100%;
        }
        ::v-deep.el-textarea__inner {
          border: none !important;
          height: 100%;
        }
      }
      .test-btn {
        border-bottom: 1px solid #e3e6e8;
        background-color: #f3f3f3;
        flex: 1;
        display: flex;
        align-items: center;
        padding: 6px 0;
        .btn {
          width: 90px;
          line-height: 26px;
          cursor: pointer;
          border: 1px solid #c3c7d0;
          text-align: center;
          border-radius: 6px;
          margin-left: 8px;
          background: linear-gradient(to bottom, #fefefd, #ececec);
          &:hover {
            background: linear-gradient(to bottom, #ececec, #fefefd);
          }
        }
      }
      .test-result {
        flex: 3;

        height: 100%;
        overflow: auto;
      }
    }
  }

  .database-info-box {
    .el-divider {
      margin: 40px 0px;
    }
    .el-divider__text {
      color: #5270dd;
    }
  }
  .test-database-part {
    .test-form {
      background-color: #f5f5f5;
      padding: 20px 20px 10px 0;
      border-radius: 10px;
      .test-btn {
        text-align: right;
      }
    }
  }
  .dialog-footer {
    position: relative;
    .test-data-source {
      position: absolute;
      top: 0;
      bottom: 0;
    }
    .mouse-prompt {
      position: absolute;
      left: 50%;
      top: -40px;
      text-align: center;
      .tip {
        color: #888;
        margin-top: 10px;
        font-size: 12px;
      }
    }
  }
  .driverFiles {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 6px 20px;

    img {
      margin-right: 4px;
      margin-top: 2px;
    }
    span {
      color: #395bd5;
    }
  }
  .driverFiles-list {
    .list-item {
      display: flex;
      justify-content: space-between;
      background-color: #e8eef0;
      border-radius: 9px;
      padding: 0 10px;
      margin-top: 6px;
      &:hover {
        background-color: #ecf2f4;
      }
      .left {
        img {
          vertical-align: middle;
          margin-top: -2px;
          margin-right: 8px;
        }
        .size {
          margin-left: 8px;
          color: #888;
        }
      }
      .right {
        cursor: pointer;
        color: #395bd5;
        &:hover {
          font-weight: 600;
        }
      }
    }
  }
  ::v-deep.el-input-group__append,
  .el-input-group__prepend {
    cursor: pointer;
    background-color: #fff;
    border-top-right-radius: 9px;
    border-bottom-right-radius: 9px;
    &:hover {
      background-color: #f5f7fa;
    }
  }
}
.el-form {
  margin-right: 10px;
}
::v-deep .el-dialog .el-dialog__body {
  // 设置dialog的固定高度
  position: relative;
  /* height: 74vh; */
  overflow: auto;
  padding: 10px 10px 0;
}
::v-deep .el-input-group__append,
.el-input-group__prepend {
  padding: 0;
}
::v-deep.el-tabs--left .el-tabs__header.is-left {
  height: 520px;
}
</style>
