<template>
  <div class="rate-info-container">
    <el-dialog
      v-dialogDrag
      title="信息详情"
      @open="handlerAttachment"
      :visible.sync="dialogFormVisible"
      width="80%"
      top="60px"
      :close-on-click-modal="false">
      <div class="nav-info">
        <span
          @click="goPreviou"
          v-show="curIndex !== 0">
          <span class="control">上一条</span>
          <el-divider
            v-show="attachmentsList.length !== 0 && curIndex !== rateInfoList.length - 1"
            direction="vertical"></el-divider>
        </span>
        <span
          @click="goNext"
          v-show="curIndex !== rateInfoList.length - 1">
          <span class="control">下一条</span>
          <el-divider
            v-show="attachmentsList.length !== 0"
            direction="vertical"></el-divider>
        </span>
        <span
          v-show="attachmentsList.length !== 0"
          class="control"
          @click="smoothScroll('#attachments')"
          >查看附件</span
        >
      </div>
      <div
        class="content"
        ref="rateBox">
        <div class="info-header">
          <div class="title">{{ curRateInfo.msgTitle }}</div>
          <div class="info-type">信息类型：{{ curRateInfo.msgType }}</div>
        </div>
        <div class="info-content">{{ curRateInfo.msgContent }}</div>
        <div
          id="attachments"
          v-show="attachmentsList.length !== 0"
          class="info-footer">
          <div class="title">相关附件</div>
          <div class="attachments-list">
            <div
              class="item"
              v-for="(item, index) in attachmentsList"
              :key="index">
              <span class="item-name">{{ item }}</span>
              <span
                class="downLoad"
                @click="downLoad(item)"
                >下载</span
              >
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { messageDownload as _messageDownload } from '@/api/ratingInfoManagement/ratingInfoConfig'
export default {
  data() {
    return {
      dialogFormVisible: false,
      attachmentsList: []
    }
  },
  props: ['rateInfoList', 'curRateInfo'],
  computed: {
    curIndex() {
      return this.rateInfoList.indexOf(this.curRateInfo)
    }
  },
  methods: {
    // 平滑滚动
    smoothScroll(targetId) {
      const targetElement = this.$refs.rateBox.querySelector(targetId)
      targetElement.scrollIntoView({ behavior: 'smooth' })
    },
    // 上一页
    goPreviou() {
      this.$emit('update:curRateInfo', this.rateInfoList[this.curIndex - 1])
      // 下一个事件循环执行，因为.sync为异步更新
      this.$nextTick(() => {
        this.handlerAttachment()
      })
    },
    // 下一页
    goNext() {
      this.$emit('update:curRateInfo', this.rateInfoList[this.curIndex + 1])
      this.$nextTick(() => {
        this.handlerAttachment()
      })
    },
    handlerAttachment() {
      if (Boolean(this.curRateInfo.attachment)) {
        this.attachmentsList = this.curRateInfo.attachment.split(',')
        return
      }
      this.attachmentsList = []
    },
    // 附件单个下载
    downLoad(item) {
      const data = {
        msgId: this.curRateInfo.id,
        fileName: item
      }
      _messageDownload(data).then((v) => {
        const link = document.createElement('a')
        if (v.type == 'application/json') {
          const reader = new FileReader() //创建一个FileReader实例
          reader.readAsText(v, 'utf-8') //读取文件,结果用字符串形式表示
          reader.onload = () => {
            const { msg } = JSON.parse(reader.result)
            this.$message.error(msg) //弹出错误提示
          }
        } else {
          let blob = new Blob([v]) //如果后台返回的直接是blob对象类型，直接获取数据
          link.style.display = 'none'
          const url = window.URL || window.webkitURL || window.moxURL
          link.href = url.createObjectURL(blob)
          link.download = item //下载的文件名称
          link.click()
          window.URL.revokeObjectURL(url)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.rate-info-container {
  .nav-info {
    text-align: right;
    padding-right: 10px;
    padding-bottom: 10px;
    .el-divider {
      margin: 0;
    }
    .control {
      padding: 0 10px;
      vertical-align: middle;
      cursor: pointer;
      &:hover {
        color: #5270dd;
      }
    }
  }
  .content {
    height: 70vh;
    padding: 0 10px;
    overflow: auto;
    .info-header {
      padding-bottom: 6px;
      border-bottom: 1px solid #dbedf3;
      .info-type {
        font-size: 12px;
        color: #666;
      }
      .title {
        font-size: 18px;
        font-weight: 700;
        margin-bottom: 10px;
      }
    }
    .info-content {
      padding: 16px 0 34px 0;
      border-bottom: 1px solid #dbedf3;
      white-space: pre-wrap;
      line-height: 1.4;
    }
    .info-footer {
      margin-top: 20px;
      .title {
        font-weight: 700;
        margin-bottom: 10px;
      }
      .attachments-list {
        .item {
          margin-bottom: 10px;
          .item-name {
            margin-right: 20px;
          }
          .downLoad {
            cursor: pointer;
            &:hover {
              color: #5270dd;
              text-decoration: underline;
            }
          }
        }
      }
    }
  }
}
</style>
