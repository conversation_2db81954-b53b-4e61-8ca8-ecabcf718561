<template>
  <div>
    <el-menu
      :default-active="activeIndex"
      class="el-menu-demo"
      mode="horizontal"
      @select="handleSelect"
    >
      <el-menu-item
        :index="item.index"
        v-for="item in menulist"
        :key="item.index"
        ><div>{{ item.name }} <i></i></div>
      </el-menu-item>
    </el-menu>
    <MainCard>
      <div class="emr-container">
        <component :is="comName" :selectedProject="activeporject"></component>
      </div>
    </MainCard>
  </div>
</template>

<script>
import CatalogueConfiguration from "./CatalogueConfiguration/index.vue";
import BasicdataDictionary from "./BasicdataDictionary/index.vue";
import MedicalRecordDataDictionary from "./MedicalRecordDataDictionary/index.vue";
import QualitativeDataDictionary from "./QualitativeDataDictionary/index.vue";
export default {
  data() {
    return {
      activeIndex: "1",
      activeporject: "",
      comName: CatalogueConfiguration,
      menulist: [
        { index: "1", name: "质量文档规则配置" },
        { index: "2", name: "基础数据规则配置" },
        { index: "3", name: "病历数据规则配置" },
        { index: "4", name: "质量数据规则配置" },
      ],
      // projectList: [],
      // selectedProject: '',
    };
  },
  components: {
    CatalogueConfiguration,
    BasicdataDictionary,
    MedicalRecordDataDictionary,
    QualitativeDataDictionary,
  },
  created() {
    if (this.$route.params.id === undefined) {
      this.activeporject = JSON.parse(sessionStorage.getItem("projectactive"));
    } else {
      this.activeporject = this.$route.params;
    }
  },
  methods: {
    handleSelect(key, keyPath) {
      this.activeIndex = key;
      if (key === "1") {
        this.comName = CatalogueConfiguration;
      } else if (key === "2") {
        this.comName = BasicdataDictionary;
      } else if (key === "3") {
        this.comName = MedicalRecordDataDictionary;
      } else if (key === "4") {
        this.comName = QualitativeDataDictionary;
      } else {
        this.comName = "";
      }
    },
  },
};
</script>

<style scoped lang="scss">
.el-menu {
  margin-bottom: 10px;
  background: transparent;
  border-bottom: 0px solid #000;
}

.el-menu--horizontal > .el-menu-item {
  background: transparent;
  margin-right: 20px;
  height: 40px;
  font-size: 16px;
  line-height: 40px;
}
.el-menu--horizontal > .el-menu-item:hover {
  background: transparent;
}

.el-menu--horizontal > .el-menu-item.is-active {
  font-weight: bold;
  font-size: 16px;
  color: #5270dd;
  background: transparent;
  border-radius: 10px 10px 0px 0px;
  border: 0px solid transparent;

  div {
    position: relative;

    i {
      position: absolute;
      bottom: 0px;
      left: 25%;
      padding: 2px 8px;
      background: #5270dd;
      width: 50%;
      border-radius: 3px;
    }
  }
}
.header {
  ::v-deep .el-input__inner {
    border: none;
    box-shadow: none;
    font-size: 20px;
    color: red;
    font-weight: bold;
  }
  ::v-deep .el-input {
    margin-bottom: 20px;
  }
}
</style>
