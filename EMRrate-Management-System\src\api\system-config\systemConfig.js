import request from '@/utils/request'


/* 1. 系统配置 */

// 获取所有系统配置
export function querySysConfig() {
    return request({
        url: "/system/querySysConfig"
    })
}

// 添加系统配置
export function addSysConfigs(data) {
    return request({
        url: "/system/addSysConfigs",
        method: "post",
        data
    })
}
// 更新系统配置
export function updateSysConfigs(data) {
    return request({
        url: "/system/updateSysConfigs",
        method: "post",
        data
    })
}

//获取医院图标
export function getHospitalLogoPath(params) {
    return request({
        url: '/system/getHospitalLogoPath',
        method: 'get',
        params
    })
}

//上传医院图标
export function setHospitalLogo(data) {
    return request({
        url: '/system/setHospitalLogo',
        method: 'post',
        data
    })
}