<template>
  <el-dialog
    v-dialogDrag
    :visible="dialogDrag"
    width="800px"
    :show-close="false"
    @close="closeCreateModal"
    :close-on-click-modal="false"
  >
    <div class="dialog-new-title">
      <h4>{{ dialogtitle }}</h4>
      <i class="el-icon-close" @click="handlerClose()"></i>
    </div>
    <el-form
      :model="createForm"
      class="dialog-body"
      ref="createFormRef"
      :rules="rules"
    >
      <div class="dialog-form-item">
        <el-form-item label="项目名称" prop="projectName">
          <el-input
            style="width: 100%"
            v-model="createForm.projectName"
          ></el-input>
        </el-form-item>
        <el-form-item label="评价等级" prop="levelCode" label-width="85px">
          <el-radio-group v-model="createForm.levelCode" size="small">
            <el-radio v-for="(v, i) in levelList" :key="i" :label="v.levelCode">
              {{ v.levelName }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="项目成员" prop="personInCharge" label-width="85px">
        </el-form-item>

        <div class="member-list">
          <div class="dialog-left">
            <div class="leftmainlist">
              <el-input
                placeholder="查找人员…"
                suffix-icon="el-icon-search"
                v-model="input1"
                @input="queryUserInfoList()"
              >
              </el-input>
              <div class="userlist">
                <div
                  @click="addselect(item)"
                  v-for="item in useralllist"
                  :key="item.loginId"
                  class="itemuser"
                >
                  {{ item.userName }}
                </div>
              </div>
            </div>
          </div>
          <div class="dialog-right">
            <div class="listname">
              <b>已选</b>（{{ createForm.projectMembers.length }}）
            </div>
            <el-table :data="createForm.projectMembers" height="300px">
              <el-table-column
                label="姓名"
                prop="userName"
                min-width="100px"
              ></el-table-column>
              <el-table-column
                label="是否负责人"
                align="center"
                min-width="100px"
              >
                <template slot-scope="scope">
                  <el-checkbox v-model="scope.row.isincharge"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="50px">
                <template slot-scope="scope">
                  <img
                    v-show="!scope.row.isincharge"
                    src="@/assets/emrimg/delicon.png"
                    @click="delselect(scope.row)"
                  />
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </el-form>
    <div slot="footer">
      <el-button @click="dialogDrag = false" size="mini">取 消</el-button>
      <el-button type="primary" @click="createUser" size="mini"
        >保 存</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import {
  addProject as _addProject,
  deleteProjectById as _deleteProjectById,
  queryProject as _queryProject,
  queryProjectAdmin as _queryProjectAdmin,
  updateProject as _updateProject,
  querylevelDictionary as _querylevelDictionary,
} from "@/api/document-management/project-management";
import { queryUserInfoList } from "@/api/document-management/catalogue-configuration";
function defaultCreateForm() {
  return {
    projectName: "",
    personInCharge: [],
    levelCode: "",
    dataStartTime: "",
    dataEndTime: "",
    projectType: 0,
    projectMembers: [],
  };
}
export default {
  data() {
    return {
      createForm: defaultCreateForm(),
      rules: {
        projectName: [
          {
            trigger: "blur",
            required: true,
            message: "请填写项目名称",
          },
        ],
        personInCharge: [
          {
            trigger: "blur",
            required: true,
            message: "请选择项目负责人",
          },
        ],
        levelCode: [
          { required: true, message: "请选择评价等级", trigger: "change" },
        ],
      },
      levelList: [],
      personList: [],
      useralllist: [],
      input1: "",
      groupName: "",
      dialogDrag: false,
      dialogtitle: "", //动态标题
    };
  },
  async created() {
    await _querylevelDictionary({ pageNum: 1, pageSize: 100 }).then((res) => {
      if (res && "data" in res) {
        this.levelList = res.data.list;
      }
    });
    await _queryProjectAdmin({ projectType: 0 }).then((res) => {
      if (res && "data" in res) {
        this.personList = res.data;
      }
    });
    this.queryUserInfoList();
  },
  methods: {
    // 选择人员
    addselect(data) {
      if (
        this.createForm.projectMembers.filter((item) =>
          item.loginId.includes(data.loginId)
        ).length === 0
      ) {
        this.createForm.projectMembers.push({
          isincharge: false,
          userName: data.userName,
          loginId: data.loginId,
          gender: data.gender,
        });
      } else {
        this.$message({
          message: "该人员已选择，请选择其他人员",
          type: "error",
        });
      }
    },

    // 删除人员
    delselect(item) {
      this.createForm.projectMembers.splice(
        this.createForm.projectMembers.indexOf(item),
        1
      );
    },
    // 查询人员
    queryUserInfoList() {
      this.useralllist = [];
      queryUserInfoList({
        current: 1,
        size: 99999,
        userName: this.input1,
      }).then((res) => {
        this.useralllist = res.data.list;
      });
    },
    // 关闭弹窗
    closeCreateModal() {
      this.dialogDrag = false;
      this.createForm = defaultCreateForm();
      this.$store.commit("user/SET_ADDPROJECT", "");
    },
    // 不同方式打开弹窗展示不同的信息
    openCreateModal(type, category) {
      // 数据质量文档新增项目
      if ((type === "新增", category === "数据质量文档")) {
        this.dialogDrag = true;
        this.createForm.projectType = 0;
        this.dialogtitle = "新建数据质量项目";
      }
      // 实证材料管理新增项目
      else if ((type === "新增", category === "实证材料管理")) {
        this.dialogDrag = true;
        this.createForm.projectType = 1;
        this.dialogtitle = "新建实证材料管理项目";
      }
    },
    // 保存
    createUser() {
      // if (this.flag == "edit") {
      //   this.editUser();
      //   return;
      // }
      this.createForm.projectMembers.forEach((item) => {
        if (item.isincharge === true) {
          this.createForm.personInCharge.push(item.loginId);
        }
      });
      this.$refs["createFormRef"].validate((valid) => {
        if (valid) {
          _addProject(this.createForm).then((res) => {
            if (res && res.msg == "success") {
              this.$message.success("新增项目成功!");
              this.dialogDrag = false;
              this.createForm = defaultCreateForm();
              this.$parent.queryProject();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          return false;
        }
      });
    },
  },

  computed: {
    addproject() {
      return this.$store.state.user.addproject;
    },
  },
  watch: {
    addproject: {
      async handler(val) {
        this.openCreateModal("新增", val);
      },
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/emr-styles/emr-dialog.scss";

::v-deep .el-dialog__body {
  height: 100%;
  padding: 10px 20px;
  .dialog-new-title {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 10px 20px;
  }
}
.member-list {
  display: flex;
  justify-content: space-around;
  align-items: center;
  border: 1px solid #e3e6e8;
  border-radius: 9px;
  overflow: hidden;
  .dialog-left {
    width: 280px;
    border-right: 1px solid #e3e6e8;
    .leftmainlist {
      height: 341px;
      background: #ffffff;
      .el-input {
        margin: 10px 10%;
        width: 80%;
      }
      .userlist {
        height: 300px;
        overflow: scroll;
      }
      .itemuser {
        line-height: 30px;
        height: 30px;
        font-size: 13px;
        color: #333333;
        margin: 0px 10%;
        border-radius: 6px;
        padding: 0px 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        img {
          width: 14px;
          height: 14px;
        }
      }
      .itemuser:hover {
        background: #eef1f9;
      }
    }
  }
  .dialog-right {
    position: relative;
    flex: 1;
    padding: 10px 20px;
  }
  .listname {
    font-size: 14px;
    color: #333333;
    margin-bottom: 10px;
  }
}

::v-deep .el-table__header-wrapper {
  .has-gutter tr th {
    font-weight: 400;
  }
}
</style>