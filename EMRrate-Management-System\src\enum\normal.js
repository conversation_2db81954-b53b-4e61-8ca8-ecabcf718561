//身份证类型
let idCardType = {
    '01': '居民身份证',
    '02': '居民户口簿',
    '03': '护照',
    '04': '军官证',
    '05': '驾驶证',
    '06': '港澳居民来往内地通行证',
    '07': '台湾居民来往内地通行证',
    '99': '其他法定有效证件',
}

//
let dataType = {
    1: '初次导入',
    2: '增量导入',
    3: '创建患者',
    4: '更新患者',
    5: '合并患者',
    6: '拆分患者',
    7: '疑似确认',
    8: '疑似解除',
}

//索引状态
let indexStatusType = {
    0: '自动合并',
    1: '手动合并',
    2: '未合并'
}

//性别
let genderType = {
    1: '男',
    2: '女',
}

//WS日志 消息名称
let wsMessageType={
    'webAddPatient':'患者信息新增',
    "webUpdatePatient":"患者信息更新",
    "webQueryPatient":"患者信息搜索",
    "webMergePatient":"患者信息合并",
}
//消息日志 消息名称
let msgType={
    'webAddPatient':'患者信息新增',
    "webUpdatePatient":"患者信息更新",
}

//学位
let degreeType = {
    1: '名誉博士',
    2: '博士',
    201: "哲学博士学位",
    202: "经济学博士学位",
    203: "法学博士学位",
    204: "教育学博士学位",
    205: "文学博士学位",
    206: "历史学博士学位",
    207: "理学博士学位",
    208: "工学博士学位",
    209: "农学博士学位",
    210: "医学博士学位",
    211: "军事博士学位",
    212: "管理学博士学位",
    245: "临床医学博士学位专业学位",
    248: "兽医博士专业学位",
    250: "口腔医学博士专业学位",
    3: "硕士",
    301: "哲学硕士学位",
    302: "经济学硕士学位",
    303: "法学硕士学位",
    304: "教育学硕士学位",
    305: "文学硕士学位",
    306: "历史学硕士学位",
    307: "理学硕士学位",
    308: "工学硕士学位",
    309: "农学硕士学位",
    310: "医学硕士学位",
    311: "军事学硕士学位",
    312: "管理学硕士学位",
    341: "法律硕士专业学位",
    342: "教育硕士专业学位",
    343: "工程硕士专业学位",
    344: "建筑学硕士专业学位",
    345: "临床医学硕士专业学位",
    346: "工商管理硕士专业学位",
    347: "农业推广硕士专业学位",
    348: "兽医硕士专业学位",
    349: "公共管理硕士专业学位",
    350: "口腔医学硕士专业学位",
    351: "公共卫生硕士专业学位",
    352: "军事硕士专业学校",
    4: "学士",
    401: "哲学学士学位",
    402: "经济学学士学位",
    403: "法学学士学位",
    404: "教育学学士学位",
    405: "文学学士学位",
    406: "历史学学士学位",
    407: '理学学士学位',
    408: '工学学士学位',
    409: '农学学士学位',
    410: '医学学士学位',
    411: '军事学学士学位',
    412: '管理学学士学位',
    444: '建筑学学士专业学位',
}



export { idCardType, dataType, indexStatusType, genderType, degreeType ,wsMessageType,msgType}