<template>
  <MainCard>
    <div class="emr-container">
      <div class="emr-container-explain">
        依据《电子病历系统应用水平分级评价标准（试行）》国卫办医函〔2018〕
      </div>
      <div class="emr-container-main">
        <div class="emr-container-button">
          <el-button
            type="primary"
            icon="el-icon-folder-add"
            size="mini"
            @click="addnewoperation(0)"
            >添加一级目录</el-button
          >
        </div>
        <div class="emr-container-table-self-defined">
          <!-- 第一排标题 -->
          <div
            class="emr-container-table-title emr-container-table-self-defined-row"
          >
            <div class="son1"></div>
            <div class="son2">目录名称</div>
            <div class="son3">项目代码</div>
            <div class="son4">关联等级</div>
            <div class="son4">项目序号</div>
            <div class="son5">业务项目</div>
            <div class="son6">评价类别</div>
            <div class="son7">主要评价内容及备注</div>
            <div class="son9">操作</div>
          </div>
          <!-- 第一级 -->
          <div v-for="(item1, index1) in tableData" :key="item1.id">
            <div class="emr-container-table-self-defined-row">
              <div class="son1">
                <svg-icon
                  v-show="!Boolean(item1.id)"
                  icon-class="new"
                  style="font-size: 36px"
                />
                <i
                  v-show="Boolean(item1.id) & item1.directionstate"
                  class="el-icon-arrow-down"
                  @click="item1.directionstate = false"
                ></i>
                <i
                  v-show="Boolean(item1.id) & (item1.directionstate === false)"
                  class="el-icon-arrow-right"
                  @click="loadnextlevel(1, item1, index1)"
                >
                </i>
              </div>
              <div class="son2">
                <div v-show="item1.editstate">
                  <el-input
                    :class="!Boolean(item1.id) ? 'noidinput' : ''"
                    v-model="item1.directoryName"
                    :placeholder="
                      item1.parentCode ? '' : '输入一级目录（工作角色）'
                    "
                  ></el-input>
                </div>
                <div v-show="!item1.editstate">
                  <b>{{ item1.directoryName }}</b>
                </div>
              </div>
              <div class="son3">
                <div v-show="item1.editstate">
                  <el-input
                    :class="!Boolean(item1.id) ? 'noidinput' : ''"
                    v-model="item1.directoryCode"
                    placeholder="请输入项目代码"
                  ></el-input>
                </div>
                <div v-show="!item1.editstate">
                  {{ item1.directoryCode }}
                </div>
              </div>
              <div class="son4"></div>
              <div class="son4"></div>
              <div class="son5"></div>
              <div class="son6"></div>
              <div class="son7"></div>
              <div class="son9">
                <span v-show="!item1.editstate">
                  <el-button
                    size="mini"
                    type="text"
                    @click="item1.editstate = true"
                  >
                    编辑
                  </el-button>
                  <el-divider direction="vertical"> </el-divider>
                </span>
                <span v-show="!item1.editstate">
                  <el-popconfirm
                    title="此操作将删除该等级, 是否继续?"
                    @onConfirm="deleteempiricalmaterialbutton(1, item1, index1)"
                  >
                    <el-button slot="reference" type="text"> 删除 </el-button>
                  </el-popconfirm>
                  <el-divider direction="vertical"> </el-divider>
                </span>

                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-plus"
                  v-show="!item1.editstate"
                  @click="addnewoperation(1, item1, index1)"
                >
                  添加项目
                </el-button>
                <span v-show="item1.editstate">
                  <el-button
                    size="mini"
                    type="primary"
                    @click="
                      updateandadd(1, item1, index1);
                      item1.editstate = false;
                    "
                  >
                    保存
                  </el-button>
                  <el-divider direction="vertical"> </el-divider>
                </span>
                <el-button
                  v-show="item1.editstate"
                  @click="editstatechange(1, item1, index1)"
                >
                  取消
                </el-button>
              </div>
            </div>
            <!-- 第二级 -->
            <div
              v-show="item1.directionstate"
              v-for="(item2, index2) in item1.secondtableData"
              :key="item2.id"
            >
              <div class="emr-container-table-self-defined-row">
                <div class="son1" style="text-align: center">
                  <svg-icon
                    v-show="!Boolean(item2.id)"
                    icon-class="new"
                    style="font-size: 36px"
                  />
                  <i
                    v-show="Boolean(item2.id) & item2.directionstate"
                    class="el-icon-arrow-down"
                    @click="item2.directionstate = false"
                  >
                  </i>
                  <i
                    v-show="
                      Boolean(item2.id) & (item2.directionstate === false)
                    "
                    class="el-icon-arrow-right"
                    @click="loadnextlevel(2, item1, index1, item2, index2)"
                  >
                  </i>
                </div>
                <div class="son2">
                  <div v-show="item2.editstate">
                    <el-input
                      :class="!Boolean(item2.id) ? 'noidinput' : ''"
                      v-model="item2.directoryName"
                      :placeholder="
                        item2.parentCode ? '' : '输入一级目录（工作角色）'
                      "
                    ></el-input>
                  </div>
                  <div v-show="!item2.editstate">
                    <span>{{ item2.directoryName }}</span>
                  </div>
                </div>
                <div class="son3">
                  <div v-show="item2.editstate">
                    <el-input
                      :class="!Boolean(item2.id) ? 'noidinput' : ''"
                      v-model="item2.directoryCode"
                      placeholder="请输入项目代码"
                    ></el-input>
                  </div>
                  <div v-show="!item2.editstate">
                    {{ item2.directoryCode }}
                  </div>
                </div>
                <div class="son4"></div>
                <div class="son4">{{item2.projectNum}}</div>
                <div class="son5">
                  <el-button
                    v-show="Boolean(item2.id)"
                    size="mini"
                    @click="
                      $refs.AddMainEvaluationContent.handlerOpen(
                        2,
                        item1,
                        index1,
                        item2,
                        index2
                      )
                    "
                  >
                    <svg-icon icon-class="icon_scale3" /> 打开</el-button
                  >
                </div>
                <div class="son6"></div>
                <div class="son7"></div>

                <div class="son9">
                  <span v-show="!item2.editstate">
                    <el-button
                      size="mini"
                      type="text"
                      @click="item2.editstate = true"
                      >编辑</el-button
                    >
                    <el-divider direction="vertical"></el-divider>
                  </span>
                  <span v-show="!item2.editstate">
                    <el-popconfirm
                      title="此操作将删除该等级, 是否继续?"
                      @onConfirm="
                        deleteempiricalmaterialbutton(
                          2,
                          item1,
                          index1,
                          item2,
                          index2
                        )
                      "
                    >
                      <el-button slot="reference" type="text">删除</el-button>
                    </el-popconfirm>
                    <el-divider direction="vertical"></el-divider>
                  </span>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-plus"
                    v-show="!item2.editstate"
                    @click="addnewoperation(2, item1, index1, item2, index2)"
                  >
                    添加子项
                  </el-button>
                  <span v-show="item2.editstate">
                    <el-button
                      size="mini"
                      type="primary"
                      @click="
                        updateandadd(2, item1, index1, item2, index2);
                        item2.editstate = false;
                      "
                      >保存</el-button
                    >
                    <el-divider direction="vertical"> </el-divider>
                  </span>
                  <el-button
                    v-show="item2.editstate"
                    @click="editstatechange(2, item1, index1, item2, index2)"
                    >取消</el-button
                  >
                </div>
              </div>
              <!-- 第三级 -->
              <div
                v-show="item2.directionstate"
                v-for="(item3, index3) in item2.thirdtableData"
                :key="item3.id"
              >
                <div class="emr-container-table-self-defined-row">
                  <div class="son1">
                    <svg-icon
                      v-show="!Boolean(item3.id)"
                      icon-class="new"
                      style="font-size: 36px"
                    />
                  </div>
                  <div class="son2">
                    <div v-show="item3.editstate">
                      <el-input
                        :class="!Boolean(item3.id) ? 'noidinput' : ''"
                        v-model="item3.directoryName"
                        :placeholder="
                          item3.parentCode ? '' : '输入一级目录（工作角色）'
                        "
                      ></el-input>
                    </div>
                    <div v-show="!item3.editstate">
                      <span>{{ item3.directoryName }}</span>
                    </div>
                  </div>
                  <div class="son3">
                    <div v-show="item3.editstate">
                      <el-input
                        :class="!Boolean(item3.id) ? 'noidinput' : ''"
                        v-model="item3.directoryCode"
                        placeholder="请输入项目代码"
                      ></el-input>
                    </div>
                    <div v-show="!item3.editstate">
                      {{ item3.directoryCode }}
                    </div>
                  </div>
                  <div class="son4">
                    <div>
                      <div v-show="item3.editstate">
                        <el-select v-model="item3.levelCode">
                          <el-option
                            v-for="item in levelCodeData"
                            :key="item.levelName"
                            :label="item.levelName"
                            :value="item.levelCode"
                          ></el-option>
                        </el-select>
                      </div>
                      <div v-show="!item3.editstate">
                        {{ item3.levelCode }}级
                      </div>
                    </div>
                  </div>
                  <div class="son4"></div>
                  <div class="son5"></div>
                  <div class="son6">
                    <div>
                      <div v-show="item3.editstate">
                        <el-select v-model="item3.evaluationCategory">
                          <el-option
                            v-for="item in emrRuleTypeData"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          ></el-option>
                        </el-select>
                      </div>
                      <div v-show="!item3.editstate">
                        <span
                          v-show="item3.evaluationCategory === '0'"
                          class="emr-tag emr-tag-jiben"
                          >基本项</span
                        >
                        <span
                          v-show="item3.evaluationCategory === '1'"
                          class="emr-tag emr-tag-xuanze"
                          >选择项</span
                        >
                      </div>
                    </div>
                  </div>
                  <div class="son7">
                    <el-button
                      v-show="
                        Boolean(item3.id) &
                        (item3.evaluationContents.length > 0)
                      "
                      size="mini"
                      @click="
                        $refs.AddMainEvaluationContent.handlerOpen(
                          3,
                          item1,
                          index1,
                          item2,
                          index2,
                          item3,
                          index3
                        )
                      "
                    >
                      <svg-icon icon-class="icon_scale3" /> 打开</el-button
                    >
                    <span
                      class="shezhi"
                      v-show="
                        (item3.evaluationContents.length === 0) &
                        Boolean(item3.id)
                      "
                      @click="
                        $refs.AddMainEvaluationContent.handlerOpen(
                          3,
                          item1,
                          index1,
                          item2,
                          index2,
                          item3,
                          index3
                        )
                      "
                      >未设置 <i class="el-icon-arrow-right"></i
                    ></span>
                  </div>
                  <div class="son9">
                    <span v-show="!item3.editstate">
                      <el-button
                        size="mini"
                        type="text"
                        @click="item3.editstate = true"
                      >
                        编辑
                      </el-button>
                      <el-divider direction="vertical"></el-divider>
                    </span>
                    <el-popconfirm
                      title="此操作将删除该等级, 是否继续?"
                      @onConfirm="
                        deleteempiricalmaterialbutton(
                          3,
                          item1,
                          index1,
                          item2,
                          index2,
                          item3,
                          index3
                        )
                      "
                    >
                      <el-button
                        slot="reference"
                        type="text"
                        v-show="!item3.editstate"
                        >删除</el-button
                      >
                    </el-popconfirm>
                    <span v-show="item3.editstate">
                      <el-button
                        size="mini"
                        type="primary"
                        @click="
                          updateandadd(
                            3,
                            item1,
                            index1,
                            item2,
                            index2,
                            item3,
                            index3
                          );
                          item3.editstate = false;
                        "
                        >保存</el-button
                      >
                      <el-divider direction="vertical"></el-divider>
                    </span>
                    <el-button
                      v-show="item3.editstate"
                      @click="
                        editstatechange(
                          3,
                          item1,
                          index1,
                          item2,
                          index2,
                          item3,
                          index3
                        )
                      "
                      >取消</el-button
                    >
                  </div>
                </div>
                <!-- 第三级 -->
              </div>
            </div>
          </div>
        </div>
        <AddMainEvaluationContent
          ref="AddMainEvaluationContent"
        ></AddMainEvaluationContent>
      </div>
    </div>
  </MainCard>
</template>

<script>
import {
  queryempiricalmaterial,
  deleteempiricalmaterial,
  batchUpdateempiricalmaterial,
  addempiricalmaterial,
} from "@/api/empirical-material-mgt";
import { querylevelDictionary } from "@/api/document-management/dictionary-configuration";
import AddMainEvaluationContent from "./components/AddMainEvaluationContent.vue";
export default {
  components: {
    AddMainEvaluationContent,
  },
  data() {
    return {
      tableData: [], // 表格数据
      loading: false,
      row: {}, // 点击编辑或结构设置时整行的数据
      emrRuleTypeData: [
        { label: "基本", value: "0" },
        { label: "选择", value: "1" },
      ],
      levelCodeData: [], //等级展示
    };
  },
  created() {
    // 进入页面初始化查询
    this.queryempiricalmaterial();
    // 查询等级
    querylevelDictionary({}).then((res) => {
      this.levelCodeData = res.data.list;
    });
  },
  methods: {
    // 查询列表最外层
    queryempiricalmaterial() {
      this.loading = true;
      this.tableData = [];
      queryempiricalmaterial({}).then((res) => {
        if (res.status === 0) {
          res.data.map((v) => {
            this.tableData.push(
              Object.assign(v, {
                editstate: false,
                directionstate: false,
                secondtableData: [],
              })
            );
          });
          this.loading = false;
        }
      });
    },
    // 打开子集
    loadnextlevel(type, item1, index1, item2, index2) {
      console.log(123);
      if (type === 1) {
        queryempiricalmaterial({
          directoryCode: item1.directoryCode,
        }).then((res) => {
          if (res.status === 0) {
            let newtableData = [];
            res.data.map((v) => {
              newtableData.push(
                Object.assign(v, {
                  editstate: false,
                  directionstate: false,
                  thirdtableData: [],
                })
              );
            });
            this.$nextTick(() => {
              this.tableData[index1].secondtableData = newtableData;
            });
            this.tableData[index1].directionstate = true;
          }
        });
      } else if (type === 2) {
        queryempiricalmaterial({
          directoryCode: item2.directoryCode,
        }).then((res) => {
          if (res.status === 0) {
            let newtableData = [];
            res.data.map((v) => {
              newtableData.push(
                Object.assign(v, { editstate: false, directionstate: false })
              );
            });
            this.$nextTick(() => {
              this.tableData[index1].secondtableData[index2].thirdtableData =
                newtableData;
            });
            this.tableData[index1].secondtableData[
              index2
            ].directionstate = true;
          }
        });
      }
    },
    // 不同新增操作，添加不同的目录
    addnewoperation(type, item1, index1, item2, index2) {
      let newobj = {
        directoryName: "",
        directoryCode: "",
        emrRuleType: "",
        levelCode: "",
        evaluationCategory: "",
        evaluationContents: [],
        businessProject: "",
        remarksDesc: "",
        id: null,
        editstate: true,
      };
      if (type === 0) {
        this.tableData.push(newobj);
      } else if (type === 1) {
        this.tableData[index1].directionstate
          ? this.tableData[index1].secondtableData.push(newobj)
          : (this.tableData[index1].secondtableData = [newobj]);
        this.tableData[index1].directionstate = true;
      } else if (type === 2) {
        this.tableData[index1].secondtableData[index2].directionstate
          ? this.tableData[index1].secondtableData[index2].thirdtableData.push(
              newobj
            )
          : (this.tableData[index1].secondtableData[index2].thirdtableData = [
              newobj,
            ]);
        this.tableData[index1].secondtableData[index2].directionstate = true;
      }
    },
    //提交保存和编辑操作
    async updateandadd(type, item1, index1, item2, index2, item3) {
      console.log(123123);
      if ((type === 1) & !Boolean(item1.id)) {
        await this.addempiricalmaterial(item1);
        this.queryempiricalmaterial();
      } else if ((type === 1) & Boolean(item1.id)) {
        await this.batchUpdateempiricalmaterial(item1);
      } else if ((type === 2) & !Boolean(item2.id)) {
        await this.addempiricalmaterial(item2);
        this.loadnextlevel(1, item1, index1);
      } else if ((type === 2) & Boolean(item2.id)) {
        await this.batchUpdateempiricalmaterial(item2);
      } else if ((type === 3) & !Boolean(item3.id)) {
        await this.addempiricalmaterial(item3);
        this.loadnextlevel(2, item1, index1, item2, index2);
      } else if ((type === 3) & Boolean(item3.id)) {
        await this.batchUpdateempiricalmaterial(item3);
      }
    },
    // 新增操作请求
    async addempiricalmaterial(item) {
      await addempiricalmaterial(item).then((res) => {
        if (res.status !== 0) {
          this.$message({
            message: res.msg,
            type: "error",
          });
          return;
        } else {
          this.$message({
            message: "新增目录成功",
            type: "success",
          });
        }
      });
    },
    // 修改操作请求
    async batchUpdateempiricalmaterial(item) {
      await batchUpdateempiricalmaterial([item]).then((res) => {
        if (res.status === 0) {
          this.$message({
            message: "保存成功!",
            type: "success",
          });
        } else {
          this.$message({
            message: res.msg,
            type: "error",
          });
        }
      });
    },
    // 改变编辑状态(取消)
    editstatechange(type, item1, index1, item2, index2, item3, index3) {
      if (type === 1) {
        if (this.tableData[index1].id) {
          queryempiricalmaterial({}).then((res) => {
            if (res.status === 0) {
              this.tableData[index1].directoryName =
                res.data[index1].directoryName;
              this.tableData[index1].directoryCode =
                res.data[index1].directoryCode;
              this.tableData[index1].editstate = false;
            }
          });
        } else {
          this.tableData.splice(index1, 1);
        }
      } else if (type === 2) {
        if (this.tableData[index1].secondtableData[index2].id) {
          queryempiricalmaterial({
            directoryCode: item1.directoryCode,
          }).then((res) => {
            if (res.status === 0) {
              this.tableData[index1].secondtableData[index2].directoryName =
                res.data[index2].directoryName;
              this.tableData[index1].secondtableData[index2].directoryCode =
                res.data[index2].directoryCode;
              this.tableData[index1].secondtableData[index2].editstate = false;
            }
          });
        } else {
          this.tableData[index1].secondtableData.splice(index2, 1);
        }
      } else if (type === 3) {
        if (
          this.tableData[index1].secondtableData[index2].thirdtableData[index3]
            .id
        ) {
          queryempiricalmaterial({
            directoryCode: item2.directoryCode,
          }).then((res) => {
            if (res.status === 0) {
              this.tableData[index1].secondtableData[index2].thirdtableData[
                index3
              ].directoryName = res.data[index2].directoryName;
              this.tableData[index1].secondtableData[index2].thirdtableData[
                index3
              ].directoryCode = res.data[index2].directoryCode;
              this.tableData[index1].secondtableData[index2].thirdtableData[
                index3
              ].levelCode = res.data[index2].levelCode;
              this.tableData[index1].secondtableData[index2].thirdtableData[
                index3
              ].evaluationCategory = res.data[index2].evaluationCategory;
              this.tableData[index1].secondtableData[index2].thirdtableData[
                index3
              ].editstate = false;
            }
          });
        } else {
          this.tableData[index1].secondtableData[index2].thirdtableData.splice(
            index3,
            1
          );
        }
      }
    },
    // 删除操作
    async deleteempiricalmaterialbutton(
      type,
      item1,
      index1,
      item2,
      index2,
      item3,
      index3
    ) {
      if (type === 1) {
        deleteempiricalmaterial({ ids: item1.id }).then((res) => {
          this.loading = true;
          if (res.status === 0) {
            this.$message({
              message: "删除成功!",
              type: "success",
            });
            this.tableData.splice(index1, 1);
          } else {
            this.$message({
              message: res.msg,
              type: "error",
            });
          }
        });
      } else if (type === 2) {
        deleteempiricalmaterial({ ids: item2.id }).then((res) => {
          this.loading = true;
          if (res.status === 0) {
            this.$message({
              message: "删除成功!",
              type: "success",
            });
            this.tableData[index1].secondtableData.splice(index2, 1);
          } else {
            this.$message({
              message: res.msg,
              type: "error",
            });
          }
        });
      } else if (type === 3) {
        deleteempiricalmaterial({ ids: item3.id }).then((res) => {
          this.loading = true;
          if (res.status === 0) {
            this.$message({
              message: "删除成功!",
              type: "success",
            });
            this.tableData[index1].secondtableData[
              index2
            ].thirdtableData.splice(index3, 1);
          } else {
            this.$message({
              message: res.msg,
              type: "error",
            });
          }
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
@import "@/styles/emr-styles/emr-main-table.scss";
</style>
