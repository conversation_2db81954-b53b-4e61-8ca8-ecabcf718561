<template>
  <div class="login-container-box">
    <div class="login-main">
      <div class="logo-part">
        <img
          :src="loginImg"
          alt="" />
      </div>
      <div class="login-content">
        <div class="login-left">
          <div class="description">
            <div class="dep-top">
              <div class="title">
                <span>以评促建</span><span>助力医院</span
                ><span class="digitization">数字化能力</span>
              </div>
              <div class="dep-list">
                <ul>
                  <li>
                    <img
                      :src="pic1"
                      alt="" /><span>电子病历系统应用水平分级评价</span>
                  </li>
                  <li>
                    <img
                      :src="pic1"
                      alt="" /><span>医院信息互联互通标准化成熟度测评</span>
                  </li>
                  <li>
                    <img
                      :src="pic1"
                      alt="" /><span
                      >医院智慧服务分级评价、医院智慧管理分级评价</span
                    >
                  </li>
                </ul>
              </div>
            </div>
            <div class="dep-bottom">
              <div class="dep-icon">
                <div class="item">
                  <img
                    :src="pic2"
                    alt="" />
                  <span class="bottom">电子病历评级</span>
                </div>
                <!-- <div class="item">
                  <img
                    :src="pic3"
                    alt="" />
                  <span class="bottom">互联互通测评</span>
                </div>
                <div class="item">
                  <img
                    :src="pic4"
                    alt="" />
                  <div class="bottom">智慧医院评级</div>
                </div> -->
              </div>
            </div>
          </div>
        </div>
        <div class="login-right">
          <div class="login-form">
            <el-card class="box-card">
              <div
                class="login-container"
                v-show="loginType === 1">
                <el-form
                  ref="loginForm"
                  :model="loginForm"
                  :rules="loginRules"
                  auto-complete="on"
                  label-position="left">
                  <div class="dt"></div>
                  <div class="flex-jc">
                    <div class="tip">用户登录 <span>Log in</span></div>
                    <el-link
                      v-show="identifyType !== '2'"
                      type="primary"
                      @click="loginChange"
                      >{{ loginFlag ? '短信登录' : '账号密码登录' }}</el-link
                    >
                  </div>
                  <el-col :span="24">
                    <el-form-item prop="username">
                      <el-input
                        ref="username"
                        v-model="loginForm.username"
                        placeholder="请输入登录账号"
                        name="username"
                        type="text"
                        tabindex="1"
                        :disabled="identifyType == '2'">
                        <i
                          style="padding: 13px 0 0 6px; font-size: 16px"
                          slot="prefix"
                          class="el-icon-user"></i
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col
                    :span="24"
                    v-show="loginFlag">
                    <el-form-item prop="password">
                      <el-input
                        :key="passwordType"
                        ref="password"
                        v-model="loginForm.password"
                        :type="passwordType"
                        placeholder="请输入登录密码"
                        name="password"
                        tabindex="2"
                        auto-complete="new-password"
                        @keyup.enter.native="handleLogin">
                        <i
                          style="padding: 13px 0 0 6px; font-size: 16px"
                          slot="prefix"
                          class="el-icon-lock"></i
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col
                    :span="12"
                    v-show="identifyType !== '2' || !loginFlag">
                    <el-form-item prop="verificationCode">
                      <el-input
                        placeholder="请输入验证码"
                        v-model="loginForm.verificationCode"
                        type="text"
                        name="verificationCode"
                        tabindex="2"
                        auto-complete="on"
                        @keyup.enter.native="handleLogin">
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <img
                      :src="codeImage"
                      v-show="identifyType !== '2' && loginFlag"
                      style="
                        margin-left: 20px;
                        height: 42px;
                        width: 120px;
                        cursor: pointer;
                      "
                      @click="nextStep" />
                    <el-button
                      class="send-info"
                      v-show="!loginFlag"
                      style="margin-left: 20px"
                      :disabled="time > 0"
                      @click="sendMsg">
                      {{ time > 0 ? `${time}秒` : '发送短信' }}</el-button
                    >
                  </el-col>
                  <div class="btn">
                    <el-button
                      :loading="loading"
                      type="primary"
                      class="button"
                      @click.native.prevent="handleLogin"
                      round
                      >登 录</el-button
                    >
                  </div>

                  <div
                    style="width: 100%; display: flex; justify-content: center">
                    <el-link
                      :loading="loading"
                      style="margin-right: 10px"
                      class="button"
                      @click.native.prevent="qywxhandleLogin"
                      :underline="false">
                      <img
                        :src="pic10"
                        style="vertical-align: bottom"
                        alt=""
                        title="企业微信登录"
                        height="20px"
                        width="20px" />
                      企业微信
                    </el-link>
                    <el-link
                      :loading="loading"
                      class="button"
                      @click.native.prevent="wxhandleLogin"
                      :underline="false">
                      <img
                        :src="pic11"
                        style="vertical-align: bottom"
                        title="微信登录"
                        alt=""
                        height="20px"
                        width="20px" />
                      微信
                    </el-link>
                  </div>
                </el-form>
              </div>
              <div
                class="login-container"
                v-show="loginType === 2">
                <div
                  class="qrCode"
                  :style="{ height: tipFlag ? '450px' : '400px' }">
                  <div style="margin-top: 4px">
                    <el-link
                      icon="el-icon-back"
                      @click.native.prevent="backLogin"
                      :underline="false">
                      返回</el-link
                    >
                    <span style="margin-left: 70px"> 企业微信扫码 </span>
                  </div>
                  <el-image
                    :src="qyQRurl"
                    fit="cover"
                    style="width: 100%; margin-top: 20px"
                    loading>
                    <div
                      slot="error"
                      class="image-slot">
                      <i class="el-icon-loading"></i> 加载中...
                    </div>
                  </el-image>
                  <div
                    class="success"
                    v-show="tipFlag">
                    <el-button
                      type="success"
                      icon="el-icon-check"
                      circle></el-button>
                    扫描成功，请点击确认登录
                  </div>
                </div>
              </div>
              <div
                class="login-container"
                v-show="loginType === 3">
                <div class="wxqrCode">
                  <div style="margin-top: 10px">
                    <el-link
                      icon="el-icon-back"
                      @click.native.prevent="backLogin"
                      :underline="false">
                      返回</el-link
                    >
                    <span style="margin-left: 80px"> 微信扫码 </span>
                  </div>
                  <div
                    id="qrcode"
                    style="width: 100%; margin-top: 20px"
                    ref="qrcode"></div>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </div>
    <div class="login-footer">
      <div class="about">
        关于佳缘科技 | 联系我们 | 帮助中心 | Copyright ©1994-2016 Scjydz.com All
        rights reserved.
      </div>
      <div class="support">
        技术支持：佳缘科技股份有限公司 联系电话：<span>028-62122223</span>
      </div>
    </div>
  </div>
  <!-- <div class="container">
    <div class="logoContainer">
      <img
        :src="loginImg"
        alt=""
        width="300" />
    </div>
    <div class="title-container">
      <div>电子病历应用水平分级管理系统</div>
      <div>EMRrate Management System</div>
    </div>
    <div
      class="login-container"
      v-show="loginType === 1">
      <el-form
        ref="loginForm"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        auto-complete="on"
        label-position="left">
        <div class="dt"></div>

        <div class="flex-jc">
          <div class="tip">用户登录 <span>Log in</span></div>
          <el-button
            v-show="identifyType !== '2'"
            type="text"
            @click="loginChange"
            >{{ loginFlag ? '短信登录' : '账号密码登录' }}</el-button
          >
        </div>
        <el-col :span="24">
          <el-form-item prop="username">
            <span class="svg-container">
              <svg-icon icon-class="user" />
            </span>
            <el-input
              ref="username"
              v-model="loginForm.username"
              placeholder="用户名"
              name="username"
              type="text"
              tabindex="1"
              :disabled="identifyType == '2'" />
          </el-form-item>
        </el-col>
        <el-col
          :span="24"
          v-show="loginFlag">
          <el-form-item prop="password">
            <span class="svg-container">
              <svg-icon icon-class="password" />
            </span>
            <el-input
              :key="passwordType"
              ref="password"
              v-model="loginForm.password"
              :type="passwordType"
              placeholder="密码"
              name="password"
              tabindex="2"
              auto-complete="new-password"
              @keyup.enter.native="handleLogin" />
            <span
              class="show-pwd"
              @click="showPwd">
              <svg-icon
                :icon-class="
                  passwordType === 'password' ? 'eye' : 'eye-open'
                " />
            </span>
          </el-form-item>
        </el-col>
        <el-col
          :span="12"
          v-show="identifyType !== '2' || !loginFlag">
          <el-form-item prop="verificationCode">
            <el-input
              placeholder="验证码"
              v-model="loginForm.verificationCode"
              type="text"
              name="verificationCode"
              tabindex="2"
              auto-complete="on"
              @keyup.enter.native="handleLogin" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <img
            :src="codeImage"
            v-show="identifyType !== '2' && loginFlag"
            style="
              margin-left: 20px;
              height: 42px;
              width: 120px;
              cursor: pointer;
            "
            @click="nextStep" />
          <el-button
            v-show="!loginFlag"
            style="margin-left: 20px"
            :disabled="time > 0"
            @click="sendMsg">
            {{ time > 0 ? `${time}秒` : '发送短信' }}</el-button
          >
        </el-col>
        <div style="width: 100%; display: flex">
          <el-button
            :loading="loading"
            type="primary"
            class="button"
            @click.native.prevent="handleLogin"
            round
            >登 录</el-button
          >
        </div>
        <div style="width: 100%; display: flex">
          <el-link
            :loading="loading"
            class="button"
            @click.native.prevent="qywxhandleLogin"
            :underline="false">
            <img
              :src="pic10"
              alt=""
              height="20px"
              width="20px" />
            企业微信</el-link
          >
          <el-link
            :loading="loading"
            class="button"
            @click.native.prevent="wxhandleLogin"
            :underline="false">
            <img
              :src="pic11"
              alt=""
              height="20px"
              width="20px" />
            微信</el-link
          >
        </div>
      </el-form>
      <div
        class="copyright-tip"
        v-html="
          '技术支持：佳缘科技股份有限公司&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;联系电话：028-62122223&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;：V1.0'
        "></div>
    </div>
    <div
      class="login-container"
      v-show="loginType === 2">
      <div
        class="qrCode"
        :style="{ height: tipFlag ? '450px' : '400px' }">
        <div style="margin-top: 4px">
          <el-link
            icon="el-icon-back"
            @click.native.prevent="backLogin"
            :underline="false">
            返回</el-link
          >
          <span style="margin-left: 70px"> 企业微信扫码 </span>
        </div>
        <el-image
          :src="qyQRurl"
          fit="cover"
          loading>
          <div
            slot="error"
            class="image-slot">
            <i class="el-icon-loading"></i> 加载中...
          </div>
        </el-image>
        <div
          class="success"
          v-show="tipFlag">
          <el-button
            type="success"
            icon="el-icon-check"
            circle></el-button>
          扫描成功，请点击确认登录
        </div>
      </div>
    </div>
    <div
      class="login-container"
      v-show="loginType === 3">
      <div class="wxqrCode">
        <div style="margin-top: 10px">
          <el-link
            icon="el-icon-back"
            @click.native.prevent="backLogin"
            :underline="false">
            返回</el-link
          >
          <span style="margin-left: 80px"> 微信扫码 </span>
        </div>
        <div
          style="margin-left: 20px; margin-top: 10px"
          id="qrcode"
          ref="qrcode"></div>
      </div>
    </div>
  </div> -->
</template>

<script>
import Logo from '@/layout/components/Logo.vue'
import pic1 from '@/assets/login/icon_1.png'
import pic2 from '@/assets/login/icon_2.png'
import pic3 from '@/assets/login/icon_3.png'
import pic4 from '@/assets/login/icon_4.png'
import loginBg from '@/assets/login/loginBg.jpg'
import pic10 from '@/assets/sysPic/10.png'
import pic11 from '@/assets/sysPic/11.png'
import pic12 from '@/assets/sysPic/12.png'
import iconNC from '@/assets/background/dashboard-logoNC.png'
// import { queryAllSysConfig } from "@/api/sys-config"
import autoLogout from '@/mixins/autoLogout'
import {
  getQywxInfo as _getQywxInfo,
  getScanResult as _getScanResult,
  qywxLogin as _qywxLogin,
  getQrCode as _getQrCode,
  bindAccount as _bindAccount,
  getAuthUrl as _getAuthUrl,
  weixinLogin as _weixinLogin,
  getIdentifyInfo as _getIdentifyInfo,
  sendPin as _sendPin,
  getImagePin as _getImagePin
} from '@/api/user'
import { getHospitalLogoPath as _getHospitalLogoPath } from '@/api/sys-config'
import { setToken } from '@/utils/auth'
import QRCode from 'qrcodejs2'
import axios from 'axios'
import { version } from 'moment'
export default {
  name: 'Login',
  components: {
    Logo
  },
  mixins: [autoLogout],
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!validUsername(value)) {
        callback(new Error('请输入正确的用户名！'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value.length < 6) {
        callback(new Error('密码不能少于六个字符！'))
      } else {
        callback()
      }
    }
    return {
      loginForm: {
        username: '',
        password: '',
        verificationCode: '',
        phoneNumber: ''
      },
      loginRules: {
        username: [
          { required: true, message: '请输入登录账号', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入登录密码', trigger: 'blur' }
        ],
        verificationCode: [
          { required: true, message: '请输入验证码', trigger: 'blur' }
        ]
      },
      loading: false,
      passwordType: 'password',
      redirect: undefined,
      loginType: 1,
      qyQRurl: '',
      wxQRurl: '',
      pic1,
      pic2,
      pic3,
      pic4,
      loginBg,
      pic10,
      pic11,
      pic12,
      loginImg: '',
      iconNC,
      timer: '',
      qyState: '',
      identifyType: '0',
      codeImage: '',
      time: '',
      uuid: '',
      flag: true,
      loginFlag: true,
      tipFlag: false,
      autoLogoutTime: ''
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  created() {
    _getImagePin().then((res) => {
      this.uuid = res.data.uuid
      this.codeImage = res.data.codeImage
      // console.log(res)
    })
    _getHospitalLogoPath({ logoType: 'system.logo.hospitalImg1' }).then(
      (res) => {
        console.log(res.data)
        this.loginImg = res.data
      }
    )
  },
  methods: {
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      this.loading = true
      const data = {
        ...this.loginForm,
        identifyType: this.identifyType,
        uuid: this.uuid,
        authenticationType: this.identifyType === '2' ? '1' : '0'
      }
      this.$store
        .dispatch('user/login', data)
        .then(() => {
          this.$router.push({ path: '/' })
          this.loading = false
          // 计时自动登出
          this.enableAutoLogout()
        })
        .catch((v) => {
          this.loading = false
          if (v == 'ADD_ACCOUNT_CODE') {
            this.loginFlag = !this.loginFlag
            this.identifyType = '2'
            this.loginForm.verificationCode = ''
          } else if (v == 'ADD_ACCOUNT_PASSWORD') {
            this.loginFlag = !this.loginFlag
            this.identifyType = '2'
            this.loginForm.verificationCode = ''
          } else if (
            v == 'CHOOSE_ACCOUNT_PASSWORD' ||
            v == 'CHOOSE_ACCOUNT_CODE'
          ) {
            this.loginFlag = !this.loginFlag
            this.loginForm.verificationCode = ''
          }
        })
    },
    wxhandleLogin() {
      this.loginType = 3
      _getAuthUrl().then((res) => {
        // console.log(res)
        this.$refs.qrcode.innerHTML = ''
        if (res.data) {
          new QRCode(this.$refs.qrcode, {
            width: 280,
            height: 280,
            text: res.data.authUrl
          })
          this.timer = setInterval(() => {
            _weixinLogin({ state: res.data.state }).then((result) => {
              if (result.status === 0) {
                clearInterval(this.timer)
                if (result.data.msg == 'UNBOUND_ACCOUNT') {
                  this.$prompt('该微信尚未绑定账号，请先绑定账号', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消'
                  })
                    .then(({ value }) => {
                      const data = {
                        userAccount: value,
                        userName: result.data.name,
                        wxAccount: result.data.wxAccount
                      }
                      _bindAccount(data).then((item) => {
                        // console.log(item)
                        if (item.msg == 'success') {
                          setToken(item.data)
                          this.$router.push({ path: '/' })
                        } else {
                          this.$message.error(item.msg)
                          this.loginType = 1
                        }
                      })
                    })
                    .catch(() => {
                      this.$message({
                        type: 'info',
                        message: '取消输入'
                      })
                    })
                } else {
                  setToken(result.data.token)
                  this.$router.push({ path: '/' })
                }
              } else if (result.msg == 'QRCODE_SCAN_ERR') {
                clearInterval(this.timer)
                // this.$message.error("二维码过期，请重新扫码");
                // this.wxhandleLogin();
                if (this.flag) {
                  this.flag = false
                  this.$confirm('二维码已过期, 是否刷新?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '返回',
                    type: 'warning'
                  })
                    .then(() => {
                      this.wxhandleLogin()
                    })
                    .catch(() => {
                      this.backLogin()
                    })
                }
              }
            })
          }, 1000)
        }
      })
    },

    // 企业微信登录
    qywxhandleLogin() {
      this.loginType = 2
      this.qyQRurl = ''
      this.tipFlag = false
      _getQywxInfo().then((res) => {
        // this.qyQRurl = res.data.qrCodeUrl;
        this.qyState = res.data.state
        if (res.data.qrCodeUrl) {
          this.loopArray({ key: res.data.key })
          this.timer = setInterval(() => {
            return this.loopArray({ key: res.data.key })
            // console.log(123);
          }, 20000)
        }
        _getQrCode({ key: res.data.key }).then((res) => {
          this.qyQRurl = 'data:application/pdf;base64,' + res.data
        })
      })
    },
    loopArray(v) {
      _getScanResult(v).then((res) => {
        if (res.msg == 'QRCODE_SCAN_ERR') {
          clearInterval(this.timer)
          if (this.flag) {
            this.flag = false
            this.$confirm('二维码已过期, 是否刷新?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '返回',
              type: 'warning'
            })
              .then(() => {
                this.qywxhandleLogin()
              })
              .catch(() => {
                this.backLogin()
              })
          }
        } else if (res.msg == 'QRCODE_SCAN_ING') {
          clearInterval(this.timer)
          this.tipFlag = true
          this.flag = true
          this.timer = setInterval(() => {
            this.loopArray(v)
          }, 1000)
        } else if (res.status == 0) {
          if (this.flag) {
            this.flag = false
            const data = {
              code: res.data,
              state: this.qyState
            }
            _qywxLogin(data).then((result) => {
              if (result.status == 0) {
                if (result.data.msg == 'UNBOUND_ACCOUNT') {
                  this.$prompt('该企业微信尚未绑定账号，请先绑定账号', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消'
                  })
                    .then(({ value }) => {
                      const data = {
                        userAccount: value,
                        userName: result.data.name,
                        wxAccount: result.data.wxAccount
                      }
                      _bindAccount(data).then((item) => {
                        // console.log(item)
                        if (item.msg == 'success') {
                          setToken(item.data)
                          this.$router.push({ path: '/' })
                        } else {
                          this.$message.error(item.msg)
                          this.loginType = 1
                        }
                      })
                    })
                    .catch(() => {
                      this.$message({
                        type: 'info',
                        message: '取消输入'
                      })
                    })
                } else {
                  setToken(result.data.token)
                  this.$router.push({ path: '/' })
                }
              } else {
                this.$message.error(result.msg)
                this.loginType = 1
              }
            })
            clearInterval(this.timer)
          }
        } else if (res.msg == '扫码错误') {
          this.backLogin()
          this.$message('取消登录')
        }
      })
    },
    backLogin() {
      this.loginType = 1
      this.flag = true
      clearInterval(this.timer)
    },

    nextStep() {
      _getImagePin().then((res) => {
        this.uuid = res.data.uuid
        this.codeImage = res.data.codeImage
      })
    },
    sendMsg() {
      _sendPin({ loginId: this.loginForm.username }).then((res) => {
        if (res.status == 0) {
          this.time = 60
          this.timer = setInterval(() => {
            if (this.time > 0) {
              this.time -= 1
            } else {
              clearInterval(this.timer)
            }
          }, 1000)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    loginChange() {
      this.loginFlag = !this.loginFlag
      if (this.identifyType == '0') {
        this.identifyType = '1'
      } else {
        this.identifyType = '0'
      }
    }
  },
  beforeDestroy() {
    clearInterval(this.timer)
  }
}
</script>

<style lang="scss">
$bg: #ccc;
$cursor: #ccc;
.container {
  .el-input {
    display: inline-block;
    width: 80%;
    input {
      background: transparent;
      border: 0px;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
    }
  }
  .el-form-item {
    border: 1px solid rgba(0, 0, 0, 0.1);
    background: white;
    margin-bottom: 20px;
    .el-form-item__content {
      line-height: 22px;
    }
  }
}
.phone {
  .el-input {
    width: 100%;
  }
}
</style>

<style lang="scss" scoped>
.login-container-box {
  background: url(~@/assets/login/loginBg.jpg) top/cover no-repeat;
  .login-main {
    position: relative;
    width: 1100px;
    height: 100vh;
    margin: 0 auto;
    .logo-part {
      height: 110px;
      position: absolute;
      left: 0;
      top: 20px;
      text-align: left;
      img {
        height: 100%;
      }

    }
    .login-content {
      display: flex;
      .login-left {
        width: 62%;
        position: relative;
        .description {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          .dep-top {
            .title {
              letter-spacing: 1px;
              font-size: 32px;
              margin-bottom: 22px;
              white-space: nowrap;
              font-weight: 700;
              color: #2c3148;
              span:first-child {
                margin-right: 20px;
              }
              .digitization {
                color: #5270dd;
              }
            }
            .dep-list {
              margin-bottom: 60px;
              font-size: 16px;
            }
            .dep-list ul li {
              line-height: 34px;
              img {
                vertical-align: middle;
                margin-right: 8px;
              }
              &:first-child span {
                position: relative;
                z-index: 1;
                &::before {
                  content: '';
                  position: absolute;
                  z-index: -1;
                  bottom: 0;
                  left: 0;
                  width: 64px;
                  height: 8px;
                  background-color: #ffcf72;
                }
              }
            }
          }
          .dep-bottom {
            .dep-icon {
              display: flex;
              .item {
                white-space: nowrap;
                font-size: 18px;
                display: flex;
                flex-direction: column;
                align-items: center;
                & + .item {
                  margin-left: 70px;
                }
                .bottom {
                  font-weight: 600;
                  color: #343951;
                  margin-top: 12px;
                }
              }
            }
            .title {
              font-size: 32px;
              margin-bottom: 34px;
              white-space: nowrap;
            }
            .dep-list ul li {
              line-height: 34px;
            }
          }
        }
      }
      .login-right {
        width: 38%;
        height: 100vh;
        .login-form {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          .box-card {
            width: 400px;
            height: 500px;
            padding: 40px 30px;
            .login-container {
              .el-form-item {
                margin-bottom: 26px;
              }
              ::v-deep .el-input--small .el-input__inner {
                height: 42px;
                line-height: 42px;
              }
              .send-info {
                height: 42px;
              }
              .flex-jc {
                margin-bottom: 40px;
                .tip {
                  font-weight: 600;
                  font-size: 22px;
                  color: #565d83 !important;
                }
              }
              .btn {
                margin-top: 10px;
                .el-button {
                  display: block;
                  width: 100%;
                  height: 40px;
                  border-radius: 6px;
                  font-size: 16px;
                }
                margin-bottom: 20px;
              }
            }
          }
        }
      }
    }
  }
  .login-footer {
    position: fixed;
    bottom: 34px;
    text-align: center;
    line-height: 24px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 13px;
    color: #979eae;
    .support {
      span {
        color: #6778ae;
      }
    }
  }
}

$bg: #2ca0bb;
$dark_gray: rgba(0, 0, 0, 0.2);
$light_gray: #eee;

.container {
  min-height: 100%;
  width: 100%;
  /* background-image: linear-gradient(to bottom, #1688af, #2ca0bb); */
  background-color: #d2e8ff;
  overflow: hidden;
  .lizi {
    height: 100vh;
  }
  .tip {
    margin-left: -36px;
    margin-bottom: 20px;
    font-size: 21px;

    color: rgba(0, 0, 0, 0.6);
    span {
      color: rgba(0, 0, 0, 0.2);
      font-size: 16px;
    }
  }

  .login-container {
    position: absolute;
    top: 30%;
    left: calc(50% - 250px);
    width: 520px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    .logoContainer {
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      // height: 100px;
      margin-bottom: 15px;
      display: flex;
      align-items: flex-end;
      padding-bottom: 15px;
      justify-content: space-around;

      .textPart {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        & > :nth-child(1) {
          font-size: 30px;
          letter-spacing: 2px;
        }
        & > :nth-child(2) {
          padding: 5px 0;
          font-size: 10px;
        }
      }
    }
    /* .login-form {
      position: relative;
      padding: 20px 55px 20px 55px;
      margin: 0 10px;
      background-color: #fcfcfc;
      border-radius: 10px;
      box-shadow: 0px 0px 10px rgb(0 0 0 / 35%);
      .dt {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 7px;
        border-top-left-radius: 7px;
        border-top-right-radius: 7px;
        background: linear-gradient(90deg, #b0d2ff, #fcc4e2);
      }
      .svg-container {
        padding: 6px 5px 6px 15px;
        color: $dark_gray;
        vertical-align: middle;
        width: 30px;
        display: inline-block;
      }
      .el-button {
        font-size: 15px;
      }
    } */

    /* .copyright-tip {
      font-size: 13px;
      color: white;
      text-align: center;
      padding: 10px 0;
    } */
  }

  .title-container {
    position: absolute;
    top: 17%;
    left: calc(50% - 270px);
    width: 560px;
    color: white;
    text-align: center;
    font-weight: 600px;
    :nth-child(1) {
      font-size: 40px;
      letter-spacing: 0px;
    }
    :nth-child(2) {
      font-size: 15px;
      letter-spacing: 4px;
      margin-top: 5px;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 12px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }

  .button {
    width: 100%;
    margin-bottom: 15px;
    margin-top: 5px;
    align-self: center;
    // background-image: linear-gradient(to left, #1688af, #2ca0bb);
  }
  .qrCode {
    width: 400px;
    // height: 400px;
    padding: 0 20px 0 20px;
    margin-left: 60px;
    background-color: #fff;
    font-size: 20px;
    line-height: 30px;
    // font-weight: bold;
  }
  .wxqrCode {
    width: 400px;
    height: 400px;
    padding: 0 20px 0 20px;
    margin-left: 60px;
    background-color: #fff;
    font-size: 20px;
    line-height: 30px;
    // font-weight: bold;
  }
  .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .yzm-container {
    padding: 6px 0px 6px 6px;
    color: $dark_gray;
    vertical-align: middle;
    width: 50px;
    display: inline-block;
  }
}
.flex-jc {
  display: flex;
  justify-content: space-between;
}
.logoContainer {
  position: absolute;
  top: 0%;
  left: 40px;
  // height: 100px;
  margin-bottom: 15px;
  margin-top: 20px;
  display: flex;
  align-items: flex-end;
  padding-bottom: 15px;
  justify-content: space-around;
}
.success {
  background-color: #fff;
  text-align: center;
}
</style>
