<template>
  <MainCard>
    <div class="emr-container">
      <div class="emr-headline">
        <i></i>
        病历数据
      </div>
      <div class="emr-container-main">
        <div class="emr-container-main-left">
          <!-- <div class="title">
            <b>9个角色共26项</b>
          </div> -->
          <div class="tree">
            <div v-for="(item1, index1) in treedata" :key="item1.id">
              <div @click="loadNode(1, index1)" class="treeitem">
                <i
                  :class="
                    item1.developedstate
                      ? 'el-icon-arrow-down'
                      : 'el-icon-arrow-right'
                  "
                ></i>
                {{ item1.directoryName }}
              </div>
              <div
                class="treeitem treeitem2"
                :style="[
                  {
                    background:
                      item2.directoryCode === activemenu ? '#E0E4F9' : '',
                  },
                ]"
                @click="handleNodeClick(item2,index1,index2)"
                v-for="(item2,index2) in item1.secondLevels"
                :key="item2.id"
              >
                {{ item2.directoryCode }} {{ item2.directoryName }}
              </div>
            </div>
          </div>
        </div>
        <div class="emr-container-main-right" v-show="showtabel.directoryCode">
          <div class="title">
            <b>{{ showtabel.directoryName }} </b>
            <span v-show="showtabel.directoryCode"
              >(编号：{{ showtabel.directoryCode }} )</span
            >
          </div>

          <el-table
            class="emr-container-table"
            :data="tableData"
            ref="sourceMgtTable"
            v-loading="loading"
            :header-cell-style="{ color: '#333' }"
          >
            <el-table-column prop="levelName" label="" width="100">
              <template slot-scope="scope">
                <div>
                  {{ scope.row.directoryCode }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="levelName" label="项目名称" min-width="400">
              <template slot-scope="scope">
                <div>
                  {{ scope.row.directoryName }}
                </div>
              </template>
            </el-table-column>

            <el-table-column
              prop="levelName"
              label="数量（SQL）"
              min-width="100"
            >
              <template slot-scope="scope">
                <el-button
                  @click="$refs.Dataallocation.handlerOpenredact(scope.row)"
                  size="mini"
                  v-if="
                    scope.row.associatedType === '0' ||
                    scope.row.associatedType === '1'
                  "
                >
                  <svg-icon icon-class="icon_scale3" /> 打开</el-button
                >
                <span
                  class="shezhi"
                  v-else
                  @click="$refs.Dataallocation.handlerOpenredact(scope.row)"
                  >未配置 <i class="el-icon-arrow-right"></i
                ></span>
              </template>
            </el-table-column>
            <!-- <el-table-column prop="levelName" label="等级" min-width="100">
            <template slot-scope="scope">
              <div>{{ scope.row.associationLevel }}级</div>
            </template>
          </el-table-column>
          <el-table-column prop="levelName" label="类型" min-width="100">
            <template slot-scope="scope">
              <div>
                <div>
                  <span v-show="scope.row.dataType === '0'">基础</span>
                  <span v-show="scope.row.dataType === '1'">选择</span>
                </div>
              </div>
            </template>
          </el-table-column> -->
            <el-table-column prop="levelName" label="单位" min-width="100">
              <template slot-scope="scope">
                <div>
                  {{ scope.row.dataUnit }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <Dataallocation ref="Dataallocation"></Dataallocation>
      </div>
    </div>
  </MainCard>
</template>

<script>
import {
  queryDirectoryTreerule,
  getTaskrule,
} from "@/api/document-management/catalogue-configuration";
import Dataallocation from "./components/Dataallocation.vue";
export default {
  components: {
    Dataallocation,
  },
  data() {
    return {
      tableData: [], // 表格数据

      loading: false,
      showtabel: {
        directoryName: "   ",
      },
      treedata: [],
      treedatacopy: [],
      activemenu: "",
    };
  },
  created() {
    this.loadNode();
  },
  methods: {
    // 获取左侧列表
    loadNode(level, index1) {
      queryDirectoryTreerule({
        configType: "2",
        needNotAllocationTask: false,
        levelCode: this.$store.state.user.projectMsg.levelCode,
        projectId: this.$store.state.user.projectMsg.id,
        userAccount: this.$store.state.user.loginId,
      }).then((res) => {
        this.treedata = [];
        this.treedata = res.data;
        this.handleNodeClick(this.treedata[0].secondLevels[0], 0, 0);
      });
    },

    // 查询表格
    // async handleNodeClick(data, index1, index2) {
    //   if (data.directoryCode.length === 5) {
    //     this.loading = true;
    //     this.tableData = [];
    //     this.showtabel = data;
    //     this.activemenu = data.directoryCode;
    //     this.treedata[index1].secondLevels[index2].thirdLevels.map((v) => {
    //       this.tableData.push(
    //         Object.assign(v, {
    //           editstate: false,
    //         })
    //       );
    //     });
    //     this.loading = false;
    //   }
    // },
    // 查询表格
    async handleNodeClick(data,index1,index2) {
      if (data.directoryCode.length === 5) {
        this.loading = true;
        this.tableData = [];
        this.showtabel = data;
        this.activemenu = data.directoryCode;
        await getTaskrule({
          configType: "2",
          levelCode: this.$store.state.user.projectMsg.levelCode,
          directoryCode: data.directoryCode,
          userAccount: this.$store.state.user.loginId,
          projectId: this.$store.state.user.projectMsg.id,
        }).then((res) => {
          res.data.rulePermissionList.map((v) => {
            this.tableData.push(
              Object.assign(v, {
                editstate: false,
              })
            );
          });
          this.loading = false;
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.emr-container-main {
  display: flex;
  .emr-container-main-left {
    width: 15%;
    .title {
      margin-top: 20px;
      margin-bottom: 10px;
      display: flex;
      width: 100%;
      justify-content: space-between;
      font-size: 15px;
      color: #333333;
    }
    .tree {
      height: 80%;
      background: #ffffff;
      border-radius: 9px;
      border: 1px solid #e3e6e8;
      padding: 10px;
      overflow: scroll;
      .treeitem {
        // height: 30px;
        font-size: 14px;
        line-height: 30px;
        word-break: break-word;
        width: 100%;
        cursor: pointer;
      }
      .treeitem2 {
        padding-left: 20px;
        border-radius: 5px;
      }
    }
  }
  .emr-container-main-right {
    flex: 1;
    width: 80%;
    .title {
      margin-top: 20px;
      margin-left: 30px;
      margin-bottom: 10px;
      font-size: 15px;
      color: #333333;
      background: url("./../../../assets/emrimg/titleBg.png");
      background-repeat: no-repeat;
      padding: 6px 20px;
      span {
        font-size: 13px;
      }
    }
    .add {
      padding-left: 20px;
      border-bottom: 1px solid #ebeef5;
      height: 40px;
      line-height: 40px;
    }
  }
}

.wid60 {
  width: 60px;
  display: inline-block;
}
</style>
