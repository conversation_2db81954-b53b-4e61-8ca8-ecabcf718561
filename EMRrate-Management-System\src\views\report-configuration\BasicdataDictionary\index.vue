<template>
  <MainCard>
    <div class="emr-container">
      <div class="emr-headline">
        <i></i>
        基础申报数据<span> — 医疗服务信息</span>
      </div>
      <div class="emr-container-main">
        <div class="source-mgt-table" v-loading="loading">
          <el-table
            class="emr-container-table"
            :data="tableData"
            ref="sourceMgtTable"
            v-loading="loading"
            :header-cell-style="{ color: '#333' }"
          >
            <el-table-column prop="levelName" label="项目名称" min-width="300">
              <template slot-scope="scope">
                <div
                  :style="`padding-left:${
                    scope.row.directoryCode.includes('.') ? 20 : 0
                  }px`"
                >
                  {{ scope.row.directoryCode }} {{ scope.row.directoryName }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="levelName"
              label="数量（SQL）"
              min-width="100"
            >
              <template slot-scope="scope">
                <el-button
                  @click="$refs.Dataallocation.handlerOpenredact(scope.row)"
                  size="mini"
                  v-if="
                    scope.row.associatedType === '0' ||
                    scope.row.associatedType === '1'
                  "
                >
                  <svg-icon icon-class="icon_scale3" /> 打开</el-button
                >
                <span class="shezhi" v-else
                  >未配置 <i class="el-icon-arrow-right"></i
                ></span>
              </template>
            </el-table-column>
            <el-table-column prop="dataUnit" label="单位" min-width="100">
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <Dataallocation ref="Dataallocation"></Dataallocation>
  </MainCard>
</template>

<script>
import { queryDirectoryTreerule } from "@/api/document-management/catalogue-configuration";
import Dataallocation from "./components/Dataallocation.vue";
export default {
  components: {
    Dataallocation,
  },
  data() {
    return {
      tableData: [], // 表格数据
      loading: false,
    };
  },
  created() {
    // 进入页面初始化查询
    this.queryDirectoryTreerule();
  },
  methods: {
    // 查询列表
    queryDirectoryTreerule() {
      this.loading = true;
      this.tableData = [];
      // 等级待修改
      queryDirectoryTreerule({
        configType: "1",
        needNotAllocationTask: false,
        levelCode: this.$store.state.user.projectMsg.levelCode,
        projectId: this.$store.state.user.projectMsg.id,
      }).then((res) => {
        if (res.status === 0) {
          this.tableData = res.data;
          this.loading = false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
