import request from '@/utils/request'

/* 1. 评级信息配置 */

// 查询列表
export function queryAllMsgList(data) {
  return request({
    url: '/message/queryAllMsgList',
    method: 'post',
    data
  })
}
// 新增
export function addMsg(data) {
  return request({
    url: '/message/addMsg',
    method: 'post',
    data
  })
}
// 更新
export function updateMsg(data) {
  return request({
    url: '/message/updateMsg',
    method: 'post',
    data
  })
}
// 删除
export function deleteMsg(params) {
  return request({
    url: '/message/deleteMsg',
    method: 'delete',
    params
  })
}
// 下载附件（支持下载单个和下载所有）
export function messageDownload(params) {
  return request({
    url: '/message/download',
    method: 'get',
    params,
    responseType:'blob'
  })
}
// 上传
export function saveDbStructure(data) {
  return request({
    url: '/metadataStructureChooseResult/saveDbStructure',
    method: 'post',
    data
  })
}



