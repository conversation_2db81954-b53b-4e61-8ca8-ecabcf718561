<template>
  <MainCard>
    <div class="emr-container">
      <div class="emr-container-main">
        <div class="emr-container-main-left">
          <div class="emr-headline">
            <span><i></i> 病历数据</span>
            <span
              class="aboutme"
              v-if="
                selectedProject.personInCharge.includes(
                  $store.state.user.loginId
                )
              "
            >
              是否和我相关
              <el-switch
                @change="loadNode()"
                v-model="aboutme"
                active-color="#4969de"
                inactive-color="#aaa"
              >
              </el-switch>
            </span>
          </div>
          <!-- <div class="title">
            <b>9个角色共26项</b>
          </div> -->
          <div class="tree">
            <div v-for="(item1, index1) in treedata" :key="item1.id">
              <div @click="loadNode(1, index1)" class="treeitem">
                <i
                  :class="
                    item1.developedstate
                      ? 'el-icon-arrow-down'
                      : 'el-icon-arrow-right'
                  "
                ></i>
                {{ item1.directoryName }}
              </div>
              <div
                class="treeitem treeitem2"
                :style="[
                  {
                    background:
                      item2.directoryCode === activemenu ? '#E0E4F9' : '',
                  },
                ]"
                @click="handleNodeClick(item2, index1, index2)"
                v-for="(item2, index2) in item1.secondLevels"
                :key="item2.id"
              >
                {{ item2.directoryCode }} {{ item2.directoryName }}
              </div>
            </div>
          </div>
        </div>
        <div class="emr-container-main-right" v-show="showtabel.directoryCode">
          <div class="title">
            <b>{{ showtabel.directoryName }} </b>
            <span v-show="showtabel.directoryCode"
              >(编号：{{ showtabel.directoryCode }} )</span
            >
          </div>

          <el-table
            class="emr-container-table"
            :data="tableData"
            ref="sourceMgtTable"
            v-loading="loading"
            :header-cell-style="{ color: '#333' }"
          >
            <el-table-column prop="levelName" label="" width="100">
              <template slot-scope="scope">
                <div>
                  {{ scope.row.directoryCode }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="levelName" label="项目名称" min-width="400">
              <template slot-scope="scope">
                <div>
                  {{ scope.row.directoryName }}

                  <svg-icon
                    v-show="scope.row.taskStatus === '2'"
                    icon-class="icon_success"
                    style="font-size: 24px"
                  ></svg-icon>
                </div>
              </template>
            </el-table-column>

            <el-table-column
              prop="levelName"
              label="数量（SQL）"
              min-width="100"
            >
              <template slot-scope="scope">
                <el-button
                  @click="$refs.Dataallocation.handlerOpenredact(scope.row)"
                  size="mini"
                  v-if="
                    scope.row.associatedType === '0' ||
                    scope.row.associatedType === '1'
                  "
                >
                  <svg-icon icon-class="icon_scale3" /> 打开</el-button
                >
                <span
                  class="shezhi"
                  v-else
                  @click="$refs.Dataallocation.handlerOpenredact(scope.row)"
                  >未配置 <i class="el-icon-arrow-right"></i
                ></span>
              </template>
            </el-table-column>
            <!-- <el-table-column prop="levelName" label="等级" min-width="100">
            <template slot-scope="scope">
              <div>{{ scope.row.associationLevel }}级</div>
            </template>
          </el-table-column>
          <el-table-column prop="levelName" label="类型" min-width="100">
            <template slot-scope="scope">
              <div>
                <div>
                  <span v-show="scope.row.dataType === '0'">基础</span>
                  <span v-show="scope.row.dataType === '1'">选择</span>
                </div>
              </div>
            </template>
          </el-table-column> -->
            <el-table-column prop="levelName" label="单位" min-width="100">
              <template slot-scope="scope">
                <div>
                  {{ scope.row.dataUnit }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template slot-scope="scope">
                <el-button @click="complete(scope.row)" type="text" size="small"
                  >完成</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <Dataallocation
          ref="Dataallocation"
          :selectedProject="selectedProject"
        ></Dataallocation>
      </div>
    </div>
  </MainCard>
</template>

<script>
import {
  queryDirectoryTreerule,
  getTaskrule,
} from "@/api/document-management/catalogue-configuration";
import {
  markComplete,
  getMedicaRecordsOrQualityRule,
} from "@/api/document-management/rule-configuration";

import Dataallocation from "./components/Dataallocation.vue";
export default {
  props: {
    selectedProject: {
      type: Object, // 根据实际情况调整类型
      required: true,
    },
  },
  components: {
    Dataallocation,
  },
  data() {
    return {
      tableData: [], // 表格数据

      loading: false,
      showtabel: {
        directoryName: "   ",
      },
      treedata: [],
      treedatacopy: [],
      activemenu: "",
      aboutme: true,
    };
  },
  created() {
    this.loadNode();
  },
  watch: {
    selectedProject(newValue, oldValue) {
      this.showtabel = {
        directoryName: "   ",
      };
      this.tableData = [];
      this.loadNode();
    },
  },
  methods: {
    // 获取左侧列表
    loadNode(level, index1) {
      queryDirectoryTreerule({
        configType: "2",
        needNotAllocationTask: false,
        levelCode: this.selectedProject.levelCode,
        userAccount: this.aboutme ? this.$store.state.user.loginId : "",
        projectId: this.selectedProject.id,
      }).then((res) => {
        this.treedata = [];
        this.treedata = res.data;
        if (this.treedata.length > 0) {
          this.handleNodeClick(this.treedata[0].secondLevels[0], 0, 0);
        }
      });
    },

    // 查询表格
    async handleNodeClick(data, index1, index2) {
      const value = data ? data : this.showtabel;
      if (value.directoryCode.length === 5) {
        this.loading = true;
        this.tableData = [];
        if (data) {
          this.showtabel = data;
        }
        this.activemenu = value.directoryCode;
        // this.treedata[index1].secondLevels[index2].thirdLevels.map((v) => {
        //   this.tableData.push(
        //     Object.assign(v, {
        //       editstate: false,
        //     })
        //   );
        // });
        getMedicaRecordsOrQualityRule({
          directoryType: 2,
          directoryCode: value.directoryCode,
          projectId: this.selectedProject.id,
          levelCode: this.selectedProject.levelCode,
          userAccount: this.aboutme ? this.$store.state.user.loginId : "",
        }).then((res) => {
          this.tableData = res.data;
        });
        this.loading = false;
      }
    },
    complete(row) {
      markComplete({
        directoryCode: row.directoryCode,
        directoryName: row.directoryName,
        projectId: this.selectedProject.id,
        configType: 2,
        userAccount: this.aboutme ? this.$store.state.user.loginId : "",
      }).then((res) => {
        if (res.status === 0) {
          this.$message({
            message: "标记完成成功!",
            type: "success",
          });
          // this.queryDirectoryTreerule();
          // console.log(this.activemenu)
          getMedicaRecordsOrQualityRule({
            directoryType: 2,
            directoryCode: this.activemenu,
            projectId: this.selectedProject.id,
            levelCode: this.selectedProject.levelCode,
            userAccount: this.aboutme ? this.$store.state.user.loginId : "",
          }).then((res) => {
            this.tableData = res.data;
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/emr-styles/emr-main-table.scss";

.emr-container-main {
  display: flex;
  border-radius: 0px !important;
  .emr-container-main-left {
    width: 15%;
    .title {
      margin-top: 20px;
      margin-bottom: 10px;
      display: flex;
      width: 100%;
      justify-content: space-between;
      font-size: 15px;
      color: #333333;
    }
    .tree {
      height: 80%;
      background: #ffffff;
      border-radius: 9px;
      border: 1px solid #e3e6e8;
      padding: 10px;
      overflow: scroll;
      .treeitem {
        // height: 30px;
        font-size: 14px;
        line-height: 30px;
        word-break: break-word;
        width: 100%;
        cursor: pointer;
      }
      .treeitem2 {
        padding-left: 20px;
        border-radius: 5px;
      }
    }
  }
  .emr-container-main-right {
    flex: 1;
    width: 80%;
    .title {
      margin-top: 20px;
      margin-left: 30px;
      margin-bottom: 10px;
      font-size: 15px;
      color: #333333;
      background: url("./../../../../assets/emrimg/titleBg.png");
      background-repeat: no-repeat;
      padding: 6px 20px;
      span {
        font-size: 13px;
      }
    }
    .add {
      padding-left: 20px;
      border-bottom: 1px solid #ebeef5;
      height: 40px;
      line-height: 40px;
    }
  }
}

.emr-headline {
  margin-bottom: 5px;
  display: flex;
  justify-content: space-between;
}
.aboutme {
  color: #aaa;
  font-size: 14px;
}
</style>
