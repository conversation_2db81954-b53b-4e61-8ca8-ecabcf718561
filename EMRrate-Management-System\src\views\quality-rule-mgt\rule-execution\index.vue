<template>
  <div class="rule-execution-container">
    <h1 class="rule-execution-title">规则执行</h1>
    <div class="rule-execution-main">
      <div class="left">
        <el-card shadow="never" v-loading="isTreeLoading"
          ><el-tree
            ref="sysTree"
            highlight-current
            :data="systemData"
            node-key="id"
            :props="defaultProps"
            @node-click="handleSelect"
            :current-node-key="1"
            default-expand-all
          ></el-tree
        ></el-card>
      </div>
      <div class="right">
        <el-card shadow="never">
          <template v-if="isSelected">
            <div class="rule-execution-header">
              <el-form :model="queryData" ref="ruleForm" inline>
                <el-form-item label="规则大类" prop="checkRuleFatherType">
                  <el-select
                    v-model="queryData.checkRuleFatherType"
                    @change="handlerChange"
                  >
                    <el-option label="全部" value=""></el-option>
                    <el-option
                      v-for="item in checkRuleFatherTypeData"
                      :key="item.id"
                      :label="item.contentValue"
                      :value="item.contentKey"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="规则小类" prop="checkRuleType">
                  <el-select
                    v-model="queryData.checkRuleType"
                    placeholder="请选择"
                  >
                    <el-option label="全部" value=""></el-option>
                    <el-option
                      v-for="item in checkRuleTypeData"
                      :key="item.id"
                      :label="item.contentValue"
                      :value="item.contentKey"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="问题分类" prop="qstType">
                  <el-select v-model="queryData.qstType">
                    <el-option label="全部" value=""></el-option>
                    <el-option
                      v-for="item in problemTypeData"
                      :key="item.id"
                      :label="item.contentValue"
                      :value="item.contentValue"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="检测表或视图" prop="checkRuleTableOrView">
                  <el-autocomplete
                    ref="abc"
                    v-model="queryData.checkRuleTableOrView"
                    :fetch-suggestions="querySearchAsync"
                    value-key="tableOrViewName"
                    placeholder="请输入内容"
                    @select="handlerTableOrViewSelect"
                    clearable
                  ></el-autocomplete>
                </el-form-item>

                <el-form-item label="检测字段" prop="checkRuleColumn">
                  <el-select
                    v-model="queryData.checkRuleColumn"
                    placeholder="请选择"
                    :disabled="isDisabled"
                    @click.native="handlerclick"
                    filterable
                  >
                    <el-option label="全部" value=""></el-option>
                    <el-option
                      v-for="item in checkRuleColumnData"
                      :key="item.id"
                      :label="item.fieldName"
                      :value="item.fieldName"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="规则名称：" prop="checkRuleName">
                  <el-input
                    v-model="queryData.checkRuleName"
                    placeholder="请输入规则名称"
                  ></el-input>
                </el-form-item>
                <el-form-item>
                  <div style="margin-left: 10px">
                    <el-button
                      type="primary"
                      @click="searchRuleRunInfoList"
                      icon="el-icon-search"
                      >搜索</el-button
                    >
                    <!-- 新增/编辑规则 -->
                    <el-popconfirm
                      @onConfirm="exeAllRuleConfig(1)"
                      @onCancel="exeAllRuleConfig(0)"
                      confirm-button-text="覆盖"
                      cancel-button-text="不覆盖"
                      title="是否覆盖？"
                      style="margin-left: 10px"
                    >
                      <el-button slot="reference">整体执行时间设置</el-button>
                    </el-popconfirm>
                    <el-popconfirm
                      @onConfirm="exeAllRuleBySys"
                      title="确定执行所有规则吗？"
                      style="margin-left: 10px"
                    >
                      <el-button slot="reference">整体执行</el-button>
                    </el-popconfirm>
                  </div>
                </el-form-item>
                <el-form-item>
                  <el-button @click="resetForm('ruleForm')">重置</el-button>
                </el-form-item>
              </el-form>
            </div>
            <OverallExeConfig
              ref="overallExeConfig"
              :row="row"
              v-bind="commomData"
            />
            <div class="rule-execution-table">
              <el-table
                :data="tableData"
                ref="collectionTaskTable"
                style="width: 100%"
                border
                v-loading="loading"
                :header-cell-style="{ background: '#F5F7FA', color: '#606266' }"
              >
                <el-table-column prop="checkRuleFatherType" label="规则大类">
                  <template slot-scope="scope">
                    <span
                      v-for="item in checkRuleFatherTypeData"
                      :key="item.contentKey"
                    >
                      <span
                        v-if="scope.row.checkRuleFatherType === item.contentKey"
                      >
                        {{ item.contentValue }}
                      </span>
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="checkRuleType" label="规则小类">
                  <template slot-scope="scope">
                    <span
                      v-for="item in checkRuleTypeData"
                      :key="item.contentKey"
                    >
                      <span v-if="scope.row.checkRuleType == item.contentKey">
                        {{ item.contentValue }}
                      </span>
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="checkRuleId"
                  label="规则编号"
                ></el-table-column>
                <el-table-column
                  prop="checkRuleName"
                  label="规则名称"
                ></el-table-column>
                <el-table-column
                  prop="checkRuleDesc"
                  label="规则说明"
                ></el-table-column>
                <el-table-column
                  prop="qstType"
                  label="问题分类"
                ></el-table-column>
                <el-table-column
                  prop="jobStartDate"
                  label="任务开始时间"
                ></el-table-column>
                <el-table-column fixed="right" label="操作" width="240">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      @click="separateConfig(scope.$index, scope.row)"
                      type="primary"
                      >执行设置</el-button
                    >
                    <el-popconfirm
                      @onConfirm="exeOneRule(scope.$index, scope.row)"
                      title="确定执行当前规则吗？"
                      style="margin-left: 10px"
                    >
                      <el-button slot="reference">立即执行</el-button>
                    </el-popconfirm>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="rule-execution-pag">
              <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="queryData.pageNum"
                :page-sizes="[5, 10, 15, 20]"
                :page-size="queryData.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalNum"
              >
              </el-pagination>
            </div>
          </template>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
import tableViewAndField from "@/mixins/tableViewAndField"
import getCodeValueContent from "@/mixins/getCodeValueContent"
import {
  getAllSystem,
  getRuleRunInfoList,
  getRuleExecutionTable,
  getRuleExecutionTableField,
  exeAllRuleBySys,
  exeOneRule,
} from "@/api/qualityRuleMgt/ruleExecution"
import OverallExeConfig from "./components/OverallExeConfig.vue"
import { queryCodeValueContent } from "@/api/codeValueMgt/codeValueContent"
export default {
  components: {
    OverallExeConfig,
  },
  mixins: [tableViewAndField, getCodeValueContent],
  data() {
    return {
      queryData: {
        // 查询数据
        checkRuleColumn: "", // 检核字段
        checkRuleFatherType: "", // 	检核规则父类型，完整性，一致性，准确性，有效性，规范性，及时性
        checkRuleId: 0,
        checkRuleName: "", // 	检核规则名称
        checkRuleTableOrView: "", // 检查表或视图
        checkRuleType: "", // 检核规则类型，整体完整性，条件完整性，一致性，唯一性，计算正确性，代码有效性，范围有效性，长度规范性，及时性，自定义（每个大类一个
        dbNm: "", // 	数据库名称
        pageNum: 1,
        pageSize: 10,
        qstType: "", //问题分类编码：提示，预警，严重
        sysCode: "", // 	检核系统代码
        sysName: "", // 检核系统名称
      },
      commomData: {
        sysName: "",
        dbNm: "",
        sysCode: "",
        isWholeConfig: true, // 判断是单个执行时间设置还是整体的
        isCover: true, // 是否覆盖
        checkRuleId: 0, // id
      },
      queryCheckRuleFatherTypeData: {
        // 查询规则大类列表数据
        contentKey: "",
        typeCode: "checkRuleFatherType",
      },
      queryCheckRuleTypeData: {
        // 查询规则小类列表数据
        contentKey: "",
        typeCode: "checkRuleType",
        pageNum: 1,
        pageSize: 100,
      },
      checkRuleFatherTypeData: [], // 规则大类列表数据
      checkRuleTypeData: [], // 规则小类列表数据
      problemTypeData: [], // 问题分类数据
      tableData: [], // 表格数据
      totalNum: 1,
      loading: false,
      isTreeLoading: false,
      isSelected: false,
      queryRuleTableOrViewData: {
        // 查询表跟视图
        dbNm: "",
        fieldName: "",
        pageNum: 1,
        pageSize: 9999,
        sysCode: "",
        tableName: "",
      },
      queryCheckRuleColumnData: {
        // 查询表对应的字段
        dbNm: "",
        fieldName: "",
        pageNum: 1,
        pageSize: 9999,
        sysCode: "",
        tableName: "",
      },
      systemData: [
        {
          id: 0,
          sysAndDb: "全部系统",
          children: [],
        },
      ],
      defaultProps: {
        children: "children",
        label: "sysAndDb",
      },
      row: {}, // 表格整行的数据
    }
  },
  created() {
    // 初始化查询所有规则的系统
    this.getAllSystemList()
    // 获取问题分类字典数据
    this.getCodeValueContent("problemTypeData", "problemType")
  },
  watch: {
    queryData: {
      handler(newVal) {
        if (!newVal.checkRuleTableOrView) {
          this.isDisabled = true
          this.queryData.checkRuleColumn = ""
        }
      },
      deep: true,
    },
  },
  methods: {
    handleSelect(data) {
      if (data.sysAndDb !== "全部系统") {
        this.isSelected = true
        this.commomData.sysName = data.sysName
        this.commomData.dbNm = data.dbNm
        this.commomData.sysCode = data.sysCode
        this.queryData.sysCode = data.sysCode
        this.queryData.sysName = data.sysName
        this.queryData.dbNm = data.dbNm
        this.queryRuleTableOrViewData.dbNm = data.dbNm
        this.queryRuleTableOrViewData.sysCode = data.sysCode
        this.queryCheckRuleColumnData.dbNm = data.dbNm
        this.queryCheckRuleColumnData.sysCode = data.sysCode
        this.getCheckRuleFatherType()
        this.getCheckRuleType()
        this.getRuleRunInfoList()
        return
      }
      this.isSelected = false
    },
    // 整体执行设置
    exeAllRuleConfig(val) {
      if (val) {
        // 覆盖
        this.commomData.isWholeConfig = true
        this.commomData.isCover = true
        this.$refs.overallExeConfig.dialogFormVisible = true
      } else {
        // 不覆盖
        this.commomData.isWholeConfig = true
        this.commomData.isCover = false
        this.$refs.overallExeConfig.dialogFormVisible = true
      }
    },
    // 单独执行设置
    separateConfig(index, row) {
      this.commomData.isWholeConfig = false
      this.commomData.checkRuleId = row.checkRuleId
      this.row = JSON.parse(JSON.stringify(row))
      this.$refs.overallExeConfig.dialogFormVisible = true
    },
    // 整体执行
    exeAllRuleBySys() {
      exeAllRuleBySys({
        dbNm: this.commomData.dbNm,
        sysCode: this.commomData.sysCode,
      }).then((res) => {
        if (res.status === 0) {
          this.$message({
            type: "success",
            message: "执行成功",
          })
        } else {
          this.$message({
            type: "error",
            message: res.msg,
          })
        }
      })
    },
    // 执行一条规则
    exeOneRule(index, row) {
      exeOneRule({
        checkRuleId: row.checkRuleId,
        dbNm: this.commomData.dbNm,
        sysCode: this.commomData.sysCode,
      }).then((res) => {
        if (res.status === 0) {
          this.$message({
            type: "success",
            message: "执行成功",
          })
          return
        }
        this.$message({
          type: "error",
          message: res.msg,
        })
      })
    },
    // 获取所有规则的系统
    getAllSystemList() {
      this.isTreeLoading = true
      getAllSystem().then((res) => {
        if (res.status === 0) {
          res.data.forEach((item, index) => {
            if (!item.sysName) {
              item.sysAndDb = item.dbNm
              return
            }
            item.sysAndDb = `${item.sysName} (${item.dbNm})`
            item.id = ++index
          })
          this.systemData[0].children = res.data
          this.systemData[0].children.length > 0 &&
            this.handleSelect(this.systemData[0].children[0]) // 默认选中第一个系统,并获取右侧数据
        }
        this.isTreeLoading = false
      })
    },
    // 异步搜索 当前系统和数据库的表
    querySearchAsync(queryString, cb) {
      this.checkRuleTableOrViewData = []
      this.queryRuleTableOrViewData.tableName = queryString
      getRuleExecutionTable(this.queryRuleTableOrViewData).then((res) => {
        if (res.status === 0) {
          res.data.forEach((element, index) => {
            this.checkRuleTableOrViewData.push({
              id: index,
              tableOrViewName: element,
            })
          })
          cb(this.checkRuleTableOrViewData)
        }
      })
    },
    // 获取表对应的字段
    getRuleExecutionTableField() {
      getRuleExecutionTableField(this.queryCheckRuleColumnData).then((res) => {
        if (res.status === 0) {
          let checkRuleColumnDataArr = []
          res.data.forEach((el, index) => {
            checkRuleColumnDataArr.push({ id: index, fieldName: el })
          })
          this.checkRuleColumnData = checkRuleColumnDataArr
        }
      })
    },
    // 获取规则大类
    getCheckRuleFatherType() {
      queryCodeValueContent(this.queryCheckRuleFatherTypeData).then((res) => {
        if (res.status === 0) {
          this.checkRuleFatherTypeData = res.data.list
        }
      })
    },
    // 获取规则小类
    getCheckRuleType() {
      queryCodeValueContent(this.queryCheckRuleTypeData).then((res) => {
        if (res.status === 0) {
          this.checkRuleTypeData = res.data.list
        }
      })
    },
    // 当规则大类进行选择时
    handlerChange(val) {
      if (val == "") {
        this.queryCheckRuleTypeData.typeCode = "checkRuleType"
        this.getCheckRuleType()
      } else {
        this.queryCheckRuleTypeData.typeCode = val
        this.getCheckRuleType()
      }
      this.queryData.checkRuleType = ""
    },
    // 先选择表或视图
    handlerclick() {
      if (this.isDisabled) {
        this.$message({
          type: "warning",
          message: "请先选择检测表或视图内容",
        })
        this.$refs["abc"].focus()
      }
    },
    // 当当前系统和数据库的表选择时
    handlerTableOrViewSelect(val) {
      this.isDisabled = false
      this.queryCheckRuleColumnData.tableName = val.tableOrViewName
      this.getRuleExecutionTableField()
    },
    // 根据系统，以及其他条件查询对应的规则
    getRuleRunInfoList() {
      this.loading = true
      getRuleRunInfoList(this.queryData).then((res) => {
        if (res.status === 0) {
          this.tableData = res.data.list
          this.totalNum = res.data.total
        } else {
          this.$message({
            message: res.msg,
            type: "error",
          })
        }
        this.loading = false
      })
    },
    // 重置
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.getRuleRunInfoList()
    },
    // 改变页面显示条数
    handleSizeChange(val) {
      this.queryData.pageSize = val
      this.getRuleRunInfoList()
    },
    // 改变页数
    handleCurrentChange(val) {
      this.queryData.pageNum = val
      this.getRuleRunInfoList()
    },
    searchRuleRunInfoList() {
      this.getRuleRunInfoList()
    },
    searchDataSource() {},
  },
}
</script>

<style lang="scss" scoped>
.rule-execution-container {
  margin-bottom: 40px;
  .rule-execution-main {
    margin-top: 10px;
    display: flex;
    .left {
      ::v-deep.el-card {
        min-width: 300px;
        height: 610px;
        overflow: auto;
      }
    }
    .right {
      flex: 1;
      margin-left: 16px;
      ::v-deep.el-card {
        height: 610px;
        overflow: auto;
        .rule-execution-header {
          display: flex;
          margin: 10px 0;
          min-width: 1200px;
        }
      }
    }
    .rule-execution-pag {
      margin-top: 10px;
    }
  }
}
::v-deep.el-tree--highlight-current
  .el-tree-node.is-current
  > .el-tree-node__content {
  background-color: #1b88c0;
  color: white;
  border-radius: 3px;
  padding-right: 40px;
}
</style>
