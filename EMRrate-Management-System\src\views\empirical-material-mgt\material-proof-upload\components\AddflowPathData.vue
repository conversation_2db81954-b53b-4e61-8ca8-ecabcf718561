<template>
  <el-drawer
    :title="ruleForm.id ? '修改实证材料证明' : '新增实证材料证明'"
    :visible.sync="drawer"
    direction="rtl"
    :before-close="handleClose"
    @closed="handlerClose"
  >
    <div class="el-drawercontent">
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="流程资料" prop="flowPath">
          <el-input
            type="textarea"
            v-model="ruleForm.flowPath"
            :http-request="httpRequest"
          ></el-input>
        </el-form-item>
        <el-form-item label="已上传图片" v-if="imagesBase64List.length">
          <span
            v-for="(itemimg, indeximg) in imagesBase64List"
            :key="indeximg"
            class="uploadedpictures"
          >
            <el-image
              style="height: 148px; width: 148px"
              :src="'data:image/png;base64,' + itemimg.base64Str"
              @click="viewimage(itemimg)"
              :preview-src-list="srcList"
            >
            </el-image>
            <span
              class="el-upload-list__item-preview"
              @click.stop="handleRemoveupload(itemimg)"
              >删除
              <i class="el-icon-delete"></i>
            </span>
          </span>
        </el-form-item>
        <el-form-item
          label="图片上传"
          v-if="this.ruleForm.flowPathType === '2'"
        >
          <el-upload
            action="#"
            list-type="picture-card"
            ref="upload"
            :auto-upload="false"
            :file-list="fileList"
            :on-preview="handlePictureCardPreview"
            :on-remove="handleRemove"
          >
            <i class="el-icon-plus"></i>
            <div slot="file" slot-scope="{ file }" class="uploadedpictures">
              <span
                class="el-upload-list__item-preview"
                @click.stop="handleRemove(file)"
                >删除
                <i class="el-icon-delete"></i>
              </span>
              <el-image
                class="el-upload-list__item-thumbnail ffffffff"
                :src="file.url"
                @click="handlePictureCardPreview(file)"
                :preview-src-list="srcList"
              >
              </el-image>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm('ruleForm')">{{
            ruleForm.id ? "立即修改" : "立即创建"
          }}</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-drawer>
</template>

<script>
import {
  addflowpath as _addflowpath,
  getImages,
  updateflowpath,
} from "@/api/empirical-material-mgt";
export default {
  data() {
    return {
      drawer: false,
      ruleForm: {
        directoryCode: "",
        directoryName: "",
        evaluationContentId: "",
        flowPathType: "",
        parentId: "",
        flowPath: "",
        flowPathData: "",
        id: "",
        files: "",
        deleteImageNames: [],
      },
      rules: {
        flowPath: [
          { required: true, message: "请填写实现方式", trigger: "blur" },
        ],
      },
      fileList: [],
      srcList: [],
      imagesBase64List: [],
    };
  },
  methods: {
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.drawer = false;

          done();
        })
        .catch((_) => {});
    },
    // 新增打开
    opendrawer(data, flowPathType, parentId) {
      this.drawer = true;
      this.ruleForm = {
        directoryCode: data.directoryCode,
        directoryName: data.directoryName,
        evaluationContentId: data.evaluationContentId,
        flowPathType: flowPathType,
        parentId: parentId,
      };
    },
    // 编辑打开
    opendraweredit(data) {
      this.drawer = true;
      this.ruleForm = {
        directoryCode: data.directoryCode,
        directoryName: data.directoryName,
        evaluationContentId: data.evaluationContentId,
        flowPathType: data.flowPathType,
        flowPathData: data.flowPathData,
        parentId: data.parentId,
        flowPath: data.flowPath,
        id: data.id,
        deleteImageNames: [],
      };
      this.imagesBase64List = JSON.parse(JSON.stringify(data.imagesBase64List));
    },
    // 提交表单
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          // 新建form表单
          this.fileData = new FormData();
          const data = {
            ...this.ruleForm,
            projectId: this.$store.state.user.projectMsg.id,
          };
          Object.keys(data).forEach((key) => {
            const value = data[key];
            if (Array.isArray(value)) {
              value.forEach((subValue, i) =>
                this.fileData.append(key + `[${i}]`, subValue)
              );
            } else {
              this.fileData.append(key, data[key]);
            }
          });
          if (this.$refs.upload !== undefined) {
            let { uploadFiles } = this.$refs.upload;
            uploadFiles.forEach((item) => {
              this.fileData.append("files", item.raw);
            });
          }

          // 更新
          if (Boolean(this.ruleForm.id)) {
            await updateflowpath(this.fileData)
              .then((res) => {
                if (res.status === 0) {
                  this.$message({
                    message: "修改成功!",
                    type: "success",
                  });
                  this.drawer = false;
                } else {
                  this.$message({
                    message: res.msg,
                  });
                }
              })
              .catch((err) => {
                this.$message({
                  message: err.msg,
                });
              });
          } else {
            // 新增
            await _addflowpath(this.fileData)
              .then((res) => {
                if (res.status === 0) {
                  this.$message({
                    message: "新增成功!",
                    type: "success",
                  });
                  this.drawer = false;
                } else {
                  this.$message({
                    message: res.msg,
                  });
                }
              })
              .catch((err) => {
                this.$message({
                  message: err.msg,
                });
              });
          }
        } else {
          return false;
        }
      });
    },
    // 关闭抽屉函数
    handlerClose() {
      this.$parent.queryflowpath();
      this.drawer = false;
      this.ruleForm = {
        directoryCode: "",
        directoryName: "",
        evaluationContentId: "",
        flowPathType: "",
        parentId: "",
        flowPath: "",
        flowPathData: "",
        id: "",
        files: "",
        deleteImageNames: [],
      };
      this.fileList = [];
      this.srcList = [];
      this.imagesBase64List = [];
    },

    // 删除已上传到服务器的图片
    handleRemoveupload(item) {
      this.imagesBase64List.splice(this.imagesBase64List.indexOf(item), 1);
      this.ruleForm.deleteImageNames.push(item.fileName);
    },
    // 删除未上传到服务器的图片
    handleRemove(file, fileList) {
      // 1.获取将要删除图片的临时路径
      const index = this.fileList.findIndex((item) => {
        return item.uid === file.uid;
      });
      this.fileList.splice(index, 1);
    },
    // 查看未上传到服务器的图片图片
    handlePictureCardPreview(file) {
      this.srcList = [file.url];
    },
    // 查看已上传文件详情图片
    viewimage(data) {
      getImages({
        directoryCode: this.ruleForm.directoryCode,
        directoryName: this.ruleForm.directoryName,
        evaluationContentId: this.ruleForm.evaluationContentId,
        materialFlowPathId: this.ruleForm.id,
        fileName: data.fileName,
        projectId: this.$store.state.user.projectMsg.id,
      }).then((res) => {
        let blob = new Blob([res], { type: "image/jpeg/png" });
        this.currentPicture = window.URL.createObjectURL(blob);
        this.srcList = [this.currentPicture];
      });
    },
    // 自定义的提交函数，取出文件设置进请求参数
    httpRequest(param) {
      this.ruleForm.files = param.file;
    },
  },
};
</script>

<style  scoped lang="scss">
.el-drawercontent {
  padding: 20px;
}
.uploadedpictures {
  position: relative;
  z-index: 99;
  .el-upload-list__item-preview {
    color: red;
    cursor: pointer;
    position: absolute;
    left: 50px;
    z-index: 999;
  }
}
.uploadedpictures {
  margin-right: 10px;
  position: relative;
  z-index: 99;
  .el-upload-list__item-preview {
    // background: #000;
    color: red;
    cursor: pointer;
    position: absolute;
    left: 50px;
    z-index: 999;
  }
}
</style>