import router from "./router"
import store from "./store"
import { Message } from "element-ui"
import NProgress from "nprogress" // progress bar
import "nprogress/nprogress.css" // progress bar style
import { getToken, removeToken } from "@/utils/auth" // get token from cookie
import getPageTitle from "@/utils/get-page-title"
import { checkUserInfoStatus } from "@/api/sys-config"
import UpdatePassword from "./components/UpdatePassword"
import { constant } from "lodash"
NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ["/login", "/singleLogin"] // no redirect whitelist
const notAllowedList = [
  "/document-management/rule-permission-configuration",
  "/document-management/rule-configuration",
  "/document-management/document-review",
  "/empirical-material-mgt/task-assignment-schedule",
  "/empirical-material-mgt/material-proof-upload",
  "/empirical-material-mgt/proof-preview-export",
]
const refreshList = [
  "/document-management/project-management",
  "/empirical-material-mgt/project-management",
]
router.beforeEach(async (to, from, next) => {
  if (!store.state.user.projectMsg?.id && sessionStorage.getItem("projectMsg")) {
    const projectMsg = JSON.parse(sessionStorage.getItem("projectMsg"));
    store.commit('user/SET_PROJECTMSG', projectMsg);
  }
  if(refreshList.includes(to.path)){
    store.commit('user/SET_PROJECTMSG', {})
    sessionStorage.setItem("projectactive", JSON.stringify({}));
  }
  // if(notAllowedList.includes(to.path) && !store.state.user.projectMsg?.id){
  //     Message.warning({
  //       message: "请先选择对应项目",
  //       duration: 3000
  //     })
  //     return
  // }
  // start progress bar
  NProgress.start()
  // set page title
  document.title = getPageTitle(to.meta.title)
  if (to.path === "/singleLogin") {
    removeToken()
  }
  // determine whether the user has logged in
  const hasToken = getToken()
  if (hasToken) {
    if (to.path === "/login") {
      // if is logged in, redirect to the home page
      next({ path: "/" })
      NProgress.done()
    } else {
      const hasGetUserInfo = !!store.getters.name
      if (hasGetUserInfo) {
        next()
      } else {
        try {
          // get user info
          const { pgPermissionKeys } = await store.dispatch("user/getInfo")
          // generate accessible routes map based on roles
          const accessRoutes = await store.dispatch(
            "permission/generateRoutes",
            pgPermissionKeys
          )
          // console.log(accessRoutes);
          if (accessRoutes.lenght != 0) {
            localStorage.setItem("activePath", accessRoutes[0].path)
          }
          // dynamically add accessible routes
          router.addRoutes(accessRoutes)
          // hack method to ensure that addRoutes is complete
          // set the replace: true, so the navigation will not leave a history record
          next({ ...to, replace: true })
          next()
        } catch (error) {
          // remove token and go to login page to re-login
          await store.dispatch("user/resetToken")
          Message.error(error || "Has Error")
          next(`/login`)
          NProgress.done()
        }
      }
    }
  } else {
    /* has no token*/

    if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      next()
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      next(`/login`)
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})
