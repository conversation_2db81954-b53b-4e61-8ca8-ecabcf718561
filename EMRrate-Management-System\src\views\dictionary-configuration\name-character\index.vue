<template>
  <div class="emr-container">
    <div class="emr-container-main">
      <div class="emr-container-button">
        <el-form :model="queryData" ref="ruleForm" inline>
          <el-form-item label="目录名称" prop="directoryName">
            <el-input
              v-model="queryData.directoryName"
              size="mini"
              placeholder="请输入目录名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="规则类型" prop="emrRuleType">
            <el-select
              v-model="queryData.emrRuleType"
              clearable
              size="mini"
              placeholder="请选择"
              @change="queryrequireProjectDictionary"
            >
              <el-option label="一致性" value="一致性"> </el-option>
              <el-option label="完整性" value="完整性"> </el-option>
              <el-option label="整合性" value="整合性"> </el-option>
              <el-option label="及时性" value="及时性"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间" prop="databaseType">
            <el-date-picker
              v-model="dataval"
              size="mini"
              type="daterange"
              align="center"
              unlink-panels
              range-separator="至"
              start-placeholder="创建时间"
              end-placeholder="结束日期"
              :picker-options="pickerOptions"
              value-format="yyyy-MM-dd"
              :clearable="false"
              @change="queryrequireProjectDictionary"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="queryrequireProjectDictionary"
              icon="el-icon-search"
              >搜索</el-button
            >
          </el-form-item>
          <el-form-item>
            <el-button @click="resetForm('ruleForm')">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <el-table
        class="emr-container-table"
        :data="tableData"
        ref="sourceMgtTable"
        v-loading="loading"
        :header-cell-style="{ background: '#fff', color: '#606266' }"
      >
        <el-table-column prop="directoryCode" label="目录编码" min-width="110">
        </el-table-column>
        <el-table-column prop="directoryName" label="目录名称" min-width="120">
        </el-table-column>
        <el-table-column prop="emrRuleType" label="规则类型" min-width="90">
        </el-table-column>
        <el-table-column
          prop="requireProjectName"
          label="要求项目名称"
          min-width="300"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.editstate">
              <el-input
                size="mini"
                type="textarea"
                v-model="scope.row.requireProjectName"
              ></el-input>
            </div>
            <div class="showstate" v-else>
              {{ scope.row.requireProjectName }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column
          prop="hospitalProjectName"
          label="医院项目"
          min-width="300"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.editstate">
              <el-input
                size="mini"
                type="textarea"
                v-model="scope.row.hospitalProjectName"
              ></el-input>
            </div>
            <div class="showstate" v-else>
              {{ scope.row.hospitalProjectName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="headerName1" label=" 表头名称1" min-width="120">
          <template slot-scope="scope">
            <div
              v-if="scope.row.editstate & (scope.row.emrRuleType === '整合性')"
            >
              <el-input
                type="textarea"
                size="mini"
                v-model="scope.row.headerName1"
              ></el-input>
            </div>
            <div class="showstate" v-else>
              {{ scope.row.headerName1 }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="headerName2" label=" 表头名称2" min-width="120">
          <template slot-scope="scope">
            <div
              v-if="scope.row.editstate & (scope.row.emrRuleType === '整合性')"
            >
              <el-input
                type="textarea"
                size="mini"
                v-model="scope.row.headerName2"
              ></el-input>
            </div>
            <div class="showstate" v-else>
              {{ scope.row.headerName2 }}
            </div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="150">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              v-if="!scope.row.editstate"
              @click="editstatechange(scope.$index, scope.row)"
              >编辑</el-button
            >
            <el-button
              size="mini"
              type="primary"
              v-if="scope.row.editstate"
              @click="updaterequireProjectDictionary(scope.$index, scope.row)"
              >保存</el-button
            ><el-divider
              direction="vertical"
              v-if="scope.row.editstate"
            ></el-divider>
            <el-button
              v-if="scope.row.editstate"
              @click="editstatechange(scope.$index, scope.row)"
              >取消</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="emr-container-pag">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryData.pageNum"
          :page-sizes="[5, 10, 15, 20]"
          :page-size="queryData.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalNum"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import {
  queryrequireProjectDictionary,
  updaterequireProjectDictionary,
} from "@/api/document-management/name-character";
export default {
  data() {
    return {
      // 查询数据
      queryData: {
        directoryName: "",
        emrRuleType: '',
        startTime: "",
        endTime: "",
        pageNum: 1,
        pageSize: 10,
      },
      tableData: [], // 表格数据
      tableDatacopy: [], //请求回来的表格数据，保持不变，方便在取消时还原
      dataval: [], // 查询时间数据
      totalNum: 0, //数据总数
      loading: false, //表格加载状态
      // 时间快捷选择快捷方式
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(new Date(new Date().setHours(0, 0, 0, 0)));
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
    };
  },
  created() {
    // 进入页面初始化查询
    this.queryrequireProjectDictionary();
  },
  methods: {
    // 查询要求项目名称字典列表
    queryrequireProjectDictionary() {
      this.loading = true;
      this.queryData.startTime = this.dataval[0];
      this.queryData.endTime = this.dataval[1];
      this.tableDatacopy = [];
      this.tableData = [];
      queryrequireProjectDictionary(this.queryData).then((res) => {
        if (res.status === 0) {
          this.tableDatacopy = JSON.parse(JSON.stringify(res.data.list));
          res.data.list.map((v) => {
            this.tableData.push(Object.assign(v, { editstate: false }));
          });
          this.totalNum = res.data.total;
          this.loading = false;
        }
      });
    },
    // 改变编辑状态
    editstatechange(index, row) {
      if (row.editstate) {
        // 取消操作
        this.tableData[index].editstate = false;
        this.tableData[index].levelName = this.tableDatacopy[index].levelName;
        this.tableData[index].levelCode = this.tableDatacopy[index].levelCode;
      } else {
        //编辑操作
        this.tableData[index].editstate = true;
      }
    },
    // 更新要求项目名称字典
    updaterequireProjectDictionary(index, row) {
      updaterequireProjectDictionary({
        directoryCode:row.directoryCode,
        directoryName:row.directoryName,
        emrRuleType:row.emrRuleType,
        requireProjectName: row.requireProjectName,
        hospitalProjectName: row.hospitalProjectName,
        headerName1: row.headerName1,
        headerName2: row.headerName2,
        id: row.id,
      }).then((res) => {
        if (res.status !== 0) {
          this.$message({
            message: res.msg,
            type: "error",
          });
          return;
        }
        this.$message({
          message: "更新要求项目字典成功",
          type: "success",
        });
        this.tableData[index].editstate = false;
        this.dialogFormVisible = false;
      });
    },
    // 改变页面显示条数
    handleSizeChange(val) {
      this.queryData.pageSize = val;
      this.queryrequireProjectDictionary();
    },
    // 改变页数
    handleCurrentChange(val) {
      this.queryData.pageNum = val;
      this.queryrequireProjectDictionary();
    },
    // 重置
    resetForm(formName) {
      this.$refs[formName].resetFields();
      // 重置时间
      this.dataval = [];
      // 重新搜索
      this.queryrequireProjectDictionary();
    },
  },
};
</script>

<style scoped lang="scss">
@import "@/styles/emr-styles/emr-main-table.scss";
</style>
