<template>
  <div class="all">
    <MainCard>
      <div class="leveltop">
        <div>
          <svg-icon icon-class="dengji" /> 评价等级：
          <el-select
            @change="getLeftTreeList(queryLeftTreelevelCode)"
            v-model="queryLeftTreelevelCode"
            placeholder="请选择"
            size="mini"
          >
            <el-option
              v-for="item in levelCodeData"
              :key="item.levelName"
              :label="item.levelName"
              :value="item.levelCode"
            >
            </el-option>
          </el-select>
        </div>
        <div>
          <div
            :class="activeIndex === 0 ? 'active' : ''"
            @click="handleSelect(0)"
          >
            文档模式
            <div></div>
          </div>
          <div
            :class="activeIndex === 1 ? 'active' : ''"
            @click="handleSelect(1)"
          >
            图片模式
            <div></div>
          </div>
        </div>
        <div v-show="activemenu.directoryCode">
          <el-button type="primary" @click="saveflowpath()" size="mini">
            保存
          </el-button>
          <el-button @click="complete()" size="small">完成</el-button>
          <el-tooltip
            :disabled="!previewdisabled"
            content="请先标记完成再预览！"
            placement="top"
            effect="light"
          >
            <el-button
              type="success"
              @click="preview()"
              size="small"
              :disabled="previewdisabled"
              >预览</el-button
            >
          </el-tooltip>
        </div>
      </div>
    </MainCard>
    <div class="emr-container-rule">
      <div class="emr-container-left">
        <div>
          <span> 实证材料目录</span>
          <span
            class="aboutme"
            v-if="
              activeporject.personInCharge.includes($store.state.user.loginId)
            "
          >
            是否和我相关
            <el-switch
              @change="getLeftTreeList(queryLeftTreelevelCode)"
              v-model="aboutme"
              active-color="#4969de"
              inactive-color="#aaa"
            >
            </el-switch>
          </span>
        </div>
        <div>
          <i class="el-icon-arrow-down"></i><i class="el-icon-arrow-up"></i>
        </div>
        <div class="treebox">
          <el-tree
            :data="treeData"
            default-expand-all
            node-key="id"
            ref="tree"
            highlight-current
            :props="defaultProps"
            @node-click="handleClick"
          >
            <template #default="props">
              <el-tooltip
                v-if="props.node.label.length > 17"
                class="item"
                effect="dark"
                :content="props.node.label"
                placement="right"
              >
                <span style="font-size: 14px">
                  {{ props.node.label.substring(0, 15) }}...
                  <svg-icon
                    v-show="props.data.taskStatus === '2'"
                    icon-class="icon_success"
                    style="font-size: 24px"
                  ></svg-icon>
                </span>
              </el-tooltip>
              <span v-else style="font-size: 14px">
                {{ props.node.label }}
                <!-- {{props.data.taskStatus }} -->
                <svg-icon
                  v-show="props.data.taskStatus === '2'"
                  icon-class="icon_success"
                  style="font-size: 24px"
                ></svg-icon>
              </span>
            </template>
          </el-tree>
        </div>
      </div>
      <div
        class="emr-container-right"
        v-if="showFlag"
        v-show="activeIndex === 0"
      >
        <MainCard>
          <div class="container-main">
            <div>
              {{ activemenu.directoryCode }} {{ activemenu.directoryName }}
            </div>
            <div>
              <el-table
                :data="[activemenu]"
                border
                :header-cell-style="{
                  color: '#333',
                  background: 'transparent ',
                }"
              >
                <el-table-column
                  prop="serialNum"
                  label="项目序号"
                  min-width="50"
                >
                </el-table-column>
                <el-table-column
                  prop="directoryCode"
                  label="项目代码"
                  min-width="60"
                >
                </el-table-column>
                <el-table-column prop="role" label="工作角色" min-width="60">
                </el-table-column>
                <el-table-column
                  prop="business"
                  label="业务项目"
                  min-width="100"
                >
                </el-table-column>
                <el-table-column
                  prop="evaluationCategory"
                  label="评价类别"
                  min-width="50"
                >
                  <template slot-scope="scope">
                    <span>{{
                      scope.row.evaluationCategory === "0" ? "基本" : "选择"
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="evaluationContents"
                  label="评价主要内容"
                  min-width="100"
                >
                  <template slot-scope="scope">
                    <p
                      v-for="(item, index) in scope.row.evaluationContents"
                      :key="item.id"
                    >
                      ({{ index + 1 }}){{ item.evaluationContent }}
                    </p>
                  </template>
                </el-table-column>
                <el-table-column prop="remarksDesc" label="备注" min-width="40">
                </el-table-column>
              </el-table>
            </div>
            <div>
              <div
                v-for="(item1, index1) in evaluationcontentlist"
                :key="item1.id"
                class="evaluationcontentitem"
              >
                <div>
                  <b>({{ index1 + 1 }}){{ item1.evaluationContent }}</b>
                </div>
                <div><b>具体实现方式:</b></div>
                <div class="implementationmodel">
                  <el-input
                    :placeholder="
                      item1.children.allFlowPath.id
                        ? '请输入具体实现方式……'
                        : '请先增加具体实现方式，再增加流程'
                    "
                    v-model="item1.children.allFlowPath.flowPath"
                    @change="changevalue1(item1, item1.children.allFlowPath)"
                    type="textarea"
                  ></el-input>
                </div>
                <div
                  v-for="(item2, index2) in item1.children.groupFlowPath"
                  :key="item2.id"
                  v-loading="Boolean(item1.children.allFlowPath.id) === false"
                  element-loading-text="请先增加具体实现方式，再增加流程"
                >
                  <div class="processdes">
                    <el-tooltip placement="bottom">
                      <div slot="content">点击 新增子流程</div>
                      <span
                        class="spansvg"
                        v-if="
                          index2 === item1.children.groupFlowPath.length - 1
                        "
                        @click="addnewline(index1)"
                      >
                        <svg-icon icon-class="icon_add"
                      /></span>
                    </el-tooltip>
                    <el-input
                      placeholder="请输入子流程描述……"
                      v-model="item2.flowPath"
                      @change="changevalue2(item1, item2, index1, index2)"
                      type="textarea"
                    ></el-input>
                    <el-tooltip placement="bottom">
                      <div slot="content">删除 子流程</div>
                      <i
                        class="el-icon-delete"
                        v-if="item2.id"
                        @click="
                          deleteflowpath(
                            {
                              directoryCode: item1.directoryCode,
                              directoryName: item1.directoryName,
                              id: item2.id,
                            },
                            1,
                            index1,
                            item2,
                            index2
                          )
                        "
                      ></i>
                    </el-tooltip>
                  </div>

                  <div
                    v-for="(item3, index3) in item2.children"
                    :key="item3.id"
                    class="sonimplementationmodelbox"
                  >
                    <div class="sonimplementationmodel">
                      ({{ index3 + 1 }})
                      <el-tooltip placement="top">
                        <div slot="content">请上传流程相关图片</div>
                        <svg-icon
                          class="icon"
                          v-if="JSON.stringify(item3.imagesBase64List) === '[]'"
                          icon-class="noimg"
                        />
                      </el-tooltip>

                      <el-input
                        :disabled="Boolean(item2.id) === false"
                        :placeholder="
                          index3 === item2.children.length - 2
                            ? '请输入子流程具体实现方式……'
                            : ''
                        "
                        @change="
                          changevalue3(
                            item1,
                            item2,
                            item3,
                            index1,
                            index2,
                            index3
                          )
                        "
                        v-model="item3.flowPath"
                        type="textarea"
                        autosize
                      ></el-input>
                      <el-tooltip placement="bottom">
                        <div slot="content">删除 子流程具体实现方式</div>
                        <i
                          class="el-icon-delete"
                          v-if="item3.id"
                          @click="
                            deleteflowpath(item3, 2, index1, item2, index2)
                          "
                        ></i>
                      </el-tooltip>
                    </div>
                    <div
                      v-for="(item4, index4) in item3.imagesBase64List"
                      :key="item4.base64Str"
                      class="uploadedimg"
                    >
                      <div>
                        <div>
                          <el-image
                            style="width: 500px"
                            :src="'data:image/png;base64,' + item4.base64Str"
                            :preview-src-list="srcList"
                            lazy
                          >
                          </el-image>
                          <el-tooltip placement="top">
                            <div slot="content">删除 图片</div>
                            <i
                              class="el-icon-delete"
                              v-if="item3.id"
                              @click="
                                deleteimg(
                                  item1,
                                  item3,
                                  index1,
                                  index2,
                                  index3,
                                  index4
                                )
                              "
                            ></i>
                          </el-tooltip>
                        </div>
                      </div>
                      <div>
                        {{ item3.serialNum
                        }}<span v-show="item3.imagesBase64List.length > 1"
                          >({{ index4 + 1 }})</span
                        >
                      </div>
                    </div>
                    <div class="divupload" v-if="item3.id">
                      <el-upload
                        v-show="item3.id"
                        class="upload-demo"
                        ref="upload"
                        :action="apiUrl"
                        :data="{
                          directoryCode: item3.directoryCode,
                          directoryName: item3.directoryName,
                          evaluationContentId: item3.evaluationContentId,
                          flowPathType: item3.flowPathType,
                          flowPathData: item3.flowPathData,
                          parentId: item3.parentId,
                          flowPath: item3.flowPath,
                          id: item3.id,
                          projectId: activeporject.id,
                        }"
                        list-type="picture-card"
                        :show-file-list="false"
                        :on-success="
                          (response, file, fileList) => {
                            uploadSuccess(
                              fileList,
                              item1,
                              item2,
                              item3,
                              index1,
                              index2,
                              index3
                            );
                          }
                        "
                      >
                        <i class="el-icon-upload"></i>
                        <div>上传截图</div>
                      </el-upload>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </MainCard>
      </div>

      <div
        class="emr-container-right"
        v-if="showFlag"
        v-show="activeIndex === 1"
      >
        <MainCard>
          <div class="container-main">
            <div>
              {{ activemenu.directoryCode }} {{ activemenu.directoryName }}
            </div>
            <PictureEditor
              class="table-right"
              ref="PictureEditor"
              :fatherMethod="clearwanchen"
              :selectedProject="activeporject"
            ></PictureEditor>
          </div>
        </MainCard>
      </div>
      <div></div>
    </div>
    <el-dialog
      title="预览"
      :fullscreen="true"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
    >
      <div
        id="previewcontainer"
        v-loading="loading1"
        element-loading-text="加载中,请稍后"
        :style="
          'width:' +
          previewcontainerwidth +
          'px;' +
          'height:' +
          previewcontainerheight +
          'px'
        "
      ></div>
    </el-dialog>
  </div>
</template>

<script>
import PictureEditor from "./components/PictureEditor.vue";
import { queryAllow } from "@/api/document-management/dictionary-configuration";
import { queryAllSysConfig } from "@/api/sys-config";
import {
  queryDirectoryTree,
  queryevaluationcontent,
  queryflowpath,
  deleteflowpath,
  saveflowpath,
  markComplete,
  addflowpath,
  updateflowpath,
} from "@/api/empirical-material-mgt";
import { previewWord } from "@/api/empirical-material-mgt";
export default {
  components: {
    PictureEditor,
  },
  data() {
    return {
      treeData: [],
      defaultProps: {
        children: "children",
        label: "label",
        disabled: function (data) {
          if (data.children) {
            return true;
          } else {
            return false;
          }
        },
      },
      // 已激活的菜单树
      activemenu: {},
      activemenutableData: [],
      evaluationcontentlist: [
        {
          children: { allFlowPath: { flowPath: "" }, groupFlowPath: [] },
        },
      ],
      newevaluationcontentlist: { allFlowPath: { flowPath: "" } },
      queryLeftTreelevelCode: "",
      queryRightTable: {
        // 查询右侧表数据
        directoryCode: "",
        directoryName: "",
        emrRuleType: "",
      },
      tableData: { documentRuleConfigurationList: [] },
      tableDataalternative: {}, //
      loading: false,
      isLoading: false,
      levelCodeData: [], //等级
      row: {}, // 点击编辑或结构设置时整行的数据
      operationindex: 0, //操作的第几行数据
      operationfield: "", //操作的字段名
      activeIndex: 0, //预览时激活菜单
      datasourcelist: [], //数据源列表
      tableFieldNamelist: [], //动态字段名
      fileList: [],
      srcList: [],
      saveflowpathData: [],
      deleteImageNames: [],
      // projectList: [],
      // selectedProject: "",
      apiUrl: "",
      showFlag: false,
      dialogVisible: false,
      loading1: false,
      activeporject: {},
      aboutme: true,
      previewdisabled: false,
      previewcontainerwidth: 0,
      previewcontainerheight: 0,
    };
  },
  async created() {
    if (this.$route.params.id === undefined) {
      this.activeporject = JSON.parse(sessionStorage.getItem("projectactive"));
    } else {
      this.activeporject = this.$route.params;
    }
    this.queryLeftTreelevelCode = this.activeporject.levelCode;
    // 初始化等级下拉框
    await queryAllow({}).then((res) => {
      const filteredData = res.data.list.filter(
        (item) => parseInt(item.levelCode) <= this.activeporject.levelCode
      );
      this.levelCodeData = filteredData;
    });
    this.getLeftTreeList(this.queryLeftTreelevelCode);
    this.apiUrl =
      process.env.NODE_ENV === "development"
        ? "/api/emm/empiricalmaterial/flowpath/update"
        : "/emm/empiricalmaterial/flowpath/update";
  },
  methods: {
    preview() {
      this.dialogVisible = true;
      this.loading1 = true;
      const data = {
        directoryCode: this.activemenu.directoryCode,
        directoryName: this.activemenu.directoryName,
        projectId: this.activeporject.id,
      };
      queryAllSysConfig().then((res) => {
        for (let key in res.data) {
          if (key === "empiricalmaterial.word.horizontal") {
            this.previewcontainerwidth = res.data[key] === "Y" ? 1130 : 800;
            this.previewcontainerheight = res.data[key] === "Y" ? 800 : 1130;
          }
        }
      });
      previewWord(data).then((res) => {
        this.loading1 = false;
        document.getElementById("previewcontainer").innerHTML = res;
      });
    },
    // 获取左侧树列表数据
    getLeftTreeList(val) {
      this.isLoading = true;
      this.treeData = []; // 清空左侧树数据
      queryDirectoryTree({
        levelCode: val,
        needNotAllocationTask: false,
        userAccount: this.aboutme ? this.$store.state.user.loginId : "",
        projectId: this.activeporject.id,
      }).then((res) => {
        // const newData = res.data.map((item) => {
        //   return {
        //     label: item["serialNum"] + "、   " + item["directoryName"],
        //     directoryCode: item["directoryCode"],
        //     directoryName: item["directoryName"],
        //     emrRuleType: item["emrRuleType"],
        //     issubmit: false,
        //     children: item.secondLevels.map((item2) => {
        //       return {
        //         label: item2["serialNum"] + "、   " + item2["directoryName"],
        //         directoryCode: item2["directoryCode"],
        //         directoryName: item2["directoryName"],
        //         emrRuleType: item2["emrRuleType"],
        //         issubmit: false,
        //         children: item2.thirdLevels.map((item3) => {
        //           return {
        //             label:
        //               item3["directoryCode"] +
        //               item3["directoryName"] +
        //               (item3["evaluationCategory"] === "0"
        //                 ? "(基本)"
        //                 : "(选择)"),
        //             issubmit: true,
        //             directoryCode: item3["directoryCode"],
        //             directoryName: item3["directoryName"],
        //             role: item["directoryName"],
        //             emrRuleType: item3["emrRuleType"],
        //             serialNum: item2["projectNum"],
        //             business: item2["businessProject"],
        //             evaluationCategory: item3["evaluationCategory"],
        //             evaluationContents: item3["evaluationContents"],
        //             remarksDesc: item3["remarksDesc"],
        //             taskStatus: item3["taskStatus"],
        //           };
        //         }),
        //       };
        //     }),
        //   };
        // });

        const newData = [];
        res.data.forEach((item) => {
          newData.push({
            label: item["directoryName"],
            directoryCode: item["directoryCode"],
            directoryName: item["directoryName"],
            emrRuleType: item["emrRuleType"],
            issubmit: false,
            children: [],
            secondLevels: item.secondLevels,
          });
        });

        newData.forEach((item) => {
          item.secondLevels.forEach((item2) => {
            item2.thirdLevels.forEach((item3) => {
              item.children.push({
                label:
                  item3["directoryCode"] +
                  item3["directoryName"] +
                  (item3["evaluationCategory"] === "0" ? "(基本)" : "(选择)"),
                issubmit: true,
                directoryCode: item3["directoryCode"],
                directoryName: item3["directoryName"],
                role: item["directoryName"],
                emrRuleType: item3["emrRuleType"],
                serialNum: item2["projectNum"],
                business: item2["businessProject"],
                evaluationCategory: item3["evaluationCategory"],
                evaluationContents: item3["evaluationContents"],
                remarksDesc: item3["remarksDesc"],
                taskStatus: item3["taskStatus"],
              });
            });
          });
        });
        this.treeData = newData;
        this.isLoading = false;
      });
    },

    // 点击树形结构 表 或 view
    async handleClick(data1, data2, data3, dataId) {
      this.previewdisabled = data1.taskStatus === "2" ? false : true;
      if (
        data1.issubmit &
        // (this.activemenu != data1) &
        (this.activeIndex === 0)
      ) {
        this.activemenutableData = [{ ...data1 }];
        this.activemenu = data1;
        await queryevaluationcontent({
          directoryCode: data1.directoryCode,
          directoryName: data1.directoryName,
        }).then(async (res) => {
          this.showFlag = true;
          this.activemenu.evaluationContents = res.data;
          const newData = res.data.map((item) => {
            return {
              ...item,
              children: {
                allFlowPath: { flowPath: "" },
                groupFlowPath: [{ children: [{}] }],
              },
            };
          });
          if (!dataId) {
            this.evaluationcontentlist = newData;
          }
          // 获取具体内容
          res.data.forEach(async (item, index) => {
            if (dataId && dataId !== item.id) {
              return;
            }
            await queryflowpath({
              directoryCode: item.directoryCode,
              directoryName: item.directoryName,
              evaluationContentId: item.id,
              projectId: this.activeporject.id,
            }).then((ress) => {
              if (ress.data.allFlowPath === null) {
                this.evaluationcontentlist[index].children = {
                  allFlowPath: {
                    flowPath: "",
                  },
                  groupFlowPath: { children: [{}] },
                };
              } else {
                const newData1 = ress.data.groupFlowPath.map((item) => {
                  return {
                    ...item,
                    children: [...item.children, {}, {}],
                  };
                });
                const newData2 = [{ children: [{}, {}] }];
                this.evaluationcontentlist[index].children = {
                  allFlowPath: {
                    ...ress.data.allFlowPath,
                  },
                  groupFlowPath:
                    ress.data.groupFlowPath.length > 0 ? newData1 : newData2,
                };
              }
              this.$forceUpdate();
            });
          });
        });
      } else if (
        data1.issubmit &
        // (this.activemenu != data1) &
        (this.activeIndex === 1)
      ) {
        this.activemenutableData = [{ ...data1 }];
        this.activemenu = data1;
        this.$refs.PictureEditor.queryAllflowpath(this.activemenu);
      }
    },

    // 页面值改变，添加到提交数组,具体实现方式
    changevalue1(data, data1) {
      if (!Boolean(data1.id)) {
        let fileData = new FormData();
        const datafrom = {
          directoryCode: data.directoryCode,
          directoryName: data.directoryName,
          evaluationContentId: data.id,
          flowPathType: "0",
          flowPath: data1.flowPath,
          projectId: this.activeporject.id,
        };
        Object.keys(datafrom).forEach((key) => {
          const value = datafrom[key];
          if (Array.isArray(value)) {
            value.forEach((subValue, i) =>
              fileData.append(key + `[${i}]`, subValue)
            );
          } else {
            fileData.append(key, datafrom[key]);
          }
        });
        addflowpath(fileData)
          .then((res) => {
            if (res.status === 0) {
              this.clearwanchen();
              this.$message({
                message: "新增具体实现方式成功!",
                type: "success",
              });
              this.handleClick(this.activemenu, 0, 0, data.id);
            } else {
              this.$message({
                message: res.msg,
              });
            }
          })
          .catch((err) => {
            this.$message({
              message: err.msg,
            });
          });
      } else {
        let active = this.saveflowpathData.find((item) => item.id == data1.id);
        if (Boolean(active)) {
          this.saveflowpathData.splice(
            this.saveflowpathData.indexOf(active),
            1
          );
          this.saveflowpathData.push({
            directoryCode: data.directoryCode,
            directoryName: data.directoryName,
            evaluationContentId: data.id,
            flowPath: data1.flowPath,
            flowPathType: "0",
            id: data1.id,
          });
        } else {
          this.saveflowpathData.push({
            directoryCode: data.directoryCode,
            directoryName: data.directoryName,
            evaluationContentId: data.id,
            flowPath: data1.flowPath,
            flowPathType: "0",
            id: data1.id,
          });
        }
      }
    },

    // 页面值改变，添加到提交数组,子流程新增
    changevalue2(item1, item2, index1, index2) {
      if (!Boolean(item2.id)) {
        let fileData = new FormData();
        const datafrom = {
          directoryCode: item1.directoryCode,
          directoryName: item1.directoryName,
          evaluationContentId: item1.id,
          flowPathType: "1",
          flowPath: item2.flowPath,
          projectId: this.activeporject.id,
        };
        Object.keys(datafrom).forEach((key) => {
          const value = datafrom[key];
          if (Array.isArray(value)) {
            value.forEach((subValue, i) =>
              fileData.append(key + `[${i}]`, subValue)
            );
          } else {
            fileData.append(key, datafrom[key]);
          }
        });
        addflowpath(fileData)
          .then((res) => {
            if (res.status === 0) {
              this.clearwanchen();
              this.$message({
                message: "新增子流程成功!",
                type: "success",
              });
              this.evaluationcontentlist[index1].children.groupFlowPath[
                index2
              ].flowPath = res.data.flowPath;
              this.evaluationcontentlist[index1].children.groupFlowPath[
                index2
              ].id = res.data.id;
              this.$forceUpdate();
            } else {
              this.$message({
                message: res.msg,
              });
            }
          })
          .catch((err) => {
            this.$message({
              message: err.msg,
            });
          });
      } else {
        let active = this.saveflowpathData.find((item) => item.id == item2.id);
        if (Boolean(active)) {
          this.saveflowpathData.splice(
            this.saveflowpathData.indexOf(active),
            1
          );
          this.saveflowpathData.push({
            directoryCode: item1.directoryCode,
            directoryName: item1.directoryName,
            evaluationContentId: item1.id,
            flowPath: item2.flowPath,
            flowPathType: "1",
            id: item2.id,
          });
        } else {
          this.saveflowpathData.push({
            directoryCode: item1.directoryCode,
            directoryName: item1.directoryName,
            evaluationContentId: item1.id,
            flowPath: item2.flowPath,
            flowPathType: "1",
            id: item2.id,
          });
        }
      }
    },
    // 页面值改变，添加到提交数组,子流程具体实现方式
    changevalue3(item1, item2, item3, index1, index2, index3) {
      if (item2.children.length - 2 === index3) {
        item2.children.push({});
      }
      if (!Boolean(item3.id)) {
        let fileData = new FormData();
        const datafrom = {
          directoryCode: item1.directoryCode,
          directoryName: item1.directoryName,
          evaluationContentId: item1.id,
          flowPathType: "2",
          parentId: item2.id,
          flowPath: item3.flowPath,
          projectId: this.activeporject.id,
        };
        Object.keys(datafrom).forEach((key) => {
          const value = datafrom[key];
          if (Array.isArray(value)) {
            value.forEach((subValue, i) =>
              fileData.append(key + `[${i}]`, subValue)
            );
          } else {
            fileData.append(key, datafrom[key]);
          }
        });
        addflowpath(fileData)
          .then((res) => {
            if (res.status === 0) {
              this.clearwanchen();
              this.$message({
                message: "新增子流程具体实现方式成功!",
                type: "success",
              });
              this.evaluationcontentlist[index1].children.groupFlowPath[
                index2
              ].children[index3] = { ...res.data };
              this.$forceUpdate();
            } else {
              this.$message({
                message: res.msg,
              });
            }
          })
          .catch((err) => {
            this.$message({
              message: err.msg,
            });
          });
      } else {
        let active = this.saveflowpathData.find((item) => item.id == item3.id);
        if (Boolean(active)) {
          this.saveflowpathData.splice(
            this.saveflowpathData.indexOf(active),
            1
          );
          this.saveflowpathData.push({
            directoryCode: item1.directoryCode,
            directoryName: item1.directoryName,
            evaluationContentId: item1.id,
            parentId: item2.id,
            flowPath: item3.flowPath,
            flowPathType: "2",
            id: item3.id,
          });
        } else {
          this.saveflowpathData.push({
            directoryCode: item1.directoryCode,
            directoryName: item1.directoryName,
            evaluationContentId: item1.id,
            parentId: item2.id,
            flowPath: item3.flowPath,
            flowPathType: "2",
            id: item3.id,
          });
        }
      }
    },
    // 保存页面值
    saveflowpath() {
      saveflowpath({
        empiricalMaterialFlowPaths: this.saveflowpathData,
        projectId: this.activeporject.id,
      }).then((res) => {
        this.saveflowpathData = [];
        this.clearwanchen();
        this.$message({
          message: "保存成功!",
          type: "success",
        });
      });
    },
    // 新增一行
    addnewline(index1) {
      const newData2 = { children: [{}, {}] };
      this.evaluationcontentlist[index1].children.groupFlowPath.push(newData2);
      this.$forceUpdate();
    },
    // 图片上传成功以后回显图片
    uploadSuccess(fileList, item1, item2, item3, index1, index2, index3) {
      queryflowpath({
        directoryCode: item1.directoryCode,
        directoryName: item1.directoryName,
        evaluationContentId: item1.id,
        projectId: this.activeporject.id,
      }).then((ress) => {
        this.clearwanchen();
        if (ress.data.allFlowPath === null) {
          this.evaluationcontentlist[index1].children = {
            allFlowPath: {
              flowPath: "",
            },
            groupFlowPath: [{ children: [{}] }],
          };
        } else {
          const newData1 = ress.data.groupFlowPath.map((item) => {
            return {
              ...item,
              children: [...item.children, [], []],
            };
          });
          const newData2 = [{ children: [{}, {}] }];
          this.evaluationcontentlist[index1].children = {
            allFlowPath: {
              ...ress.data.allFlowPath,
            },
            groupFlowPath:
              ress.data.groupFlowPath.length > 0 ? newData1 : newData2,
          };
        }
        this.$forceUpdate();
      });
    },

    // 删除实证材料证明
    deleteflowpath(data, type, index1, item2, index2) {
      this.$confirm("此操作将实证材料证明, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteflowpath({
            directoryCode: data.directoryCode,
            directoryName: data.directoryName,
            id: data.id,
            projectId: this.activeporject.id,
          }).then((res) => {
            if (res.status === 0) {
              this.clearwanchen();
              this.$message({
                message: "修改成功!",
                type: "success",
              });
              if (type === 1) {
                this.evaluationcontentlist[
                  index1
                ].children.groupFlowPath.splice(
                  this.evaluationcontentlist[
                    index1
                  ].children.groupFlowPath.indexOf(item2),
                  1
                );
                if (
                  this.evaluationcontentlist[index1].children.groupFlowPath
                    .length === 0
                ) {
                  this.evaluationcontentlist[index1].children.groupFlowPath = [
                    { children: [{}, {}] },
                  ];
                }
              } else if (type === 2) {
                this.evaluationcontentlist[index1].children.groupFlowPath[
                  index2
                ].children.splice(
                  this.evaluationcontentlist[index1].children.groupFlowPath[
                    index2
                  ].children.indexOf(data),
                  1
                );
              }
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    //删除图片
    deleteimg(item1, item3, index1, index2, index3, index4) {
      this.deleteImageNames.push(item3.imagesBase64List[index4].fileName);
      this.$confirm("此操作将实证材料证明, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 新建form表单
          this.fileData = new FormData();
          const data = {
            directoryCode: item3.directoryCode,
            directoryName: item3.directoryName,
            evaluationContentId: item3.evaluationContentId,
            flowPathType: item3.flowPathType,
            flowPathData: item3.flowPathData,
            parentId: item3.parentId,
            flowPath: item3.flowPath,
            id: item3.id,
            deleteImageNames: this.deleteImageNames,
            projectId: this.activeporject.id,
          };
          Object.keys(data).forEach((key) => {
            const value = data[key];
            if (Array.isArray(value)) {
              value.forEach((subValue, i) =>
                this.fileData.append(key + `[${i}]`, subValue)
              );
            } else {
              this.fileData.append(key, data[key]);
            }
          });
          updateflowpath(this.fileData).then((res) => {
            if (res.status === 0) {
              this.$message({
                message: "删除图片成功!",
                type: "success",
              });
              this.clearwanchen();
              this.evaluationcontentlist[index1].children.groupFlowPath[
                index2
              ].children[index3].imagesBase64List.splice(index4 - 1, 1);
              this.deleteImageNames = [];
              this.uploadSuccess([], item1, {}, {}, index1);
            } else {
              this.$message({
                message: res.msg,
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 切换菜单
    handleSelect(key, keyPath) {
      // this.activemenu = {};
      this.activeIndex = key;
      this.handleClick(this.activemenu);
    },
    // 标记完成成功
    complete() {
      let activetree = this.$refs.tree.getCurrentNode();
      markComplete({
        directoryCode: this.activemenu.directoryCode,
        directoryName: this.activemenu.directoryName,
        projectId: this.activeporject.id,
      }).then((res) => {
        if (res.status === 0) {
          this.$message({
            message: "标记完成成功!",
            type: "success",
          });
          this.previewdisabled = false;
          this.treeData.forEach((item1, index1) => {
            item1.children.forEach((item2, index2) => {
              if (item2.label === activetree.label) {
                item2.taskStatus = "2";
              }
            });
          });
        } else {
          this.$message({
            message: res.msg,
            type: "error",
          });
        }
      });
    },
    // 清除完成标志
    clearwanchen() {
      let activetree = this.$refs.tree.getCurrentNode();
      this.previewdisabled = true;
      this.treeData.forEach((item1, index1) => {
        item1.children.forEach((item2, index2) => {
          if (item2.label === activetree.label) {
            item2.taskStatus = "1";
          }
        });
      });
    },
  },

  // computed: {
  //   activeproject() {
  //     return this.activeporject;
  //   },
  // },
  // watch: {
  //   activeproject: {
  //     async handler(val) {
  //       if (Boolean(val) === true) {
  //         let that = this;
  //         this.showFlag = false;
  //         this.activemenu = {};
  //         this.queryLeftTreelevelCode = this.activeporject.levelCode;
  //         await queryAllow({}).then((res) => {
  //           const filteredData = res.data.list.filter(
  //             (item) => parseInt(item.levelCode) <= this.activeporject.levelCode
  //           );
  //           this.levelCodeData = filteredData;
  //         });
  //         this.getLeftTreeList(this.queryLeftTreelevelCode);
  //       }
  //     },
  //   },
  // },
};
</script>

<style scoped lang="scss">
@import "@/styles/emr-styles/emr-main-table.scss";

.leveltop {
  display: flex;
  // justify-content: space-between;
  align-items: center;
  > div:nth-child(1) {
    width: 20%;
    min-width: 300px;
    svg {
      color: #5783e6;
    }
    .el-select {
      width: 100px;
    }
  }
  > div:nth-child(2) {
    width: 30%;
    display: flex;
    div {
      margin-right: 20px;
      font-weight: bold;
      font-size: 14px;
      color: #0d0f18;
    }
    .active {
      color: #5270dd;
      position: relative;
      > div {
        border-bottom: 5px solid #5270dd;
        width: 30px;
        position: absolute;
        bottom: -10px;
        left: 16px;
      }
    }
  }
  > div:nth-child(3) {
    width: 50%;
    text-align: right;
    .attachment {
      color: #5270dd;
    }
  }
}
.emr-container-rule {
  width: 100%;
  display: flex;
  padding-top: 20px;
  height: calc(100vh - 230px);
  justify-content: space-between;
  .emr-container-left {
    width: 20%;
    min-width: 300px;
    border-right: 1px solid #dbdde1;
    padding-right: 10px;
    > div:nth-child(1) {
      display: flex;
      justify-content: space-between;
      span:nth-child(1) {
        font-weight: bold;
        color: #4969de;
      }
      .aboutme {
        color: #aaa;
        font-size: 14px;
      }
    }
    > div:nth-child(2) {
      margin: 10px 0px;
      i {
        border: 1px solid #cdcdcd;
        padding: 4px;
        background: #ffffff;
        border-radius: 3px;
        margin-right: 10px;
        cursor: pointer;
      }
    }
    > div:nth-child(3) {
      height: 70vh;
      overflow: scroll;
      background: transparent;
      background-repeat: 10px;
      padding: 10px;
      .el-tree {
        background: transparent;
      }
    }
  }
  .emr-container-right {
    flex: 1;
    max-width: 800px;
    max-height: 800px;
    overflow: scroll;
    margin-left: 20px;
    .container-main {
      padding: 20px 10px;
      > div:nth-child(1) {
        font-weight: bold;
        font-size: 16px;
        color: #000000;
        line-height: 40px;
      }
      > div:nth-child(2) {
        width: 100%;
        p {
          text-align: left;
        }
      }
      > div:nth-child(3) {
        line-height: 50px;
        font-size: 14px;
        color: #000000;
        line-height: 30px;
        .evaluationcontentitem {
          margin-bottom: 50px;
          .implementationmodel {
            margin-left: 30px;
            color: #bec0c2;
            margin-bottom: 20px;
          }
          .processdes {
            display: flex;
            .spansvg {
              border-radius: 6px;
              padding: 0px 8px;
              height: 30px;
              width: 30px;
            }
            .spansvg:hover {
              background: #eeeff0;
            }
            .el-icon-delete {
              display: none;
            }
          }

          .processdes:hover .el-icon-delete {
            display: block;
          }
          .sonimplementationmodel {
            margin-left: 30px;
            color: #bec0c2;
            display: flex;
            align-items: center;
            .el-icon-delete {
              display: none;
            }
          }
          .sonimplementationmodel:hover .el-icon-delete {
            display: block;
          }
          .uploadedimg {
            text-align: center;

            > div:nth-child(1) {
              display: flex;
              justify-content: space-around;
              > div {
                position: relative;
                .el-icon-delete {
                  position: absolute;
                  right: 0px;
                  top: 0px;
                  display: none;
                }
              }
              > div:hover .el-icon-delete {
                display: inline-block;
              }
            }
            > div:nth-child(2) {
              line-height: 30px;
            }
          }

          .divupload {
            margin-left: 30px;
            height: 80px;
            display: flex;
          }
          // .upload-demo {
          //   display: none;
          // }
          // .sonimplementationmodelbox:hover .upload-demo {
          //   display: block;
          // }
        }
      }
      // 修改UI样式
      .has-gutter tr th {
        background: #fff;
      }
    }
  }
}
::v-deep .el-textarea__inner {
  border: 1px solid transparent;
}
::v-deep .el-upload--picture-card {
  width: 72px;
  height: 72px;
  padding-top: 10px;
  line-height: 20px;
}
.header {
  ::v-deep .el-input__inner {
    border: none;
    box-shadow: none;
    font-size: 20px;
    color: red;
    font-weight: bold;
  }
  ::v-deep .el-input {
    margin-bottom: 20px;
  }
}
#previewcontainer {
  background: #eee;
  overflow-y: scroll;
  overflow-x: scroll;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
}
</style>
