import Vue from 'vue'

import 'normalize.css/normalize.css' // A modern alternative to CSS resets

import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import locale from 'element-ui/lib/locale/lang/zh-CN' // lang i18n
import moment from 'moment'
import '@/utils/dialogDrag'
import { download } from '@/utils/request'
//粒子特效
import VueParticles from 'vue-particles'
//权限控制
import { checkPermission } from '@/utils/auth'

import '@/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'
import MainCardComponent from '@/components/MainCard'
import HeaderSearch from '@/components/HeaderSearch'
import '@/icons' // icon
import '@/permission' // permission control

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */
// if (process.env.NODE_ENV === 'production') {
//   const { mockXHR } = require('../mock')
//   mockXHR()
// }

// set ElementUI lang to zh-CN
Vue.use(ElementUI, { locale, size: 'small' })
Vue.use(VueParticles)
Vue.component('MainCard', MainCardComponent) // 注册全局主要卡片组件
Vue.component('HeaderSearch', HeaderSearch) // 注册全局页面头部搜索组件

// window.addEventListener("resize", () => {
//   if(myChart){
//   myChart.resize()
// }
// })

Vue.config.productionTip = false
//每个vue实例里面的工具
Vue.prototype.$moment = moment
//按钮权限 检查函数
Vue.prototype.$checkPermission = checkPermission
Vue.prototype.download = download

new Vue({
  el: '#app',
  router,
  store,
  render: (h) => h(App)
})
