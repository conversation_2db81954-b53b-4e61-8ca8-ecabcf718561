import request from '@/utils/request'
import fileAxios from '@/utils/fileAxios.js'


// 预览文档
export function previewWord(data) {
  return request({
    url: '/emr/documentExportRecord/previewWord',
    method: 'post',
    data
  })
}



// 导出文档
export function exportDocument(params) {
  return request({
    url: '/emr/documentExport/export',
    method: 'get',
    responseType: 'blob',
    params
  })
}

// 导出文档
export function exportDocument2(params) {
  return request({
    url: '/emr/documentExport/export',
    method: 'get',
    params
  })
}

// 据导出ID，获取当前进度,第二次传值的时候带上第一次结果中的allDocxNum，减少数据库查询次数
export function getCurrentProgress(params) {
  return request({
    url: '/emr/documentExport/getCurrentProgress',
    method: 'get',
    params
  })
}


// 问题数据备注
export function problemDataRemarks(data) {
  return request({
    url: '/emr/documentExport/problemDataRemarks',
    method: 'post',
    data
  })
}


// 根据导出ID，获取备注
export function getProblemDataRemarks(params) {
  return request({
    url: '/emr/documentExportRecord/getProblemDataRemarks/' + params,
    method: 'get',
  })
}

// 预览导出文档
export function previewExport(data) {
  return request({
    url: '/emr/documentExport/previewExport',
    method: 'post',
    data
  })
}




// 查询文档导出记录
export function querydocumentExportRecord(data) {
  return request({
    url: '/emr/documentExportRecord/query',
    method: 'post',
    data
  })
}




// 删除文档
export function deletedocumentExportRecord(params) {
  return request({
    url: '/emr/documentExportRecord/delete/' + params,
    method: 'DELETE',
  })
}




// 据导出ID，获取当前进度,第二次传值的时候带上第一次结果中的allDocxNum，减少数据库查询次数
export function getErrorDetail(params) {
  return request({
    url: '/emr/documentExportRecord/getErrorDetail/' + params,
    method: 'get',
  })
}


// 重新生成预览文档
export function regeneratePreviewDocument(data) {
  return request({
    url: '/emr/documentExport/regeneratePreviewDocument',
    method: 'post',
    data
  })
}


// 获取已备注信息
export function getOneProblemDataRemark(data) {
  return request({
    url: '/emr/documentExportRecord/getOneProblemDataRemark',
    method: 'post',
    data
  })
}

// 根据导出ID，获取历史备注

export function getProblemDataHistoryRemarks(data) {
  return request({
    url: '/emr/documentExportRecord/getProblemDataHistoryRemarks',
    method: 'post',
    data
  })
}


// *********************************************************下载文档，并获取进度*******************************************
// 一。导出文档（异步执行）
export function exportAsync(params) {
  return request({
    url: '/emr/documentExport/exportAsync',
    method: 'get',
    params
  })
}

// 二、异步导出文档进度（-1表示失败，1表示完成）,建议5秒调一次
export function exportProcess(params) {
  return request({
    url: '/emr/documentExport/exportProcess',
    method: 'get',
    params
  })
}

// 三、下载异步导出的文档
export function downloadDoc(params) {
  return request({
    url: '/emr/documentExport/downloadDoc',
    method: 'get',
    responseType: 'blob',
    params
  })
}

// 获取基础数据
export function getBasedocumentExport(params) {
  return request({
    url: '/emr/documentExport/getBase',
    method: 'get',
    params
  })
}



// 获取基础数据
export function getMedicaRecordsOrQuality(params) {
  return request({
    url: '/emr/documentExport/getMedicaRecordsOrQuality',
    method: 'get',
    params
  })
}

// 获取病历|质量数据的状态（是否通过）
export function getMedicaRecordsOrQualityStatus(params) {
  return request({
    url: '/emr/documentExport/getMedicaRecordsOrQualityStatus',
    method: 'get',
    params
  })
}
