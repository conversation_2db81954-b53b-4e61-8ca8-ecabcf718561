import request from '@/utils/request'

//查询操作日志接口
export function queryoperlog(data) {
    return request({
        url: '/log/queryoperlog',
        method: 'post',
        data
    })
}

//查询接收消息日志接口
export function queryRcvLog(data) {
    return request({
        url: '/mqmsglog/queryRcvLog',
        method: 'post',
        data
    })
}
//重发消息
export function retrySendMsg(params) {
    return request({
        url: '/mqmsglog/retrySendMsg',
        method: 'put',
        params
    })
}

//查询发送消息日志接口
export function querySendLog(data) {
    return request({
        url: '/mqmsglog/querySendLog',
        method: 'post',
        data
    })
}