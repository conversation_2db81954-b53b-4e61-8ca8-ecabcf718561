<template>
  <MainCard>
    <div class="login-log">
      <div class="log-header">
        <el-form
          :model="queryData"
          ref="ruleForm"
          :inline="true">
          <el-form-item
            label="用户账号"
            prop="loginId">
            <el-input
              v-model="queryData.loginId"
              placeholder="请输入用户账号"></el-input>
          </el-form-item>
          <el-form-item
            label="用户名"
            prop="username">
            <el-input
              v-model="queryData.username"
              placeholder="请输入用户名"></el-input>
          </el-form-item>
          <el-form-item
            label="IP"
            prop="ip">
            <el-input
              v-model="queryData.ip"
              placeholder="请输入IP"></el-input>
          </el-form-item>
          <el-form-item
            label="开始时间"
            prop="startTime">
            <el-date-picker
              v-model="queryData.startTime"
              type="date"
              placeholder="请选择开始时间"
              value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
          <el-form-item
            label="结束时间"
            prop="endTime">
            <el-date-picker
              v-model="queryData.endTime"
              type="date"
              placeholder="请选择结束时间"
              value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <div style="margin-left: 10px">
              <el-button
                type="primary"
                @click="searchTaskStatus"
                icon="el-icon-search"
                >搜索</el-button
              >
            </div>
          </el-form-item>
          <el-form-item>
            <el-button @click="resetForm('ruleForm')">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="task-status-table">
        <el-table
          :data="tableData"
          ref="taskStatusTable"
          style="width: 100%"
          v-loading="loading"
          :header-cell-style="{ background: '#fff', color: '#606266' }">
          <el-table-column
            prop="loginId"
            label="用户账号">
          </el-table-column>
          <el-table-column
            prop="username"
            label="用户名">
          </el-table-column>
          <el-table-column
            prop="ip"
            label="IP">
          </el-table-column>
          <el-table-column
            prop="loginTime"
            label="登录时间">
          </el-table-column>
        </el-table>
      </div>
      <div class="log-pag">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryData.pageNum"
          :page-sizes="[5, 10, 15, 20]"
          :page-size="queryData.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalNum">
        </el-pagination>
      </div>
    </div>
  </MainCard>
</template>

<script>
import { queryLoginLog } from '@/api/logMgt/loginLog'
export default {
  data() {
    return {
      queryData: {
        // 查询数据
        endTime: '',
        ip: '',
        loginId: '',
        pageNum: 1,
        pageSize: 10,
        startTime: '',
        username: ''
      },
      tableData: [],
      totalNum: 1,
      loading: false
    }
  },
  created() {
    // 初始化操作日志列表
    this.queryLoginLogList()
  },
  methods: {
    // 查询操作日志列表
    queryLoginLogList() {
      this.loading = true
      queryLoginLog(this.queryData).then((res) => {
        if (res.status === 0) {
          this.tableData = res.data.list
          this.totalNum = res.data.total
          this.loading = false
        } else {
          this.loading = false
          this.$message({
            message: res.msg,
            type: 'error'
          })
        }
      })
    },
    // 重置
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.queryLoginLogList()
    },
    // 搜索任务状态
    searchTaskStatus() {
      this.queryLoginLogList()
    },
    // 改变页面显示条数
    handleSizeChange(val) {
      this.queryData.pageSize = val
      this.queryLoginLogList()
    },
    // 改变页数
    handleCurrentChange(val) {
      this.queryData.pageNum = val
      this.queryLoginLogList()
    }
  }
}
</script>

<style scoped lang="scss">
.login-log {
  .log-header {
    min-width: 1200px;
    display: flex;
    /* margin: 10px 0; */
  }
  .log-pag {
    margin-top: 10px;
  }
}
</style>
