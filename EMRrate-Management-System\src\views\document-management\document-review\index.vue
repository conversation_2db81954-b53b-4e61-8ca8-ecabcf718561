<template>
  <div class="emr-container">
    <MainCard v-show="!Boolean(this.previewdocumentstate)">
      <div class="emr-container-search">
        <div class="emr-container-search-left">
          <el-form
            :model="createformData"
            ref="ruleForm"
            label-width="0px"
            :rules="rules"
            element-loading-spinner="el-icon-loading"
          >
            <el-form-item prop="exportDocumentLevel">
              <div>评价等级</div>
              <el-select
                v-model="createformData.exportDocumentLevel"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in levelCodeData"
                  :key="item.levelName"
                  :label="item.levelName"
                  :value="item.levelCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="dataStartTime">
              <div>统计时间范围</div>
              <el-date-picker
                v-model="dataval"
                size="mini"
                type="monthrange"
                align="center"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM"
                :clearable="false"
                @change="changeMonth"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item prop="timedExportTime">
              <div>定时导出时间</div>
              <el-date-picker
                v-model="createformData.timedExportTime"
                type="datetime"
                :picker-options="option"
                placeholder="选择日期时间"
                value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <div>预览导出文档类型</div>
              <el-checkbox-group v-model="exportDocumentTypelist">
                <el-checkbox label="0" disabled
                  >数据质量文档 (必选)
                </el-checkbox>
                <el-checkbox label="1">基础数据填报</el-checkbox>
                <el-checkbox label="2">病历数据填报</el-checkbox>
                <el-checkbox label="3">质量数据填报</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                @click="queryrequireProjectDictionary"
                icon="el-icon-printer"
                size="mini"
                :disabled="builddisabled"
                >生成预览页面</el-button
              >
            </el-form-item>
          </el-form>
        </div>
        <div class="emr-container-search-right">
          <div class="emr-container-header">
            <el-form :model="queryData" ref="ruleForm1" inline>
              <el-form-item label="评价等级：">
                <el-select
                  v-model="queryData.exportDocumentLevel"
                  placeholder="请选择"
                  @change="querydocumentExportRecord()"
                >
                  <el-option
                    v-for="item in levelCodeData"
                    :key="item.levelName"
                    :label="item.levelName"
                    :value="item.levelCode"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="操作时间" prop="databaseType">
                <el-date-picker
                  v-model="dataval1"
                  size="mini"
                  type="daterange"
                  align="center"
                  unlink-panels
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束日期"
                  :picker-options="pickerOptions"
                  value-format="yyyy-MM-dd"
                  :clearable="false"
                  @change="changedataval"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item prop="databaseType">
                <el-button
                  type="primary"
                  @click="querydocumentExportRecord()"
                  icon="el-icon-search"
                  size="mini"
                  >查询</el-button
                >
              </el-form-item>
              <el-form-item>
                <el-button @click="resetForm('ruleForm')" size="mini"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>
          </div>
          <div class="emr-container-main">
            <div class="emr-container-table">
              <el-table
                :data="tableData"
                ref="sourceMgtTable"
                style="width: 100%"
                v-loading="loading"
                :header-cell-style="{ background: '#fff', color: '#333' }"
              >
                <el-table-column prop="id" label="文档ID" min-width="150">
                  <template slot-scope="scope">
                    <div>
                      〔
                      {{
                        scope.row.exportDocumentLevel
                          ? convertToChinaNum(scope.row.exportDocumentLevel)
                          : ""
                      }}〕{{ scope.row.id }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="exportDocumentTemplatesNum"
                  label="导出状态"
                  min-width="80"
                  ><template slot-scope="scope">
                    <span v-if="scope.row.exportStatus === '1'">待生成</span>
                    <span v-if="scope.row.exportStatus === '2'">生成中</span>
                    <span v-if="scope.row.exportStatus === '3'">已完成</span>
                  </template>
                </el-table-column>
                <el-table-column prop="createBy" label="操作人" min-width="60">
                </el-table-column>
                <el-table-column
                  prop="createTime"
                  label="导出时间"
                  min-width="150"
                >
                </el-table-column>

                <el-table-column fixed="right" label="操作" width="200">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      @click="handleShowDialog(scope.$index, scope.row)"
                    >
                      预览
                    </el-button>
                    <el-divider direction="vertical"></el-divider>
                    <el-button
                      size="mini"
                      type="text"
                      :disabled="$store.state.user.isDownload"
                      @click="exportDocumentget(scope.row)"
                    >
                      导出
                    </el-button>
                    <el-divider direction="vertical"></el-divider>
                    <el-button
                      size="mini"
                      type="text"
                      @click="
                        deletedocumentExportRecord(scope.$index, scope.row)
                      "
                      >删除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="emr-container-pag">
              <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="queryData.pageNum"
                :page-sizes="[5, 10, 15, 20]"
                :page-size="queryData.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalNum"
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </MainCard>
    <MainCard v-show="Boolean(this.previewdocumentstate)">
      <div class="emr-container-title-box">
        <div>
          <el-button
            type="text"
            @click="
              exportRecordId = '';
              schedule = 0;
              exportDocumentTypelist = ['0'];
              previewdocumentstate = false;
            "
            icon="el-icon-back"
            >返回</el-button
          >
          <el-divider direction="vertical"></el-divider>
          <span>文档预览</span>
        </div>
        <div>
          <div
            :class="activeIndex === item.index ? 'active' : ''"
            v-for="item in menulist"
            :key="item.index"
            v-show="exportDocumentTypelist.includes(item.index)"
            @click="handleSelect(item.index)"
          >
            {{ item.name }}
            <div></div>
          </div>
        </div>

        <div>
          <span>
            <svg-icon icon-class="icon_10" />评价等级：
            <b> {{ queryData.exportDocumentLevel }}级</b>
          </span>
          <el-popconfirm title="是否下载该文档?" @onConfirm="exportDocumentget">
            <el-button
              type="primary"
              size="mini"
              slot="reference"
              :disabled="$store.state.user.isDownload"
              :icon="
                $store.state.user.isDownload
                  ? 'el-icon-loading'
                  : 'el-icon-folder-opened'
              "
            >
              {{ $store.state.user.isDownload ? "正在导出文档" : "导出文档" }}
            </el-button>
          </el-popconfirm>
        </div>
      </div></MainCard
    >
    <component
      :is="comName"
      v-if="Boolean(this.previewdocumentstate)"
      :exportRecordId="exportRecordId"
      :exportDocumentLevel="queryData.exportDocumentLevel"
      :alarmfailureResult="alarmfailureResult"
      :errorfailureResult="errorfailureResult"
      :activeporject="activeporject"
    ></component>

    <div class="downzuixiahua" v-show="schedule > 0">
      <span>{{ schedule }}%文档生成中……</span>
      <span>
        <svg-icon icon-class="zuidahua" @click="dialogFormVisible = true"
      /></span>
    </div>
    <el-dialog
      v-dialogDrag
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :show-close="false"
      width="300px"
    >
      <div class="outsidedialog">
        <div class="dialog-new-title">
          <h4></h4>
          <el-tooltip content="最小化窗口" placement="bottom" effect="light">
            <svg-icon
              icon-class="zuixiaohua"
              @click="
                dialogFormVisible = false;
                schedule = 0;
              "
            />
          </el-tooltip>
        </div>
        <div v-if="Number(schedule) < 100">
          <div class="progress">
            <el-progress
              type="circle"
              :show-text="false"
              :percentage="Number(schedule)"
              :width="150"
              color="#5270DD"
            ></el-progress>
            <div class="container">
              <div>{{ schedule }}%</div>
              <div>文档生成中</div>
              <div>……</div>
            </div>
          </div>

          <div class="time">
            <span
              >已耗时间<span v-show="parseInt(scheduletime / 60) > 0"
                >{{ parseInt(scheduletime / 60) }}分钟</span
              >{{ scheduletime % 60 }}秒…
            </span>
          </div>
          <div class="wordlist">
            <div
              v-for="item in menulist"
              :key="item.index"
              v-show="exportDocumentTypelist.includes(item.index)"
            >
              <svg-icon icon-class="jiazai" />
              <span>{{ item.name }}</span>
            </div>
          </div>
        </div>
        <div v-else>
          <div class="accomplishimg">
            <img class="img" src="../../../assets/emrimg/icon_complete.png" />
            <div class="accomplish">
              <div>已完成</div>
              <div>{{ exportDocumentTypelist.length }}个文档生成成功</div>
            </div>
          </div>
          <div class="buttonbox">
            <el-button
              type="primary"
              @click="
                handleSelect('0');
                dialogFormVisible = false;
                previewdocumentstate = true;
                schedule = 0;
              "
            >
              在线预览文档
            </el-button>
            <el-button
              @click="
                dialogFormVisible = false;
                schedule = 0;
                exportDocumentget();
              "
            >
              导出文档
            </el-button>
            <el-button
              type="text"
              @click="
                dialogFormVisible = false;
                schedule = 0;
              "
            >
              完成
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  previewExport,
  exportAsync,
  getCurrentProgress,
  querydocumentExportRecord,
  deletedocumentExportRecord,
} from "@/api/document-management/document-review";
import { queryAllow } from "@/api/document-management/dictionary-configuration";
import MedicalRecord from "./components/MedicalRecord.vue";
import BasicdataDictionary from "./components/BasicdataDictionary.vue";
import MedicalRecordDataDictionary from "./components/MedicalRecordDataDictionary.vue";
import QualitativeDataDictionary from "./components/QualitativeDataDictionary.vue";
export default {
  components: {
    MedicalRecord,
    BasicdataDictionary,
    MedicalRecordDataDictionary,
    QualitativeDataDictionary,
  },
  data() {
    return {
      loading: false,
      dialogFormVisible: false, //生成弹窗状态
      previewdocumentstate: false, //预览和生成状态
      builddisabled: false, //生成按钮状态
      comName: BasicdataDictionary, //激活菜单组件
      schedule: 0, //生成进度数
      scheduletime: 0, //生成进度数
      totalNum: 1, //导出记录总数
      activeIndex: "0", //预览时激活菜单
      exportRecordId: "", //预览ID
      timer: "", //计时器
      allDocxNum: "",
      activeporject: {
        dataStartTime: "",
        dataEndTime: "",
        levelCode: "",
      },
      exportDocumentTypelist: ["0"], //导出文档类型列表
      levelCodeData: [], //等级
      tableData: [], // 生成记录表格表格数据
      dataval: [], //导出记录数据
      dataval1: [], //查询记录时间数据
      alarmfailureResult: [], //告警列表
      errorfailureResult: [], //错误列表
      // 生成文档规则
      rules: {
        exportDocumentLevel: [
          { required: true, message: "请选择文档级别", trigger: "change" },
        ],
        dataStartTime: [
          { required: true, message: "请选择", trigger: "change" },
        ],
      },
      option: {
        disabledDate: (time) => {
          return time.getTime() < Date.now() - 1 * 24 * 3600 * 1000;
        },
      },
      // 生成表单文档数据
      createformData: {
        exportDocumentLevel: "",
        dataEndTime: "",
        dataStartTime: "",
        timedExportTime: "",
        exportDocumentType: "",
      },
      // 查询导出文档数据
      queryData: {
        startTime: "",
        endTime: "",
        exportDocumentLevel: "",
        createBy: "",
        pageNum: 1,
        pageSize: 10,
      },
      // 时间快捷选择快捷方式
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(new Date(new Date().setHours(0, 0, 0, 0)));
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      menulist: [
        { index: "0", name: "数据质量文档" },
        { index: "1", name: "基础数据填报" },
        { index: "2", name: "病历数据填报" },
        { index: "3", name: "质量数据填报" },
      ],
      // projectList: [],
      // selectedProject: "",
    };
  },
  created() {
    if (this.$route.params.id === undefined) {
      this.activeporject = JSON.parse(sessionStorage.getItem("projectactive"));
    } else {
      this.activeporject = this.$route.params;
    }

    this.dataval = [
      this.activeporject.dataStartTime.substring(0, 7),
      this.activeporject.dataEndTime.substring(0, 7),
    ];
    (this.createformData = {
      exportDocumentLevel: this.activeporject.levelCode,
      dataEndTime: this.activeporject.dataEndTime,
      dataStartTime: this.activeporject.dataStartTime,
      timedExportTime: "",
      exportDocumentType: "",
    }),
      //导出记录数据
      // queryProject({ projectType: 0 }).then((res) => {
      //   if (res && "data" in res) {
      //     this.projectList = res.data;
      //     this.selectedProject = this.$route.params.id;
      //   }
      // });
      // 进入页面初始化查询等级
      queryAllow({}).then((res) => {
        const filteredData = res.data.list.filter(
          (item) => parseInt(item.levelCode) <= this.activeporject.levelCode
        );
        this.levelCodeData = filteredData;
        // 进入页面初始化查询
        this.querydocumentExportRecord();
      });
  },
  methods: {
    // 查询文档导出记录
    querydocumentExportRecord() {
      querydocumentExportRecord({
        ...this.queryData,
        projectId: this.activeporject.id,
      }).then((res) => {
        if (res.status === 0) {
          this.tableData = res.data.list;
          this.totalNum = res.data.total;
        }
      });
    },

    // 改变页面显示条数
    handleSizeChange(val) {
      this.queryData.pageSize = val;
      this.querydocumentExportRecord();
    },
    // 改变页数
    handleCurrentChange(val) {
      this.queryData.pageNum = val;
      this.querydocumentExportRecord();
    },
    //获取本月的第一天 获取本月的最后一天
    changeMonth() {
      let myDate = new Date(this.dataval[1]);
      let month = myDate.getMonth() + 1;
      month = month < 10 ? "0" + month : month; //格式化月份，补0
      let dayEnd = new Date(myDate.getFullYear(), month, 0).getDate(); //获取当月一共有多少天
      this.dataval = [this.dataval[0] + "-01", this.dataval[1] + "-" + dayEnd];
      this.createformData.dataStartTime = this.dataval[0];
      this.createformData.dataEndTime = this.dataval[1];
    },

    // 查询列表树，生成预览文档
    queryrequireProjectDictionary() {
      this.createformData.exportDocumentType =
        this.exportDocumentTypelist.join(",");
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          this.allDocxNum = "";
          this.schedule = 0;
          this.failureResult = [];
          this.scheduletime = 0;
          this.builddisabled = true;
          this.dialogFormVisible = this.createformData.timedExportTime
            ? false
            : true;
          previewExport({
            ...this.createformData,
            projectId: this.activeporject.id,
          }).then((res) => {
            if (res.status === 0) {
              if (this.createformData.timedExportTime) {
                this.$message({
                  message: "定时生成文档成功",
                  type: "success",
                });
                // this.dataval = [];
                this.builddisabled = false;
                this.querydocumentExportRecord();
              } else {
                this.exportRecordId = res.data;
                let that = this;
                this.myTimer();
                that.timer = setInterval(this.myTimer, 1000); //第一个参数不可以写成this.myTimer()
              }
            } else {
              this.loading = false;
              this.dialogFormVisible = false;
              this.$message({
                message: res.msg,
                type: "error",
              });
            }
          });
        } else {
          this.builddisabled = false;
          return false;
        }
      });
    },

    // 导出文档操作
    async exportDocumentget(data) {
      if (data) {
        this.exportRecordId = data.id;
      }
      await exportAsync({ exportRecordId: this.exportRecordId }).then((res) => {
        if (res.status === 0) {
          this.$store.commit("user/SET_ISDOWNLOAD", true);
        } else {
          this.$message({
            type: "error",
            message: res.msg,
          });
        }
      });
    },
    // 获取加载进度
    myTimer() {
      this.scheduletime = this.scheduletime + 1;
      getCurrentProgress({
        allDocxNum: this.allDocxNum,
        exportRecordId: this.exportRecordId,
      }).then((res) => {
        if (
          (res.status === 0) &
          (res.data.allDocxNum === res.data.finishDocxNum)
        ) {
          this.alarmfailureResult = [...res.data.failureAndAlarmResult.告警];
          this.errorfailureResult = [...res.data.failureAndAlarmResult.失败];
          this.schedule = (
            (res.data.finishDocxNum / res.data.allDocxNum) *
            100
          ).toFixed(0);
          //清除定时器
          this.myStopFunction();
          this.queryData = {
            startTime: "",
            endTime: "",
            exportDocumentLevel: "",
            createBy: "",
            pageNum: 1,
            pageSize: 10,
          };
          this.dataval1 = [];
          this.querydocumentExportRecord();
          this.queryData.exportDocumentLevel =
            this.createformData.exportDocumentLevel;
          this.builddisabled = false;
        } else {
          this.schedule = (
            (res.data.finishDocxNum / res.data.allDocxNum) *
            100
          ).toFixed(0);
          this.allDocxNum = res.data.allDocxNum;
        }
      });
    },

    //清除定时器
    myStopFunction() {
      clearInterval(this.timer);
    },

    // 打开文档预览
    async handleShowDialog(index, row, type) {
      this.comName = "";
      this.exportRecordId = row.id;
      this.exportDocumentTypelist = row.exportDocumentType.split(",");
      this.queryData.exportDocumentLevel = row.exportDocumentLevel;
      this.previewdocumentstate = true;
      this.handleSelect("0");
    },

    // 删除
    deletedocumentExportRecord(index, row) {
      deletedocumentExportRecord(row.id).then((res) => {});
      this.$confirm("此操作将删除该记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deletedocumentExportRecord(row.id).then((res) => {
            // this.loading = true;
            if (res.status === 0) {
              this.$message({
                message: "删除成功!",
                type: "success",
              });
              this.querydocumentExportRecord();
            } else {
              this.$message({
                message: res.msg,
                type: "error",
              });
              this.loading = false;
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },

    // 等级
    convertToChinaNum(num) {
      let returnstr = "";
      this.levelCodeData.forEach((item) => {
        if (Number(item.levelCode) === Number(num)) {
          returnstr = item.levelName;
        }
      });
      return returnstr;
    },

    // 重置
    resetForm(formName) {
      this.queryData = {
        // 查询数据
        startTime: "",
        endTime: "",
        exportDocumentLevel: "",
        createBy: "",
        pageNum: 1,
        pageSize: 10,
      };
      // 重置时间
      this.dataval1 = [];
      this.querydocumentExportRecord();
    },

    // 时间改变
    async changedataval() {
      if (this.dataval1.length > 1) {
        this.queryData.startTime = this.dataval1[0];
        this.queryData.endTime = this.dataval1[1];
      } else {
        this.queryData.startTime = "";
        this.queryData.endTime = "";
      }
      this.querydocumentExportRecord();
    },

    // 切换菜单
    handleSelect(key, keyPath) {
      this.activeIndex = key;
      if (key === "0") {
        this.comName = MedicalRecord;
      } else if (key === "1") {
        this.comName = BasicdataDictionary;
      } else if (key === "2") {
        this.comName = MedicalRecordDataDictionary;
      } else if (key === "3") {
        this.comName = QualitativeDataDictionary;
      } else {
        this.comName = "";
      }
    },

    handle: function () {
      var startAt = (new Date(this.date) * 1000) / 1000;
      if (startAt < Date.now()) {
        this.date = new Date();
      }
    },
  },
};
</script>

<style scoped lang="scss">
@import "@/styles/emr-styles/emr-main-table.scss";

.emr-container {
  margin-bottom: 40px;
  .emr-container-search {
    display: flex;
    min-width: 1200px;
    justify-content: center;
    height: 100%;
    .emr-container-search-left {
      width: 300px;
      border-right: 1px solid #dbdde1;
      padding: 20px 30px;
      .el-form-item {
        // text-align: center;
        .el-select {
          width: 100%;
        }
        .el-date-editor {
          width: 100%;
        }
      }
    }
    .emr-container-search-right {
      padding: 20px 30px;
      flex: 1;
    }
  }
  .emr-container-title-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    > div:nth-child(1) {
      font-size: 14px;
      .el-divider {
        margin: 0px 10px;
      }
      .el-button {
        color: #666666;
      }
      span {
        color: #9da4ba;
      }
    }
    > div:nth-child(2) {
      display: flex;
      font-size: 14px;
      color: #333333;
      > div {
        margin: 0px 10px;
      }
      .active {
        color: #5270dd;
        position: relative;
        > div {
          border-bottom: 4px solid #5270dd;
          width: 30px;
          position: absolute;
          bottom: -8px;
          left: 26px;
        }
      }
    }
    > div:nth-child(3) {
      font-size: 14px;
      > span {
        margin: 0px 10px;
        svg {
          margin: 0px 4px;
        }
      }
    }
  }
}

//checkBox自定义禁用样式
::v-deep .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
  border-color: #fff;
}
::v-deep .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
  background-color: #409eff;
  border-color: #dcdfe6;
}
::v-deep .el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #409eff;
}

::v-deep .el-dialog__body {
  // display: flex;
  // min-height: 600px;
  height: 100%;
  padding: 0px;
  padding: 0px 20px 20px 20px;
  .outsidedialog {
    min-height: 340px;
    .dialog-new-title {
      width: 100%;
      display: flex;
      justify-content: space-between;
      svg {
        cursor: pointer;
      }
    }
    .progress {
      display: flex;
      position: relative;
      justify-content: space-around;
      .container {
        position: absolute;
        top: 25%;
        > div {
          text-align: center;
        }
        > div:nth-child(1) {
          font-weight: 400;
          font-size: 36px;
          color: #333333;
          line-height: 50px;
        }
      }
    }
    .time {
      font-weight: 400;
      font-size: 14px;
      color: #888888;
      line-height: 17px;
      margin: 40px 0px 10px;
      display: flex;
      justify-content: space-between;
    }
    .wordlist {
      background: #f5f5f5;
      border-radius: 9px;
      padding: 10px;
      > div {
        line-height: 30px;
      }
      span {
        margin: 0px 6px;
      }
      .el-icon-success {
        color: #5270dd;
      }
    }
    .accomplishimg {
      text-align: center;

      .accomplish > div:nth-child(1) {
        font-size: 24px;
        color: #333333;
        line-height: 50px;
      }
    }
    .buttonbox {
      margin-top: 30px;
      text-align: center;
      .el-button {
        width: 150px;
        margin: 10px 0px;
      }
    }
  }
}
.downzuixiahua {
  position: fixed;
  bottom: 0px;
  z-index: 9999999999999999999999;
  width: 220px;
  height: 40px;
  background: #5270dd;
  box-shadow: 0px 0px 4px 0px rgba(82, 112, 221, 0.4);
  border-radius: 12px 12px 0px 0px;
  display: flex;
  line-height: 40px;
  color: #fff;
  padding: 0px 10px;
  justify-content: space-between;
}
.header {
  ::v-deep .el-input__inner {
    border: none;
    box-shadow: none;
    font-size: 20px;
    color: red;
    font-weight: bold;
  }
  ::v-deep .el-input {
    margin-bottom: 20px;
  }
}
</style>
