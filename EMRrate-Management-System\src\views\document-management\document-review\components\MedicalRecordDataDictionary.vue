<template>
  <MainCard>
    <div class="emr-headline">
      <i></i>
      病历数据填报
      <span
        class="aboutme"
        v-if="activeporject.personInCharge.includes($store.state.user.loginId)"
      >
        是否和我相关
        <el-switch
          @change="changeaboutme"
          v-model="aboutme"
          active-color="#4969de"
          inactive-color="#aaa"
        >
        </el-switch
      ></span>
    </div>
    <div class="emr-container-main">
      <div class="emr-container-main-left">
        <div>
          <div
            v-for="Tabs in editableTabs"
            :key="Tabs.id"
            :class="activemenu === Tabs.id ? 'active' : ''"
            @click="handleRightTitle(Tabs)"
          >
            <i
              class="el-icon-warning"
              v-if="notMatchDataQualityIndexFirst.includes(Tabs.directoryCode)"
            ></i>
            <i class="el-icon-circle-check" v-else></i>
            {{ Tabs.directoryCode }}{{ Tabs.directoryName }}
          </div>
        </div>
        <div
          :class="{
            'pass-on': Statusresult === '0',
            'pass-off': Statusresult === '1',
          }"
        >
          <div>校验结果</div>
          <!-- <div>数据项总条数：57</div>
          <div>
            <i class="el-icon-circle-check"></i>符合标准：<b
              class="circle-check"
              >44</b
            >
          </div>
          <div>
            <i class="el-icon-warning"></i>不符合标准：<b class="warning">13</b>
          </div> -->
        </div>
      </div>
      <div class="emr-container-main-right">
        <el-collapse v-model="activeName">
          <el-collapse-item
            :name="Tabss.id"
            v-for="Tabss in elcollapseTabs"
            :key="Tabss.id"
            @click.native="handletableData(Tabss)"
          >
            <template slot="title">
              <h4>
                <i
                  class="el-icon-warning"
                  v-if="
                    notMatchDataQualityIndexSecond.includes(Tabss.directoryCode)
                  "
                ></i>
                <i class="el-icon-circle-check" v-else></i>
                {{ Tabss.directoryName }}({{ Tabss.directoryCode }})
              </h4>
            </template>

            <el-table
              :data="Tabss.tableData"
              style="width: 100%"
              row-key="id"
              ref="LoadTable"
              :header-cell-style="{ background: '#fff', color: '#606266' }"
            >
              <el-table-column prop="directoryCode" label="编号" width="150">
                <template slot-scope="scope">
                  <span
                    v-if="scope.row.dataQualityIndex.includes('不足标准')"
                    style="color: red"
                  >
                    {{ scope.row.directoryCode }}
                  </span>
                  <span v-else>
                    {{ scope.row.directoryCode }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="directoryName" label="名称" width="600"
                ><template slot-scope="scope">
                  <div
                    style="text-align: left; color: red"
                    v-if="scope.row.dataQualityIndex.includes('不足标准')"
                  >
                    {{ scope.row.directoryName }}
                  </div>
                  <div style="text-align: left" v-else>
                    {{ scope.row.directoryName }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="allNum" label="数量" width="100">
                <template slot-scope="scope">
                  <span
                    v-if="scope.row.dataQualityIndex.includes('不足标准')"
                    style="color: red"
                  >
                    {{ scope.row.allNum }}
                  </span>
                  <span v-else>
                    {{ scope.row.allNum }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="dataUnit" label="单位" min-width="100">
                <template slot-scope="scope">
                  <span
                    v-if="scope.row.dataQualityIndex.includes('不足标准')"
                    style="color: red"
                  >
                    {{ scope.row.dataUnit }}
                  </span>
                  <span v-else>
                    {{ scope.row.dataUnit }}
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </MainCard>
</template>

<script>
import {
  getMedicaRecordsOrQuality,
  getMedicaRecordsOrQualityStatus,
} from "@/api/document-management/document-review";
export default {
  data() {
    return {
      tableData: [], // 表格数据
      activeName: "",
      activemenu: "",
      editableTabs: [],
      elcollapseTabs: [],
      querydirectoryCode: "",
      editableTabsValue: "0",
      directoryName: "",
      loading: false,
      Statusresult: "",
      Statusreason: "",
      notMatchDataQualityIndexFirst: "",
      notMatchDataQualityIndexSecond: "",
      aboutme: true,
    };
  },
  props: {
    exportRecordId: {
      type: String,
    },
    activeporject: {
      type: Object,
    },
  },
  created() {
    this.handleLeftTitle(null);
    this.getMedicaRecordsOrQualityStatus();
  },
  methods: {
    getMedicaRecordsOrQualityStatus() {
      getMedicaRecordsOrQualityStatus({
        exportRecordId: this.exportRecordId,
        directoryType: 2,
        directoryCode: "",
        userAccount: this.aboutme ? this.$store.state.user.loginId : null,
      }).then((res) => {
        this.Statusresult = res.data.result;
        this.Statusreason = res.data.reason;
        this.notMatchDataQualityIndexFirst =
          res.data.notMatchDataQualityIndexFirst;
        this.notMatchDataQualityIndexSecond =
          res.data.notMatchDataQualityIndexSecond;
      });
    },
    // 查询左侧一级
    handleLeftTitle() {
      getMedicaRecordsOrQuality({
        exportRecordId: this.exportRecordId,
        directoryType: 2,
        directoryCode: "",
        userAccount: this.aboutme ? this.$store.state.user.loginId :null,
      }).then((res) => {
        this.editableTabs = res.data;
        this.activemenu = res.data[0].id;
        getMedicaRecordsOrQuality({
          exportRecordId: this.exportRecordId,
          directoryType: 2,
          directoryCode: res.data[0].directoryCode,
          userAccount: this.aboutme ? this.$store.state.user.loginId : null,
        }).then((ress) => {
          this.elcollapseTabs = ress.data.map((item) => ({
            ...item,
            tableData: [],
          }));
          this.activeName = ress.data[0].id;
          if (ress.data.length > 0) {
            this.handletableData(this.elcollapseTabs[0]);
          }
        });
      });
    },
    // 查询右侧一级
    handleRightTitle(Tabs) {
      this.activemenu = Tabs.id;
      getMedicaRecordsOrQuality({
        exportRecordId: this.exportRecordId,
        directoryType: 2,
        directoryCode: Tabs.directoryCode,
        userAccount: this.aboutme ? this.$store.state.user.loginId : null,
      }).then((ress) => {
        this.elcollapseTabs = ress.data.map((item) => ({
          ...item,
          tableData: [],
        }));
        this.activeName = ress.data[0].id;
        if (ress.data.length > 0) {
          this.handletableData(this.elcollapseTabs[0]);
        }
      });
    },
    // 查询表格
    handletableData(row) {
      this.tableData = [];
      this.loading = true;
      getMedicaRecordsOrQuality({
        exportRecordId: this.exportRecordId,
        directoryType: 2,
        directoryCode: row.directoryCode,
        userAccount: this.aboutme ? this.$store.state.user.loginId : null,
      }).then((res) => {
        this.elcollapseTabs[this.elcollapseTabs.indexOf(row)].tableData =
          res.data;
        this.loading = false;
      });
    },

    // 切换和相关
    changeaboutme() {
      this.handleLeftTitle(null);
      this.getMedicaRecordsOrQualityStatus();
    },
  },
};
</script>

<style lang="scss" scoped>
.emr-container-main {
  display: flex;
  .emr-container-main-left {
    margin-top: 20px;
    font-size: 14px;
    > div:nth-child(1) {
      width: 226px;
      min-height: 350px;
      padding: 10px 10px;
      background: #ffffff;
      border-radius: 9px;
      border: 1px solid #e3e6e8;
      line-height: 20px;
      margin-bottom: 20px;
      > div {
        padding: 6px 10px;
        margin: 10px 0px;
      }
    }
    > div:nth-child(2) {
      width: 226px;
      height: 80px;
      padding: 10px 10px;
      background-color: #ffffff;
      background-position: top right;
      background-repeat: no-repeat;
      border-radius: 9px;
      border: 1px solid #e3e6e8;
      > div:nth-child(1) {
        font-weight: bold;
        font-size: 16px;
        color: #888888;
        line-height: 36px;
      }
      > div:nth-child(2) {
        font-weight: bold;
        font-size: 14px;
        color: #000000;
        line-height: 36px;
      }
      > div:nth-child(3),
      > div:nth-child(4) {
        font-size: 14px;
        color: #000000;
        line-height: 36px;
      }
    }
    .pass-on {
      background-image: url("../../../../assets/emrimg/verifyResults_yes.png");
    }
    .pass-off {
      background-image: url("../../../../assets/emrimg/verifyResults_no.png");
    }
    .active {
      background: #e0e4f9;
      border-radius: 5px;
      font-weight: 600;
    }
  }
  .emr-container-main-right {
    flex: 1;
    padding: 0px 20px;
  }
  .el-icon-warning,
  .warning {
    color: #e64747;
  }
  .el-icon-circle-check,
  .circle-check {
    color: #46c376;
  }
}
.aboutme {
  color: #aaa;
  font-size: 14px;
}
</style>
