<template>
  <div
    :class="{ 'has-logo': true }"
    class="nav">
    <div class="sideTop">
      <img
        :style="{ width: isShow ? '140px' : '40px',marginTop: isShow ? '0' : '10px'}"
        class="img"
        :src="sideImg"
        alt="商标"
        @click="linkTo" />
      <!-- <div v-show="hospitalName">{{ hospitalName }}</div> -->
    </div>
    <div
      v-show="isShow"
      class="description">
      <div class="title">电子病历评级</div>
      <div class="mgt-box">
        <div class="mgt-content">文档协作管理</div>
      </div>
    </div>
    <div class="first-level-menu">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        @select="(val) => handleMenuActive(val, 'click')"
        class="el-menu-vertical-demo"
        :text-color="'black'"
        :collapse-transition="true"
        mode="vertical">
        <template v-for="route in permission_routes">
          <el-menu-item
            :class="{ 'menu-on': isShow, 'menu-off': !isShow }"
            v-if="route.meta"
            :key="route.path"
            :index="route.path">
            <img
              class="menu-img"
              :src="
                activeMenu === route.path
                  ? route.meta.menuIcons.active
                  : route.meta.menuIcons.normal
              "
              alt="" />
            <span slot="title">{{ route.meta.title }}</span>
          </el-menu-item>
        </template>
      </el-menu>
    </div>
    <div class="footer-fold">
      <div
        class="footer-icon"
        @click="handlerFold">
        <el-tooltip
          effect="dark"
          v-show="isShow"
          content="收起"
          placement="top">
          <img :src="menu_off" />
        </el-tooltip>
        <el-tooltip
          v-show="!isShow"
          effect="dark"
          content="展开"
          placement="top">
          <img :src="menu_on" />
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<script>
import Logo from '../../components/Logo.vue'
import { mapGetters } from 'vuex'
import SidebarItem from './SidebarItem'
import {
  getHospitalLogoPath as _getHospitalLogoPath,
  queryAllSysConfig as _queryAllSysConfig
} from '@/api/sys-config'
import menu_off from '@/assets/sidebar/menu_off.png'
import menu_on from '@/assets/sidebar/menu_on.png'
export default {
  data() {
    return {
      sideImg: '',
      menu_off,
      menu_on,
      hospitalName: '',
      isShow: true,
      isCollapse: false
    }
  },
  components: { SidebarItem, Logo },
  mounted() {
    _getHospitalLogoPath({ logoType: 'system.logo.hospitalImg2' }).then(
      (res) => {
        this.sideImg = res.data
      }
    )
    _queryAllSysConfig().then((res) => {
      this.hospitalName = res.data.hospitalName
    })
    // console.log(this.permission_routes)
  },
  computed: {
    ...mapGetters(['permission_routes']),
    activeMenu() {
      const { path } = this.$route
      return '/' + path.split('/')[1]
    }
  },

  methods: {
    linkTo() {
      // 跳首页
      this.$router.push('/home-page')
    },
    clickFold() {
      this.$router.push('/')
    },
    handlerFold() {
      this.isCollapse = !this.isCollapse
      if (!this.isShow) {
        setTimeout(() => {
          this.isShow = !this.isShow
        }, 150)
      } else {
        this.isShow = !this.isShow
      }
    },
    handleMenuActive(val, type) {
      const curMenuItem = this.permission_routes.filter(
        (item) => item.path === val
      )[0]
      this.$emit('getCurMenuItem', curMenuItem)
      if (type === 'click') {
        const fullPath = `${curMenuItem.path}/${curMenuItem.children[0].path}`
        this.$router.push(fullPath)
      }
    }
  },
  watch: {
    permission_routes: {
      handler(val) {
        if (val) {
          const { path } = this.$route.matched[0]
          if (path === '/home-page') return
          this.handleMenuActive(path, 'refrash')
        }
      },
      immediate: true
    },
    '$route.path': {
      handler(val) {
        if (val) {
          const { path } = this.$route.matched[0]
          if (path === '/home-page') return
          this.handleMenuActive(path, 'refrash')
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.nav {
  position: relative;
  z-index: 100;
  height: 100%;
  background: url(~@/assets/sidebar/sidebar_bg.png) right 300px / contain
      no-repeat,
    url(~@/assets/sidebar/sidebar_shadow.png) right / cover no-repeat;
  display: flex;
  flex-direction: column;
  .sideTop {
    text-align: center;
    margin-top: 10px;
    .img {
      margin-top: -10px;
      width: 140px;
      cursor: pointer;
    }
    div {
      height: 30px;
      color: #fff;
      text-align: center;
      font-size: 20px;
      margin-bottom: 10px;
    }
  }
  .description {
    margin-bottom: 10px;
    text-align: center;
    .title {
      color: #262626;
      font-size: 24px;
      font-weight: 700;
    }
    .mgt-box {
      background: url(~@/assets/sidebar/sidebar_line.png) center center
        no-repeat;
      .mgt-content {
        background-color: #fff;
        line-height: 26px;
        width: 112px;
        margin: 0 auto;
        color: #5270dd;
        font-weight: 600;
        word-spacing: 1px;
        border-radius: 4px;
        margin-top: 10px;
      }
    }
  }
  .first-level-menu {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    .el-menu-vertical-demo .menu-img {
      margin-right: 10px;
    }
    .el-menu-vertical-demo:not(.el-menu--collapse) {
      width: 224px;
    }
  }
  .footer-fold {
    display: flex;
    justify-content: flex-end;
    padding: 20px 14px 20px 0;
    .footer-icon {
      padding: 10px;
      cursor: pointer;
      border-radius: 6px;
      &:hover {
        background-color: #d4dbf3;
      }
      img {
        display: block;
      }
    }
  }
}
.el-menu-item.is-active {
  color: white;
  background-color: #5270dd;
}
.el-menu-item {
  border-radius: 10px;
  height: 40px;
  line-height: 40px;
  &:hover:not(.is-active) {
    background-color: #dfe5ff;
  }
}
.menu-on {
  margin: 10px 26px;
}
.menu-off {
  border-radius: 0;
  height: 64px;
  line-height: 60px;
  ::v-deep .el-tooltip {
    padding: 0 22px!important;
  }
}
.el-menu {
  background-color: transparent;
  margin-top: 10px;
}
</style>
