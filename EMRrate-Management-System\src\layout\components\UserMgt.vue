<template>
  <div class="user-mgt-container">
    <svg-icon
      icon-class="user_touxiang"
      style="font-size: 30px; margin-right: 10px" />
    <el-dropdown
      @command="handleCommand"
      trigger="click"
      style="cursor: pointer">
      <span
        class="el-dropdown-link"
        style="font-size: 16px">
        {{ name }}
        <i class="el-icon-arrow-down el-icon--right"></i>
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="updatePwd">修改密码</el-dropdown-item>
        <el-dropdown-item command="logout">退出登录</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <div
      @click="handleCommand('logout')"
      class="logout">
      <svg-icon icon-class="logout_img" />
    </div>
    <UpdatePassword
      :status="status"
      ref="updatePassword" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import UpdatePassword from '@/components/UpdatePassword'
export default {
  components: { UpdatePassword },
  computed: {
    ...mapGetters(['name'])
  },
  data() {
    return {
      status: 0
    }
  },
  methods: {
    // 修改密码
    updatePwd() {
      this.$refs.updatePassword.dialogVisible = true
    },
    // 注销
    logout() {
      this.$confirm('此操作将退出登录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          // 登出并清除自动登出定时任务及移动事件
          await this.$store.dispatch('user/logout')
          this.$router.push('/login')

          if (localStorage.getItem('timeOutId')) {
            clearTimeout(localStorage.getItem('timeOutId'))
            localStorage.removeItem('timeOutId')
          }
          window.document.onmousemove = null
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消退出'
          })
        })
    },
    handleCommand(command) {
      this[command]()
    }
  }
}
</script>

<style lang="scss" scoped>
.user-mgt-container {
  display: flex;
  align-items: center;
  .avatar {
    margin-right: 9px;
    cursor: pointer;
  }
  .icon {
    cursor: pointer;
  }
  .logout {
    padding: 0 10px;
    margin-left: 10px;
    border-left: 1px solid #c7cadd;
    cursor: pointer;
    font-size: 30px;
  }
}
</style>
