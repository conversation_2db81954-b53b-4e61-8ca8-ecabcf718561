<template>
  <MainCard>
    <div class="emr-container">
      <el-menu
        :default-active="activeIndex"
        class="el-menu-demo"
        mode="horizontal"
        @select="handleSelect"
      >
        <el-menu-item
          :index="item.index"
          v-for="item in menulist"
          :key="item.index"
          ><div>
            {{ item.name }} <i></i>

            <img
              v-if="activeIndex === item.index"
              src="./../../../assets/emrimg/icon_20.png"
              alt=""
            />
          </div>
        </el-menu-item>
      </el-menu>
      <component :is="comName"></component>
    </div>
  </MainCard>
</template>

<script>
import CatalogueConfiguration from "./components/CatalogueConfiguration.vue";
import BasicdataDictionary from "./components/BasicdataDictionary.vue";
import MedicalRecordDataDictionary from "./components/MedicalRecordDataDictionary.vue";
import QualitativeDataDictionary from "./components/QualitativeDataDictionary.vue";
export default {
  data() {
    return {
      activeIndex: "1",
      comName: CatalogueConfiguration,
      menulist: [
        { index: "1", name: "文档目录字典" },
        { index: "2", name: "基础数据填报字典" },
        { index: "3", name: "病历数据填报字典" },
        { index: "4", name: "质量数据填报字典" },
      ],
    };
  },
  components: {
    CatalogueConfiguration,
    BasicdataDictionary,
    MedicalRecordDataDictionary,
    QualitativeDataDictionary,
  },
  created() {},
  methods: {
    handleSelect(key, keyPath) {
      this.activeIndex = key;
      if (key === "1") {
        this.comName = CatalogueConfiguration;
      } else if (key === "2") {
        this.comName = BasicdataDictionary;
      } else if (key === "3") {
        this.comName = MedicalRecordDataDictionary;
      } else if (key === "4") {
        this.comName = QualitativeDataDictionary;
      } else {
        this.comName = "";
      }
    },
  },
};
</script>

<style scoped lang="scss">
@import "@/styles/emr-styles/emr-main-table.scss";
</style>
