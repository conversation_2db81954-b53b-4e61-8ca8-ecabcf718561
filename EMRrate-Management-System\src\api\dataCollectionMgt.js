import request from '@/utils/request'

/* 1. 数据源管理 */

// 查询数据源配置列表
export function queryDataSourceList(data) {
  return request({
    url: '/datasource/list',
    method: 'post',
    data
  })
}
// 新增数据源
export function addDataSource(data) {
  return request({
    url: '/datasource/add',
    method: 'post',
    data
  })
}
// 更新数据源
export function updateDataSource(data) {
  return request({
    url: '/datasource/update',
    method: 'post',
    data
  })
}
// 删除数据源
export function deleteDataSource(data) {
  return request({
    url: '/datasource/delete',
    method: 'delete',
    data
  })
}
// 测试数据源
export function testDataSource(data) {
  return request({
    url: '/datasource/testDb',
    method: 'post',
    data
  })
}



