<template>
  <div
    :style="{ overflow: isOverflowAuto ? 'auto' : 'none' }"
    class="main-card">
    <slot />
  </div>
</template>
<script>
export default {
  name: 'MainCard',
  props: {
    isOverflowAuto: {
      type: Boolean,
      default: true
    }
  }
}
</script>

<style lang="scss" scoped>
.main-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 0 14px rgba(34, 34, 34, 0.1);
  height: calc(100% - 28px);
}
</style>
