<template>
  <div>
    <!-- 长度规范性 -->
    <template v-if="formData.checkRuleType === 'CDGFX'">
      <el-form-item label="检测表或视图" prop="checkRuleTableOrView">
        <el-autocomplete
          ref="checkRuleTableOrView"
          v-model="formData.checkRuleTableOrView"
          :fetch-suggestions="querySearchAsync"
          value-key="tableOrViewName"
          @select="handlerTableOrViewSelect($event, 'checkRuleTableOrView')"
          placeholder="请输入内容"
          clearable
        ></el-autocomplete>
      </el-form-item>
      <el-form-item label="检测字段" prop="checkRuleColumn">
        <el-input
          :disabled="isDisabled"
          @click.native="handlerclick('checkRuleColumn')"
          v-model="formData.checkRuleColumn"
        >
          <el-button slot="append">
            <SelectFieldList
              type="checkRuleColumn"
              selectType="radio"
              @click.native.stop="handlerclick('checkRuleColumn')"
              :isDisabled="isDisabled"
              :checkRuleColumnData="checkRuleColumnData"
              @backfillSelectedData="backfillSelectedData"
            />
          </el-button>
        </el-input>
      </el-form-item>
      <el-form-item label="长度执行条件" prop="checkObjectType">
        <el-radio-group v-model="formData.checkObjectType">
          <el-radio label="0">等于</el-radio>
          <el-radio label="1">不等于</el-radio>
          <el-radio label="2">大于</el-radio>
          <el-radio label="3">小于</el-radio>
          <el-radio label="4">大于等于</el-radio>
          <el-radio label="5">小于等于</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="字段长度" prop="columnsLength">
        <el-input v-model="formData.columnsLength"></el-input>
      </el-form-item>
      <el-form-item label="精度执行条件" prop="prcsnExecCondCd">
        <el-radio-group v-model="formData.prcsnExecCondCd">
          <el-radio label="0">等于</el-radio>
          <el-radio label="1">不等于</el-radio>
          <el-radio label="2">大于</el-radio>
          <el-radio label="3">小于</el-radio>
          <el-radio label="4">大于等于</el-radio>
          <el-radio label="5">小于等于</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="字段精度" prop="resultSmlNmrlDgit">
        <el-input
          type="textarea"
          v-model="formData.resultSmlNmrlDgit"
        ></el-input>
      </el-form-item>
      <el-form-item label="明细表WHERE条件" prop="checkRuleWhere">
        <el-input type="textarea" v-model="formData.checkRuleWhere"></el-input>
      </el-form-item>
      <el-form-item label="问题明细显示字段" prop="pbSubsidiaryColumns">
        <el-input
          :disabled="isDisabled"
          @click.native="handlerclick"
          v-model="formData.pbSubsidiaryColumns"
        >
          <el-button slot="append">
            <SelectFieldList
              type="pbSubsidiaryColumns"
              selectType="checkBox"
              @click.native.stop="handlerclick"
              :isDisabled="isDisabled"
              :checkRuleColumnData="checkRuleColumnData"
              @backfillSelectedData="backfillSelectedData"
            />
          </el-button>
        </el-input>
      </el-form-item>
      <slot></slot>
    </template>

    <!-- 自定义规范性 -->
    <template v-if="formData.checkRuleType === 'ZDYGFX'">
      <CustomCheckRuleType :formData.sync="formData" />
    </template>
  </div>
</template>


<script>
import tableViewAndField from "@/mixins/tableViewAndField"
import CustomCheckRuleType from "@/components/CustomCheckRuleType/index.vue"
import SelectFieldList from "./common/SelectFieldList.vue"
import {
  getDictTable,
  getDictTableFiled,
} from "@/api/qualityRuleMgt/ruleConfig"
export default {
  data() {
    return {
      isDisabled: true,
      dictTableData: [], // 字典中的表数据
      dictTableFiledData: [], // 字典表字段数据
    }
  },

  components: {
    CustomCheckRuleType,
    SelectFieldList,
  },
  mixins: [tableViewAndField],
  props: {
    formData: {
      type: Object,
    },
    dataSourceId: {
      type: Number,
    },
  },
  mounted() {
    if (this.formData.checkRuleTableOrView) {
      this.queryCheckRuleColumnData.tableName =
        this.formData.checkRuleTableOrView
      this.queryCheckRuleColumnData.dataSourceId = this.dataSourceId
      this.getCheckRuleColumnData()
    }
  },
  watch: {
    formData: {
      handler(val) {
        // 只要表单有值变化就清空获取的SQL数据
        this.$emit("clearCheckSQLData")
        if (val.checkRuleTableOrView) {
          this.isDisabled = false
        } else {
          this.clearPartData()
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    //handlerClear
    clearPartData() {
      this.isDisabled = true
      this.checkRuleColumnData = []
      this.formData.checkRuleColumn = ""
      this.formData.pbSubsidiaryColumns = ""
    },
    // 先选择表或视图
    handlerclick(type) {
      if (this.isDisabled && type === "checkRuleColumn") {
        this.$message({
          type: "warning",
          message: "请先选择主表或视图",
        })
        this.$refs["checkRuleTableOrView"].focus()
      } else if (this.isDisabled) {
        this.$message({
          type: "warning",
          message: "请先选择主表或视图",
        })
        this.$refs["checkRuleTableOrView"].focus()
      }
    },
    // 当主表或视图或副表或视图选择时
    handlerTableOrViewSelect(e, type) {
      // console.log(e, type)
      if (type === "checkRuleTableOrView") {
        this.isDisabled = false
        this.queryCheckRuleColumnData.tableName = e.tableOrViewName
        this.queryCheckRuleColumnData.dataSourceId = this.row
          ? this.row.dataSourceId
          : this.dataSourceId
        this.getCheckRuleColumnData()
      }
      this.isDisabled = false
    },
    // 获取字典表字段
    getDictTableFiled() {
      getDictTableFiled({
        pageNum: 1,
        pageSize: 9999,
        fieldName: "",
      }).then((res) => {
        if (res.status === 0) {
          let dictTableFiledDataArr = res.data.list
          dictTableFiledDataArr.forEach((element, index) => {
            element.id = index
          })
          this.dictTableFiledData = dictTableFiledDataArr
        }
      })
    },
    // 异步搜索 字典表或视图的表
    querySearchDictAsync(queryString, cb) {
      this.dictTableData = []
      getDictTable({
        pageNum: 1,
        pageSize: 9999,
        tableName: queryString,
      }).then((res) => {
        if (res.status === 0) {
          res.data.list.forEach((element, index) => {
            this.dictTableData.push({
              id: index,
              mappingTabal: element.mappingTabal,
            })
          })
          cb(this.dictTableData)
        }
      })
    },
    backfillSelectedData(val, type) {
      if (type === "checkRuleColumn") {
        this.formData.checkRuleColumn = val.join(",")
      } else if (type === "pbSubsidiaryColumns") {
        this.formData.pbSubsidiaryColumns = val.join(",")
      } else if (type === "stCheckRuleColumn") {
        this.formData.stCheckRuleColumn = val.join(",")
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.selected {
  margin-bottom: 20px;
}
.el-form-item .el-form-item {
  display: flex;
  flex-direction: column;
  text-align: center;
}
</style>
