<template>
  <div class="add-code-value-content">
    <el-dialog
      v-dialogDrag
      :title="btnType ? '新增码值内容' : '编辑码值内容'"
      :visible.sync="dialogFormVisible"
      @open="handlerOpen"
      @closed="handlerClose"
      width="30%"
      :close-on-click-modal="false"
    >
      <el-form
        :model="formData"
        :rules="rules"
        :label-width="formLabelWidth"
        ref="ruleForm"
      >
        <el-form-item label="类型编码" prop="typeCode">
          <el-select v-model="formData.typeCode">
            <el-option
              v-for="item in typeCodeData"
              :key="item.id"
              :value="item.typeCode"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="键" prop="contentKey">
          <el-input v-model="formData.contentKey"></el-input>
        </el-form-item>
        <el-form-item label="值" prop="contentValue">
          <el-input v-model="formData.contentValue"></el-input>
        </el-form-item>
        <el-form-item label="内容描述" prop="contentDesc">
          <el-input v-model="formData.contentDesc"></el-input>
        </el-form-item>
        <el-form-item label="预留字段1" prop="data1">
          <el-input v-model="formData.data1"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="contentSeq">
          <el-input v-model="formData.contentSeq"></el-input>
        </el-form-item>
        <el-form-item label="预留字段2" prop="data2">
          <el-input v-model="formData.data2"></el-input>
        </el-form-item>
        <el-form-item label="预留字段3" prop="data3">
          <el-input v-model="formData.data3"></el-input>
        </el-form-item>
        <el-form-item label="预留字段4" prop="data4">
          <el-input v-model="formData.data4"></el-input>
        </el-form-item>
        <el-form-item label="预留字段5" prop="data5">
          <el-input v-model="formData.data5"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm('ruleForm')"
          >保存</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addCodeValueContent,
  updateCodeValueContent,
} from "@/api/codeValueMgt/codeValueContent"
import { queryCodeValueType } from "@/api/codeValueMgt/codeValueType"
export default {
  data() {
    return {
      dialogFormVisible: false, // 弹框状态
      formLabelWidth: "120px",
      isShowLoading: false, // 是否显示loading
      formData: {
        // 表单数据
        contentKey: "", // 键
        contentValue: "", // 值
        contentDesc: "", // 内容描述
        contentSeq: "", // 排序
        data1: "", // 预留字段1
        data2: "", // 预留字段2
        data3: "",
        data4: "",
        data5: "",
        typeCode: "", // 类型编码
      },
      rules: {
        typeCode: [
          { required: true, message: "请选择类型编码", trigger: "change" },
        ],
        contentKey: [{ required: true, message: "请输入键", trigger: "blur" }],
        contentValue: [
          { required: true, message: "请输入值", trigger: "blur" },
        ],
      },
      typeCodeData: [],
    }
  },
  props: {
    btnType: {
      type: Number,
    },
    row: {
      type: Object,
    },
  },
  methods: {
    // 查询码值类型列表
    queryCodeValueTypeList() {
      queryCodeValueType({
        // 查询数据
        typeCode: "", // 类型编码
        typeName: "", // 类型名称
        pageNum: 1,
        pageSize: 9999,
      }).then((res) => {
        if (res.status !== 0) {
          this.$message({
            type: "error",
            message: res.msg,
          })
          return
        }
        this.typeCodeData = res.data.list
      })
    },
    // 处理dialog打开时
    handlerOpen() {
      this.queryCodeValueTypeList()
      if (this.btnType === 0) {
        this.formData = this.row
      }
    },
    // 校验
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.addOrEditCodeValContent()
        } else {
          return false
        }
      })
    },
    // 新增或编辑码值内容
    addOrEditCodeValContent() {
      if (this.btnType) {
        this.addCodeValueContent()
      } else {
        this.updateCodeValueContent()
      }
    },
    // 新增码值内容
    addCodeValueContent() {
      addCodeValueContent(this.formData).then((res) => {
        if (res.status !== 0) {
          this.$message({
            message: res.msg,
            type: "error",
          })
          return
        }
        this.$message({
          message: "新增成功!",
          type: "success",
        })
        this.$parent.queryCodeValueContentList()
        this.dialogFormVisible = false
      })
    },
    // 更新码值内容
    updateCodeValueContent() {
      updateCodeValueContent(this.formData).then((res) => {
        if (res.status !== 0) {
          this.$message({
            message: res.msg,
            type: "error",
          })
          return
        }
        this.$message({
          message: "更新成功!",
          type: "success",
        })
        this.$parent.queryCodeValueContentList()
        this.dialogFormVisible = false
      })
    },
    // 处理dialog关闭前
    handlerClose() {
      this.formData = {}
      this.$refs["ruleForm"].resetFields()
    },
  },
}
</script>

<style lang="scss" scoped>
.add-code-value-type {
  display: inline-block;
  margin-right: 10px;
  .dialog-footer {
    position: relative;
  }
}
.el-form {
  margin-right: 10px;
  margin-left: -20px;
}
</style>
