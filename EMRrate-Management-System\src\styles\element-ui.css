@charset "UTF-8";
.el-upload input[type="file"] {
  display: none !important;
}

.el-upload__input {
  display: none;
}

.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

.upload-container .el-upload {
  width: 100%;
}

.upload-container .el-upload .el-upload-dragger {
  width: 100%;
  height: 200px;
}

.el-dropdown-menu a {
  display: block;
}

.el-range-separator {
  box-sizing: content-box;
}

.el-form--label-top .el-form-item__label {
  padding: 0px;
  font-size: 13px;
}

.el-button--primary {
  background: #1b88c0;
  border-color: #1b88c0;
  color: #fff;
}

.el-button--primary:hover, .el-button--primary:focus {
  background-color: rgba(27, 136, 192, 0.8);
  border-color: rgba(27, 136, 192, 0.8);
  color: #fff;
}

.el-button--secondary {
  background: #6bcfc0;
  border-color: #6bcfc0;
  color: #fff;
}

.el-button--secondary:hover, .el-button--secondary:focus {
  background-color: rgba(107, 207, 192, 0.8);
  border-color: rgba(107, 207, 192, 0.8);
  color: #fff;
}

.el-autocomplete {
  width: 240px;
}

.el-autocomplete-suggestion.el-popper {
  width: 400px !important;
}

.el-button--third {
  background: #f3b255;
  border-color: #f3b255;
  color: #fff;
}

.el-button--third:hover, .el-button--third:focus {
  background-color: rgba(243, 178, 85, 0.8);
  border-color: rgba(243, 178, 85, 0.8);
  color: #fff;
}

.el-breadcrumb {
  font-size: 16px;
}

.cell {
  text-align: center;
}

/**修改全局的滚动条*/
/**滚动条的宽度*/
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-thumb {
  background-color: #e4e7ed;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #c1c5cc;
}

.el-table__body-wrapper::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #e4e7ed;
  border-radius: 10px;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
  cursor: pointer !important;
  background-color: #c1c5cc;
}

.scrollbar-wrapper {
  margin-bottom: 0 !important;
}

.el-dialog {
  border-radius: 5px;
}

.el-dialog .el-dialog__header {
  color: white;
  background-color: #369cc4;
  padding-top: 15px;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  line-height: 20px;
}

.el-dialog .el-dialog__header .el-dialog__headerbtn {
  top: 15px;
}

.el-dialog .el-dialog__header .el-dialog__headerbtn .el-dialog__close {
  color: white;
  font-size: 20px;
}

.el-dialog .el-dialog__header .el-dialog__title {
  font-size: 16px;
  color: white;
}

.el-dialog .el-dialog__body {
  padding: 15px 10px;
}

.has-gutter tr th {
  background-color: #f0f0f0;
}

.el-divider {
  margin: 16px 0px;
}
