<template>
  <div class="test-logo">
    <!-- 测试标志 -->
    <div v-if="this.testFlag === 'Y'" class="test">
      <div>测试版</div>
      <div>test version</div>
    </div>
  </div>
</template>

<script>
import { queryAllSysConfig } from "@/api/sys-config"
export default {
  name: "App",
  data() {
    return {
      testFlag: "",
    }
  },
  created() {
    this.queryAllSysConfig()
  },
  methods: {
    // 获取规则配置列表
    async queryAllSysConfig() {
      let res = await queryAllSysConfig()
      if (res.status !== 0) {
        this.$message({
          type: "error",
          message: res.msg,
        })
        return
      } else {
        for (let key in res.data) {
          if (key === "config.system.test.flag") {
            this.testFlag = res.data[key]
          }
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.test-logo {
  .test {
    position: fixed;
    border: 4px solid rgba(255, 0, 0, .6);
    z-index: 999;
    width: 90px;
    height: 90px;
    right: 400px;
    top: 6px;
    border-radius: 100%;
    text-align: center;
    font-weight: 700;
    color: rgba(255, 0, 0, .6);
    transform: rotate(-30deg);
    div:first-child {
      margin-top: 26px;
      font-size: 18px;
    }
    div:last-child {
      font-size: 12px;
    }
  }
}
</style>
