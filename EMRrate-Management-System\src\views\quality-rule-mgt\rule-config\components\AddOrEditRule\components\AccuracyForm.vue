<template>
  <div>
    <template v-if="formData.checkRuleType === 'WYX'">
      <!-- 唯一性 -->
      <el-form-item label="检测表或视图" prop="checkRuleTableOrView">
        <el-autocomplete
          ref="abc"
          v-model="formData.checkRuleTableOrView"
          :fetch-suggestions="querySearchAsync"
          value-key="tableOrViewName"
          @select="handlerTableOrViewSelect"
          placeholder="请输入内容"
          clearable
        ></el-autocomplete>
      </el-form-item>
      <el-form-item label="检测字段" prop="checkRuleColumn">
        <el-input
          :disabled="isDisabled"
          @click.native="handlerclick"
          v-model="formData.checkRuleColumn"
        >
          <el-button slot="append">
            <SelectFieldList
              type="checkRuleColumn"
              selectType="checkBox"
              @click.native.stop="handlerclick"
              :isDisabled.sync="isDisabled"
              :checkRuleColumnData="checkRuleColumnData"
              @backfillSelectedData="backfillSelectedData"
            />
          </el-button>
        </el-input>
      </el-form-item>
      <el-form-item label="定义WHERE条件" prop="checkRuleWhere">
        <el-input type="textarea" v-model="formData.checkRuleWhere"></el-input>
      </el-form-item>

      <el-form-item label="问题明细显示字段" prop="pbSubsidiaryColumns">
        <el-input
          :disabled="isDisabled"
          @click.native="handlerclick"
          v-model="formData.pbSubsidiaryColumns"
        >
          <el-button slot="append">
            <SelectFieldList
              type="pbSubsidiaryColumns"
              selectType="checkBox"
              @click.native.stop="handlerclick"
              :isDisabled.sync="isDisabled"
              :checkRuleColumnData="checkRuleColumnData"
              @backfillSelectedData="backfillSelectedData"
            />
          </el-button>
        </el-input>
      </el-form-item>
      <slot></slot>
    </template>

    <!-- 计算正确性 -->
    <template v-if="formData.checkRuleType === 'JSZQX'">
      <el-form-item label="检测表或视图" prop="checkRuleTableOrView">
        <el-autocomplete
          ref="abc"
          v-model="formData.checkRuleTableOrView"
          :fetch-suggestions="querySearchAsync"
          value-key="tableOrViewName"
          @select="handlerTableOrViewSelect"
          placeholder="请输入内容"
          clearable
        ></el-autocomplete>
      </el-form-item>
      <el-form-item label="统计表字段" prop="checkRuleColumn">
        <el-input
          :disabled="isDisabled"
          @click.native="handlerclick"
          v-model="formData.checkRuleColumn"
        >
          <el-button slot="append">
            <SelectFieldList
              type="checkRuleColumn"
              selectType="radio"
              @click.native.stop="handlerclick"
              :isDisabled.sync="isDisabled"
              :checkRuleColumnData="checkRuleColumnData"
              @backfillSelectedData="backfillSelectedData"
            />
          </el-button>
        </el-input>
      </el-form-item>
      <el-form-item label="明细表字段1" prop="stCheckRuleColumn">
        <el-input
          :disabled="isDisabled"
          @click.native="handlerclick"
          v-model="formData.stCheckRuleColumn"
        >
          <el-button slot="append">
            <SelectFieldList
              type="stCheckRuleColumn"
              selectType="radio"
              @click.native.stop="handlerclick"
              :isDisabled.sync="isDisabled"
              :checkRuleColumnData="checkRuleColumnData"
              @backfillSelectedData="backfillSelectedData"
            />
          </el-button>
        </el-input>
      </el-form-item>
      <el-form-item label="四则运算符" prop="calcRuleCd">
        <el-radio-group v-model="formData.calcRuleCd">
          <el-radio label="1"
            ><b style="font-size: 16px">{{ "+" }}</b>
          </el-radio>
          <el-radio label="2"
            ><b style="font-size: 16px">{{ "-" }}</b>
          </el-radio>
          <el-radio label="3"
            ><b style="font-size: 16px">{{ "*" }}</b>
          </el-radio>
          <el-radio label="4"
            ><b style="font-size: 16px">{{ "/" }}</b>
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="明细表字段2" prop="cdValFieldNm">
        <el-input
          :disabled="isDisabled"
          @click.native="handlerclick"
          v-model="formData.cdValFieldNm"
        >
          <el-button slot="append">
            <SelectFieldList
              type="cdValFieldNm"
              selectType="radio"
              @click.native.stop="handlerclick"
              :isDisabled.sync="isDisabled"
              :checkRuleColumnData="checkRuleColumnData"
              @backfillSelectedData="backfillSelectedData"
            />
          </el-button>
        </el-input>
      </el-form-item>
      <el-form-item label="明细表WHERE条件" prop="checkRuleWhere">
        <el-input type="textarea" v-model="formData.checkRuleWhere"></el-input>
      </el-form-item>
      <el-form-item label="问题明细显示字段" prop="pbSubsidiaryColumns">
        <el-input
          :disabled="isDisabled"
          @click.native="handlerclick"
          v-model="formData.pbSubsidiaryColumns"
        >
          <el-button slot="append">
            <SelectFieldList
              type="pbSubsidiaryColumns"
              selectType="checkBox"
              @click.native.stop="handlerclick"
              :isDisabled.sync="isDisabled"
              :checkRuleColumnData="checkRuleColumnData"
              @backfillSelectedData="backfillSelectedData"
            />
          </el-button>
        </el-input>
      </el-form-item>
      <slot></slot>
    </template>
    <!-- 自定义准确性 -->
    <template v-if="formData.checkRuleType === 'ZDYZQX'">
      <CustomCheckRuleType :formData.sync="formData" />
    </template>
  </div>
</template>


<script>
import tableViewAndField from "@/mixins/tableViewAndField"
import CustomCheckRuleType from "@/components/CustomCheckRuleType/index.vue"
import SelectFieldList from "./common/SelectFieldList.vue"
export default {
  components: {
    CustomCheckRuleType,
    SelectFieldList,
  },
  mixins: [tableViewAndField],
  props: {
    formData: {
      type: Object,
    },
    dataSourceId: {
      type: Number,
    },
  },
  mounted() {
    if (this.formData.checkRuleTableOrView) {
      this.queryCheckRuleColumnData.tableName =
        this.formData.checkRuleTableOrView
      this.queryCheckRuleColumnData.dataSourceId = this.dataSourceId
      this.getCheckRuleColumnData()
    }
  },
  watch: {
    formData: {
      handler(val) {
        // 只要表单有值变化就清空获取的SQL数据
        this.$emit("clearCheckSQLData")
        if (val.checkRuleTableOrView) {
          this.isDisabled = false
        } else {
          this.clearPartData()
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    //handlerClear
    clearPartData() {
      this.isDisabled = true
      this.checkRuleColumnData = []
      this.formData.checkRuleColumn = ""
      this.formData.stCheckRuleColumn = ""
      this.formData.cdValFieldNm = ""
      this.formData.pbSubsidiaryColumns = ""
    },
    // 选择字段后回填至输入框
    backfillSelectedData(val, type) {
      if (type === "checkRuleColumn") {
        this.formData.checkRuleColumn = val.join(",")
      } else if (type === "pbSubsidiaryColumns") {
        this.formData.pbSubsidiaryColumns = val.join(",")
      } else if (type === "stCheckRuleColumn") {
        this.formData.stCheckRuleColumn = val.join(",")
      } else if (type === "cdValFieldNm") {
        this.formData.cdValFieldNm = val.join(",")
      }
    },
    // 先选择表或视图
    handlerclick() {
      if (this.isDisabled) {
        this.$message({
          type: "warning",
          message: "请先选择检测表或视图内容",
        })
        this.$refs["abc"].focus()
      }
    },
    // 当当前系统和数据库的表选择时
    handlerTableOrViewSelect(val) {
      this.isDisabled = false
      this.queryCheckRuleColumnData.tableName = val.tableOrViewName
      this.queryCheckRuleColumnData.dataSourceId = this.dataSourceId
      this.getCheckRuleColumnData()
    },
  },
}
</script>

<style lang="scss" scoped>
.selected {
  margin-bottom: 20px;
}
</style>
