// sidebar
$menuText:#ffffff;
$menuActiveText:#ffffff;
$subMenuActiveText:#ffffff; //https://github.com/ElemeFE/element/issues/12951

$menuBg:transparent;
$menuHover:transparent;

$subMenuBg:transparent;
$subMenuHover:rgba(255,255,255,0.1);

$sideBarWidth: 210px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}
