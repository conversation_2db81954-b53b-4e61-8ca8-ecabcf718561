<template>
  <MainCard>
    <div class="app-container">
      <!-- <el-tabs
        v-model="activeName"
        type="card"> -->
        <!-- <el-tab-pane
          label="基本配置"
          name="first"> -->
          <div
            v-if="isLoading === true"
            class="loading"
            v-loading="isLoading"></div>
          <el-form
            ref="form"
            label-width="220px"
            class="system-setting-form"
            size="small">
            <h3>系统图标：</h3>
            <el-form-item
              size="small"
              label="系统封面LOGO">
              <div class="flexv">
                <img
                  class="loginImg"
                  v-if="loginImg"
                  :src="loginImg"
                  alt="" />
                <el-button
                  v-if="!loginImg"
                  class="ebutton"
                  type="primary"
                  @click="openCropper('loginLogo')"
                  >选择图片</el-button
                >
                <el-button
                  v-else
                  class="ebutton"
                  type="primary"
                  @click="openCropper('loginLogo')"
                  >重新选择</el-button
                >
              </div>
            </el-form-item>
            <el-form-item
              size="small"
              label="系统侧边栏LOGO">
              <div class="flexv">
                <img
                  class="sideImg"
                  v-if="sideImg"
                  :src="sideImg"
                  alt="" />
                <el-button
                  v-if="!sideImg"
                  class="ebutton"
                  type="primary"
                  @click="openCropper('sideLogo')"
                  >选择图片</el-button
                >
                <el-button
                  v-else
                  class="ebutton"
                  type="primary"
                  @click="openCropper('sideLogo')"
                  >重新选择</el-button
                >
              </div>
            </el-form-item>
            <div
              v-for="part in systemArr"
              :key="part.title"
              class="module">
              <h3>{{ part.title }}：</h3>
              <el-form-item
                size="small"
                v-for="item in part.content"
                :key="item.id"
                :label="item.configName">
                <!-- 目前只支持开关和输入框 -->
                <template v-if="item.configType == '1'">
                  <el-input
                    v-model="item.configValue"
                    style="width: 250px"></el-input>
                </template>

                <template v-if="item.configType == '2'">
                  <el-switch
                    style="width: 55px"
                    v-model="item.configValue"
                    active-value="Y"
                    inactive-value="N"></el-switch>
                </template>
                <template v-if="item.configType == '3'">
                  <el-input
                    v-model="item.configValue"
                    style="width: 55px"></el-input>
                </template>

                <span class="description">提示：{{ item.description }}</span>
              </el-form-item>
            </div>
            <el-dialog
              title="图片选择"
              :visible.sync="dialogVisible"
              width="1000px">
              <cropper-image
                v-if="logoType === 'loginLogo'"
                @uploadImgSuccess="handleUploadSuccess"
                :logoType="logoType"
                ref="child">
              </cropper-image>
            </el-dialog>
            <el-dialog
              title="图片选择"
              :visible.sync="dialogVisible1"
              width="1000px">
              <cropper-image
                v-if="logoType === 'sideLogo'"
                @uploadImgSuccess="handleUploadSuccess"
                :logoType="logoType"
                ref="child">
              </cropper-image>
            </el-dialog>
            <el-form-item
              style="margin-top: 50px"
              v-if="isLoading === false">
              <el-button
                type="primary"
                @click="onSubmit"
                >保存</el-button
              >
              <el-button @click="reset">取消</el-button>
            </el-form-item>
          </el-form>
        <!-- </el-tab-pane> -->
        <!-- <el-tab-pane
          label="授权"
          name="second">
          <Authorization />
        </el-tab-pane> -->
      <!-- </el-tabs> -->
    </div>
  </MainCard>
</template>
<script>
import {
  querySysConfig as _querySysConfig,
  updateSysConfigs as _updateSysConfigs,
  getHospitalLogoPath as _getHospitalLogoPath
} from '@/api/system-config/systemConfig'
import CropperImage from './components/CropperImage.vue'
import Authorization from './components/Authorization.vue'
import autoLogout from '@/mixins/autoLogout'
export default {
  components: {
    Authorization,
    CropperImage
  },
  mixins: [autoLogout],
  data() {
    return {
      activeName: 'first',
      systemArr: [],
      loginImg: '',
      dialogVisible: false,
      dialogVisible1: false,
      sideImg: '',
      logoType: '',
      isLoading: false
    }
  },
  mounted() {
    _getHospitalLogoPath({ logoType: 'system.logo.hospitalImg1' }).then(
      (res) => {
        this.loginImg = res.data
      }
    )
    _getHospitalLogoPath({ logoType: 'system.logo.hospitalImg2' }).then(
      (res) => {
        this.sideImg = res.data
      }
    )
    this.querySysConfig()
  },
  methods: {
    querySysConfig() {
      this.isLoading = true
      return _querySysConfig().then((res) => {
        if (res && res.status == 0) {
          this.systemArr = Object.keys(res.data).map((key) => {
            return {
              title: res.data[key][0].moduleName,
              content: res.data[key]
            }
          })
        }
        this.isLoading = false
      })
    },
    reset() {
      this.querySysConfig()
    },
    onSubmit() {
      let data = []
      this.systemArr.forEach((item) => {
        if (item.content && item.content.length) {
          data = data.concat(item.content)
        }
      })
      _updateSysConfigs(data).then((res) => {
        if (res && res.status == 0) {
          this.querySysConfig()
          this.$message.success('更新成功！')
          // 计时自动登出
          this.enableAutoLogout()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    handleUploadSuccess(data) {
      this.dialogVisible = false
      this.$router.go(0)
    },
    openCropper(data) {
      if (data == 'loginLogo') {
        this.dialogVisible = true
      } else {
        this.dialogVisible1 = true
      }
      this.logoType = data
    }
  }
}
</script>
<style lang="scss" scoped>
.app-container {
  position: relative;
  .loading {
    position: absolute;
    width: 100%;
    height: 660px;
    z-index: 1000;
  }
}
.system-setting-form {
  display: table;
  margin: 0 auto;
  padding: 15px;
  //   min-width: 50%;
  .module {
    .description {
      color: rgba(0, 0, 0, 0.5);
      font-size: 14px;
      margin-left: 50px;
    }
  }
}
.flexv {
  width: 300px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.loginImg {
  width: 200px;
  padding: 10px;
  background-color: #ebf0f7;
  border: 1px dashed #9dc5f3;
  border-radius: 6px;
}
.sideImg {
  width: 200px;
  padding: 10px;
  background-color: #ebf0f7;
  border: 1px dashed #9dc5f3;
  box-sizing: border-box;
  border-radius: 6px;
}
</style>
