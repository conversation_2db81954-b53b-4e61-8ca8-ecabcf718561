<template>
  <div class="add-code-value-type">
    <el-dialog v-dialogDrag
      :title="btnType ? '新增码值类型' : '编辑码值类型'"
      :visible.sync="dialogFormVisible"
      @open="handlerOpen"
      @closed="handlerClose"
      width="30%"
      :close-on-click-modal="false"
    >
      <el-form
        :model="formData"
        :rules="rules"
        :label-width="formLabelWidth"
        ref="ruleForm"
      >
        <el-form-item label="类型编码" prop="typeCode">
          <el-input v-model="formData.typeCode"></el-input>
        </el-form-item>
        <el-form-item label="类型名称" prop="typeName">
          <el-input v-model="formData.typeName"></el-input>
        </el-form-item>
        <el-form-item label="类型描述" prop="typeDesc">
          <el-input v-model="formData.typeDesc"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm('ruleForm')"
          >保存</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addCodeValueType,
  updateCodeValueType,
} from "@/api/codeValueMgt/codeValueType"
export default {
  data() {
    return {
      dialogFormVisible: false, // 弹框状态
      formLabelWidth: "120px",
      isShowLoading: false, // 是否显示loading
      formData: {
        typeCode: "",
        typeDesc: "",
        typeName: "",
      },
      rules: {
        typeCode: [
          { required: true, message: "请输入类型编码", trigger: "blur" },
        ],
        typeName: [
          { required: true, message: "请输入类型名称", trigger: "blur" },
        ],
      },
    }
  },
  props: {
    btnType: {
      type: Number,
    },
    row: {
      type: Object,
    },
  },
  methods: {
    // 处理dialog打开时
    handlerOpen() {
      if (this.btnType == 0) {
        this.formData = this.row
      }
    },
    // 校验
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.addOrEditCodeValType()
        } else {
          return false
        }
      })
    },
    // 新增或编辑码值内容
    addOrEditCodeValType() {
      if (this.btnType) {
        this.addCodeValueType()
      } else {
        this.updateCodeValueType()
      }
    },
    // 新增码值类型
    addCodeValueType() {
      addCodeValueType(this.formData).then((res) => {
        if (res.status == 0) {
          this.$message({
            message: "新增成功!",
            type: "success",
          })
          this.$parent.queryCodeValueTypeList()
        } else {
          this.$message({
            message: res.msg,
            type: "error",
          })
        }
        this.dialogFormVisible = false
      })
    },
    // 更新码值类型
    updateCodeValueType() {
      updateCodeValueType(this.formData).then((res) => {
        if (res.status == 0) {
          this.$message({
            message: "更新成功!",
            type: "success",
          })
          this.$parent.queryCodeValueTypeList()
        } else {
          this.$message({
            message: "更新失败！",
            type: "error",
          })
        }
        this.dialogFormVisible = false
      })
    },
    // 处理dialog关闭前
    handlerClose() {
      this.formData = {}
      this.$refs["ruleForm"].resetFields()
    },
  },
}
</script>

<style lang="scss" scoped>
.add-code-value-type {
  display: inline-block;
  margin-right: 10px;
  .dialog-footer {
    position: relative;
  }
}
.el-form {
  margin-right: 10px;
  margin-left: -20px;
}
</style>
