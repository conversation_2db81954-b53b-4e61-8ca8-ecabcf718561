// v-dialogDrag: 弹窗拖拽
import Vue from "vue"
let draging = false
let dragDom
let dragpoint
Vue.directive("dialogDrag", {
  bind(el, binding, vnode, oldVnode) {
    let dialogHeaderEl = el.querySelector(".el-dialog__header")
    dialogHeaderEl.style.cssText += ';cursor:move;'
    dialogHeaderEl.addEventListener("mousedown", (ev) => {
      let target = ev.target
      //由于点击关闭按钮会事件冒泡，取消拖拽
      if (target.classList.contains("el-dialog__close")) {
        return
      }
      draging = true
      dragDom = el.querySelector(".el-dialog")
      //自定义样式，让弹窗在拖拽过程中鼠标指针变成十字移动
      dragDom?.classList.add("draging")
      dragpoint = {
        x: ev.clientX,
        y: ev.clientY,
      }
    })
  },
})
document.addEventListener("mouseup", (ev) => {
  draging = false
  dragDom?.classList.remove("draging")
  dragDom = null
})
document.addEventListener("mousemove", (ev) => {
  if (draging) {
    let _dragdom = dragDom
    let sty = window.getComputedStyle(_dragdom, null)
    _dragdom.style.marginLeft = `${
      parseFloat(sty.marginLeft) + ev.clientX - dragpoint.x
    }px`
    _dragdom.style.marginTop = `${
      parseFloat(sty.marginTop) + ev.clientY - dragpoint.y
    }px`
    dragpoint = {
      x: ev.clientX,
      y: ev.clientY,
    }
  }
})
