<template>
  <el-dialog
    v-dialogDrag
    :visible.sync="dialogFormVisible"
    @open="handlerOpen"
    @closed="handlerClose"
    :close-on-click-modal="false"
    width="800px"
    :show-close="false"
  >
    <div class="dialog-left">
      <div class="dialog-new-title">新增评价等级</div>
      <p class="dialog-explain">
        按照国家卫生健康委统一要求，二级以上医院要按时参加电子病历系统功能应用水平分级评价。评价标准中将电子病历系统应用水平划分为9个等级。每一等级的标准包括电子病历各个局部系统的要求和对医疗机构整体电子病历系统的要求。
      </p>
      <p class="dialog-reminder">
        <i class="el-icon-warning-outline"></i> tips:等级设置3级以上，最高8级
      </p>
      <div class="dialog-img">
        <img src="../../../../assets/emrimg/dengjijiangbei.png" alt="" />
      </div>
    </div>
    <div class="dialog-right">
      <div class="dialog-new-title">
        <i class="el-icon-close" @click="handlerClose()"></i>
      </div>
      <el-form
        :model="formData"
        :rules="rules"
        label-width="90px"
        ref="ruleForm"
      >
        <el-form-item label="等级名称" prop="levelName">
          <el-input v-model="formData.levelName"></el-input>
        </el-form-item>
        <el-form-item label="等级编码" prop="levelCode">
          <el-input v-model="formData.levelCode"></el-input>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-checkbox v-model="isclose">保存并添加下一等级</el-checkbox>
        <span class="dialog-footer-button">
          <el-button size="mini" @click="dialogFormVisible = false"
            >取 消</el-button
          >
          <el-button size="mini" type="primary" @click="submitForm('ruleForm')"
            >保存</el-button
          ></span
        >
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { addlevelDictionary } from "@/api/document-management/dictionary-configuration";
export default {
  data() {
    return {
      dialogFormVisible: false, // 弹框状态
      isclose: false, //保存并添加下一等级状态
      // 表单数据
      formData: {
        levelCode: "",
        levelName: "",
      },
      // 表单验证规则
      rules: {
        levelCode: [
          { required: true, message: "请输入等级名称", trigger: "blur" },
        ],
        levelName: [
          { required: true, message: "请输入等级编码", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    // 处理dialog打开时
    async handlerOpen() {
      this.formData = { levelCode: "", levelName: "" };
    },

    // 校验
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          addlevelDictionary(this.formData).then((res) => {
            if (res.status !== 0) {
              this.$message({
                message: res.msg,
                type: "error",
              });
              return;
            } else {
              this.$message({
                message: "新增等级字典成功",
                type: "success",
              });
              if (this.isclose === true) {
                console.log("11");
                this.formData = { levelCode: "", levelName: "" };
                this.$parent.$parent.getlevelDictionary();
              } else if (this.isclose === false) {
                this.$parent.$parent.getlevelDictionary();
                this.handlerClose();
              }
            }
          });
        } else {
          return false;
        }
      });
    },
    // 处理dialog关闭后
    handlerClose() {
      this.formData = { levelCode: "", levelName: "" };
      this.dialogFormVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/emr-styles/emr-dialog.scss";
::v-deep .el-dialog__body {
  display: flex;
  height: 100%;
  padding: 0px;
  .dialog-left {
    flex: 3;
    height: 100%;
    border-right: 1px solid #e0e2e4;
    padding: 20px;
    .dialog-explain {
      font-size: 14px;
      color: #555555;
      line-height: 29px;
      margin-top: 2vh;
      margin-left: 20px;
    }
    .dialog-reminder {
      font-size: 14px;
      line-height: 29px;
      margin-top: 1vh;
      margin-left: 20px;
      color: #ef5050;
    }
    .dialog-img {
      text-align: right;
    }
  }
  .dialog-right {
    flex: 4;
    padding: 20px;
    position: relative;
    .dialog-new-title {
      text-align: right;
      i {
        cursor: pointer;
      }
    }
    .el-form {
      margin-top: 2vh;
    }
    .dialog-footer {
      position: absolute;
      width: 100%;
      bottom: 30px;
      .dialog-footer-button {
        position: absolute;
        right: 30px;
        bottom: 0px;
      }
    }
  }
}

</style>
