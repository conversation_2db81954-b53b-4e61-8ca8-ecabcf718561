<template>
  <div class="add-edit-data-source">
    <el-dialog
      v-dialogDrag
      :title="btnType ? '新增数据源' : '编辑数据源'"
      :visible.sync="dialogFormVisible"
      @open="handlerOpen"
      @closed="handlerClose"
      :close-on-click-modal="false"
      width="800px">
      <el-form
        :model="formData"
        :rules="rules"
        :label-width="formLabelWidth"
        ref="ruleForm">
        <el-form-item
          style="width: 70%"
          label="数据源名称"
          prop="dataSourceName">
          <el-input
            v-model="formData.dataSourceName"
            @blur="handlerBlur()"></el-input>
        </el-form-item>
        <el-form-item
          label="描述"
          prop="dataSourceDescribe">
          <el-input v-model="formData.dataSourceDescribe"></el-input>
        </el-form-item>
        <el-form-item
          style="width: 70%"
          label="采集系统代码"
          prop="sysCode">
          <el-input v-model="formData.sysCode"></el-input>
        </el-form-item>
        <el-form-item
          label="采集系统名称"
          style="width: 70%"
          prop="sysName">
          <el-input v-model="formData.sysName"></el-input>
        </el-form-item>
        <div class="database-info-box">
          <el-divider content-position="left">采集数据库信息</el-divider>
          <el-form-item
            label="采集数据库名称"
            style="width: 70%"
            prop="databaseName">
            <el-input v-model="formData.databaseName"></el-input>
          </el-form-item>
          <el-form-item
            label="模式(SCHEMA)"
            style="width: 70%"
            prop="databaseSchema">
            <el-input v-model="formData.databaseSchema"></el-input>
          </el-form-item>
          <el-form-item
            label="数据库类型"
            prop="databaseType">
            <el-select
              style="width: 50%"
              v-model="formData.databaseType"
              @change="handlerChange">
              <el-option
                v-for="item in databaseTypeData"
                :key="item.id"
                :label="item.contentValue"
                :value="item.contentKey"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="跨库查询"
            prop="needCrossQuery">
            <el-switch
              v-model="formData.needCrossQuery"
              active-color="#5270DD"
              :active-value="1"
              :inactive-value="0"
              :active-text="'是'"
              :inactive-text="'否'">
            </el-switch>
          </el-form-item>

          <div class="test-database-part">
            <el-form-item
              label="连接数据库"
              required>
              <div class="test-form">
                <el-form-item
                  label-width="80px"
                  label="URL"
                  prop="databaseUrl">
                  <el-input v-model="formData.databaseUrl"></el-input>
                </el-form-item>
                <el-form-item
                  label-width="80px"
                  label="用户名"
                  prop="databaseUser">
                  <el-input v-model="formData.databaseUser"></el-input>
                </el-form-item>
                <el-form-item
                  label-width="80px"
                  label="密码"
                  prop="databasePwd">
                  <el-input
                    type="password"
                    auto-complete="new-password"
                    v-model="formData.databasePwd"
                    show-password></el-input>
                </el-form-item>
                <div class="test-btn">
                  <span
                    v-if="isShow"
                    v-loading="isShowLoading"
                    style="padding-right: 10px">
                    <span
                      v-if="isPassTheTest"
                      class="el-icon-circle-check"
                      style="color: green"
                      >测试通过</span
                    >
                    <span
                      v-else
                      class="el-icon-circle-close"
                      style="color: red"
                      >测试不通过</span
                    >
                  </span>
                  <el-button
                    type="primary"
                    plain
                    :disabled="isDisabled"
                    @click="satrtTestDataSource"
                    >测试连接数据源</el-button
                  >
                </div>
              </div>
            </el-form-item>
          </div>

          <el-form-item
            label="数据库驱动"
            prop="databaseDriver">
            <el-input v-model="formData.databaseDriver"></el-input>
          </el-form-item>
          <el-form-item
            label="驱动包路径"
            prop="driverFiles">
            <el-input v-model="formData.driverFiles"></el-input>
          </el-form-item>
          <el-form-item label="驱动文件"
            ><el-upload
              class="upload-demo"
              ref="upload"
              :action="`${baseURL}/datasource/uploadDriverFile`"
              :on-success="handleSuccess"
              :auto-upload="true">
              <el-button
                slot="trigger"
                plain
                size="small"
                type="primary"
                >上传驱动文件</el-button
              >
            </el-upload>
          </el-form-item>
          <el-form-item
            label="测试语句"
            prop="testsql">
            <el-input v-model="formData.testsql"></el-input>
          </el-form-item>
        </div>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="submitForm('ruleForm')"
          >保存</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addDataSource,
  testDataSource,
  updateDataSource
} from '@/api/dataSourceStructureMgt/dataSourceMgt'
import getCodeValueConten from '@/mixins/getCodeValueContent'
function defaultForm() {
  return {
    // 表单数据
    createTime: '',
    dataSourceDescribe: '',
    dataSourceId: '',
    dataSourceName: '',
    databaseDriver: '',
    databaseName: '',
    databasePwd: '',
    databaseSchema: '',
    databaseType: '',
    databaseUrl: '',
    databaseUser: '',
    driverFiles: '',
    operationPerson: '',
    sysCode: '',
    sysName: '',
    testsql: '',
    updateTime: '',
    needCrossQuery: 0
  }
}
export default {
  mixins: [getCodeValueConten],
  data() {
    return {
      dialogFormVisible: false, // 弹框状态
      formLabelWidth: '120px',
      isShow: false, // 是否显示测试提示
      isPassTheTest: true, // 是否测试通过
      isShowLoading: false, // 是否显示loading
      formData: defaultForm(),
      rules: {
        dataSourceName: [
          { required: true, message: '请输入数据源名称', trigger: 'blur' }
        ],
        databaseName: [
          { required: true, message: '请输入采集数据库名称', trigger: 'blur' }
        ],
        databaseSchema: [
          { required: true, message: '请输入SCHEMA', trigger: 'blur' }
        ],
        databaseType: [
          { required: true, message: '请选择数据库类型', trigger: 'change' }
        ],
        databaseUrl: [
          { required: true, message: '请输入数据库访问URL', trigger: 'blur' }
        ],
        databaseUser: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        databasePwd: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ],
        sysCode: [
          { required: true, message: '请输入采集系统代码', trigger: 'change' }
        ],
        sysName: [
          { required: true, message: '请输入采集系统名称', trigger: 'change' }
        ],
        testsql: [
          { required: true, message: '请输入测试语句', trigger: 'blur' }
        ]
      },
      databaseTypeData: [],
      isDisabled: false,
      baseURL: process.env.VUE_APP_BASE_API
    }
  },
  props: {
    btnType: {
      type: Number
    },
    row: {
      type: Object
    },
    databaseTypeData: {
      type: Array
    }
  },
  watch: {
    databaseTypeData: {
      handler(val) {
        if (val.length > 0) {
          this.isDisabled = false
          return
        }
        this.isDisabled = true
      }
    }
  },
  methods: {
    // 处理dialog打开时
    async handlerOpen() {
      if (this.btnType === 0) {
        this.formData = this.row
      }
    },
    // 当数据库类型选择时
    handlerChange(val) {
      const curItem = this.databaseTypeData.find(
        (item) => item.contentKey === val
      )
      this.$set(this.formData, 'databaseDriver', curItem.data1)
      this.$set(this.formData, 'testsql', curItem.data2)
      this.$set(this.formData, 'databaseUrl', curItem.data3)
    },
    // 新增时数据源名称输入失去焦点时
    handlerBlur() {
      if (this.btnType) {
        if (!this.formData.sysCode) {
          this.formData.sysCode = this.formData.dataSourceName
        }
        if (!this.formData.sysName) {
          this.formData.sysName = this.formData.dataSourceName
        }
      }
    },
    // 驱动文件上传成功时
    handleSuccess(res, file, fileList) {
      if (res.status === 0) {
        this.formData.driverFiles = file.name
        this.$message({
          message: '上传驱动文件成功!',
          type: 'success'
        })
      } else if (res.status === -1) {
        this.$message({
          message: res.msg,
          type: 'warning'
        })
      }
    },
    // 更新数据源
    updateDataSource() {
      updateDataSource(this.formData).then((res) => {
        if (res.status !== 0) {
          this.$message({
            message: res.msg,
            type: 'error'
          })
          return
        }
        this.$message({
          message: '更新数据源成功',
          type: 'success'
        })
        this.$parent.queryDataSourceList()
        this.dialogFormVisible = false
      })
    },
    // 新增数据源
    addDataSource() {
      addDataSource(this.formData).then((res) => {
        if (res.status !== 0) {
          this.$message({
            message: res.msg,
            type: 'error'
          })
          return
        }
        this.$message({
          message: '新增数据源成功',
          type: 'success'
        })
        this.$parent.queryDataSourceList()
        this.dialogFormVisible = false
      })
    },
    // 校验
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.testDataSource()
        } else {
          return false
        }
      })
    },
    // 保存前测试数据源
    async testDataSource() {
      const { status } = await testDataSource(this.formData)
      if (status !== 0) {
        this.$message({
          message: '测试数据源连接失败，请重新填写数据源',
          type: 'error'
        })
        return
      }
      if (this.btnType) {
        this.addDataSource()
        return
      }
      this.updateDataSource()
    },
    // 点击测试按钮测试数据源
    satrtTestDataSource() {
      this.$refs['ruleForm'].validate(async (valid) => {
        if (valid) {
          this.isShowLoading = true
          const { status, msg } = await testDataSource(this.formData)
          if (status == 0) {
            this.isShow = true
            this.isPassTheTest = true
            this.isShowLoading = false
            return
          }
          this.$message({
            message: msg,
            type: 'error'
          })
          this.isShow = true
          this.isPassTheTest = false
          this.isShowLoading = false
        } else {
          this.$message({
            type: 'warning',
            message: '请先完成必填项'
          })
          return false
        }
      })
    },
    // 处理dialog关闭后
    handlerClose() {
      this.formData = defaultForm()
      this.isShow = false
      this.$refs['ruleForm'].resetFields()
      this.$refs['upload'].clearFiles()
    }
  }
}
</script>

<style lang="scss" scoped>
.add-edit-data-source {
  display: inline-block;
  margin-right: 10px;
  .database-info-box {
    .el-divider {
      margin: 40px 0px;
    }
    .el-divider__text {
      color: #5270dd;
    }
  }
  .test-database-part {
    .test-form {
      background-color: #f5f5f5;
      padding: 20px 20px 10px 0;
      border-radius: 10px;
      .test-btn {
        text-align: right;
      }
    }
  }
  .dialog-footer {
    position: relative;
    .test-data-source {
      position: absolute;
      top: 0;
      bottom: 0;
    }
  }
}
.el-form {
  margin-right: 10px;
}
::v-deep .el-dialog .el-dialog__body {
  // 设置dialog的固定高度
  height: 60vh;
  overflow: auto;
}
</style>
