import { getTable, getTableField } from "@/api/qualityRuleMgt/ruleConfig"
const mixin = {
  data() {
    return {
      checkRuleTableOrViewData: [], // 当前系统和数据库的表列表
      checkRuleColumnData: [], // 表或主键表对应的字段

      queryCheckRuleColumnData: {
        // 查询表对应的字段
        // dataSourceId: "",
        fieldName: "",
        tableName: "",
        pageNum: "1",
        pageSize: "9999",
      },
      isDisabled: true,
    }
  },
  methods: {
    // 获取表对应的字段
    getCheckRuleColumnData() {
      getTableField(this.queryCheckRuleColumnData).then((res) => {
        if (res.status === 0) {
          let checkRuleColumnDataArr = res.data.list
          checkRuleColumnDataArr.forEach((element, index) => {
            element.id = index
          })
          this.checkRuleColumnData = checkRuleColumnDataArr
        }
      })
    },

    // 异步搜索 当前系统和数据库的表
    querySearchAsync(queryString, cb) {
      this.checkRuleTableOrViewData = []
      getTable({
        dataSourceId: this.row ? this.row.dataSourceId : this.dataSourceId,
        tableName: queryString,
      }).then((res) => {
        if (res.status !== 0) {
          this.$message({
            message: res.msg,
            type: "error",
          })
          return
        }
        res.data.forEach((element, index) => {
          this.checkRuleTableOrViewData.push({
            id: index,
            tableOrViewName: element,
          })
        })
        cb(this.checkRuleTableOrViewData)
      })
    },
  },
}
export default mixin
