<template>
  <MainCard>
    <div class="emr-container">
      <div class="emr-headline">
        <i></i>
        基础申报数据<span> — 医疗服务信息</span>
        <span
          class="aboutme"
          v-if="
            selectedProject.personInCharge.includes($store.state.user.loginId)
          "
        >
          是否和我相关
          <el-switch
            @change="queryDirectoryTreerule()"
            v-model="aboutme"
            active-color="#4969de"
            inactive-color="#aaa"
          >
          </el-switch>
        </span>
      </div>
      <div class="emr-container-main">
        <div class="source-mgt-table" v-loading="loading">
          <el-table
            class="emr-container-table"
            :data="tableData"
            ref="sourceMgtTable"
            v-loading="loading"
            :header-cell-style="{ background: '#fff', color: '#606266' }"
          >
            <el-table-column prop="levelName" label="项目名称" min-width="300">
              <template slot-scope="scope">
                <div
                  :style="`padding-left:${
                    scope.row.directoryCode.includes('.') ? 20 : 0
                  }px`"
                >
                  {{ scope.row.directoryCode }} {{ scope.row.directoryName }}
                  <svg-icon
                    v-show="scope.row.taskStatus === '2'"
                    icon-class="icon_success"
                    style="font-size: 24px"
                  ></svg-icon>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="levelName"
              label="数量（SQL）"
              min-width="100"
            >
              <template slot-scope="scope">
                <el-button
                  @click="$refs.Dataallocation.handlerOpenredact(scope.row)"
                  size="mini"
                  v-if="
                    scope.row.associatedType === '0' ||
                    scope.row.associatedType === '1'
                  "
                >
                  <svg-icon icon-class="icon_scale3" /> 打开</el-button
                >
                <span
                  class="shezhi"
                  v-else
                  @click="$refs.Dataallocation.handlerOpenredact(scope.row)"
                  >未配置 <i class="el-icon-arrow-right"></i
                ></span>
              </template>
            </el-table-column>
            <el-table-column prop="dataUnit" label="单位" min-width="100">
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="100">
              <template slot-scope="scope">
                <el-button @click="complete(scope.row)" type="text" size="small"
                  >完成</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <Dataallocation
      ref="Dataallocation"
      :selectedProject="selectedProject"
    ></Dataallocation>
  </MainCard>
</template>

<script>
import { queryDirectoryTreerule } from "@/api/document-management/catalogue-configuration";
import { markComplete } from "@/api/document-management/rule-configuration";
import Dataallocation from "./components/Dataallocation.vue";
export default {
  props: {
    selectedProject: {
      type: Object, // 根据实际情况调整类型
      required: true,
    },
  },
  components: {
    Dataallocation,
  },
  data() {
    return {
      tableData: [], // 表格数据
      loading: false,
      aboutme: true,
    };
  },
  watch: {
    selectedProject(newValue, oldValue) {
      this.queryDirectoryTreerule();
    },
  },
  created() {
    // 进入页面初始化查询
    this.queryDirectoryTreerule();
  },
  methods: {
    // 查询列表
    queryDirectoryTreerule() {
      this.loading = true;
      this.tableData = [];
      // 等级待修改
      queryDirectoryTreerule({
        configType: "1",
        needNotAllocationTask: false,
        levelCode: this.selectedProject.levelCode,
        projectId: this.selectedProject.id,
        userAccount: this.aboutme ? this.$store.state.user.loginId : "",
      }).then((res) => {
        if (res.status === 0) {
          this.tableData = res.data;
          this.loading = false;
        }
      });
    },
    complete(row) {
      markComplete({
        directoryCode: row.directoryCode,
        directoryName: row.directoryName,
        projectId: this.selectedProject.id,
        configType: 1,
        userAccount: this.$store.state.user.loginId,
      }).then((res) => {
        if (res.status === 0) {
          this.$message({
            message: "标记完成成功!",
            type: "success",
          });
          this.queryDirectoryTreerule();
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/emr-styles/emr-main-table.scss";

.aboutme {
  margin-left: 20px;
  color: #aaa;
  font-size: 14px;
}
</style>
