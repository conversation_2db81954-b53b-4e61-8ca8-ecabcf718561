<template>
  <div class="btn">
    <slot></slot>
    <span>{{ name }}</span>
  </div>
</template>

<script>
export default {
  props: ['name']
}
</script>

<style lang="scss" scoped>
.btn {
  cursor: pointer;
  display: inline-block;
  padding: 6px 16px;
  background: linear-gradient(to bottom, #fefefd, #ececec);
  border-radius: 6px;
  border: 1px solid #c3c7d0;
  &:hover {
    background: linear-gradient(to bottom, #ececec, #fefefd);
  }
}
</style>
