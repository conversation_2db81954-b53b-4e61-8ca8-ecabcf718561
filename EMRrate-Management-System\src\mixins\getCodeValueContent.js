import { queryCodeValueContent } from "@/api/codeValueMgt/codeValueContent"
const mixin = {
  methods: {
    // 获取码值内容
    async getCodeValueContent(dataName, param) {
      let { status, data } = await queryCodeValueContent({
        contentKey: "",
        typeCode: param,
      })
      if (status !== 0) {
        this.$message({
          message: res.msg,
          type: "error",
        })
        return
      }
      this[dataName] = data.list
    },
  },
}
export default mixin
