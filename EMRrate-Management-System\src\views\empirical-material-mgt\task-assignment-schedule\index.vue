<template>
  <MainCard>
    <div class="emr-container">
      <div class="tree-right">
        <div class="right-input">
          <svg-icon icon-class="dengji" /> 评价等级：
          <el-select
            @change="getgetUserListAndTasks(queryLeftTreelevelCode)"
            v-model="queryLeftTreelevelCode"
            placeholder="请选择"
            size="mini"
          >
            <el-option
              v-for="item in levelCodeData"
              :key="item.levelName"
              :label="item.levelName"
              :value="item.levelCode"
            >
            </el-option>
          </el-select>
        </div>
        <div class="right-statistics" v-show="savelist.userAccount === ''">
          <div class="statistics">
            <span
              >评价项目总数：<b>{{ numberobj.allNum }}</b></span
            >
            <span
              >未分配数：<b>{{
                numberobj.allNum - numberobj.allocated
              }}</b></span
            >
            <span
              >完成进度：<b
                ><span>{{ numberobj.finished }}</span
                >/{{ numberobj.allocated }}</b
              ><el-tooltip placement="top">
                <div slot="content">已完成数/已分配数=完成进度</div>
                <i class="el-icon-warning"></i> </el-tooltip
            ></span>
          </div>
          <el-progress
            type="circle"
            :width="50"
            :percentage="
              numberobj.finished > 0
                ? Number((numberobj.finished / numberobj.allocated).toFixed(2))*100
                : 0
            "
          ></el-progress>
        </div>
      </div>
      <div class="emr-container-main">
        <div class="emr-container-main-left">
          <div class="title">
            <b>成员列表</b>
            <el-tooltip
              class="item"
              effect="dark"
              content="添加成员"
              placement="top"
            >
              <el-button
                class="addicon"
                style="padding: 6px"
                icon="el-icon-plus"
                @click="$refs.Addpersonnel.handlerOpen(namedata)"
              ></el-button>
            </el-tooltip>
          </div>
          <div class="tree">
            <div
              class="treeitem"
              @click="handleNodeClick({ userAccount: '', userName: '' })"
              :style="{
                background: savelist.userAccount === '' ? '#EBEEFB' : '',
              }"
            >
              全部项目情况总览
            </div>
            <div
              class="treeitem2"
              v-for="item in namedata"
              :key="item.userAccount"
              @click="handleNodeClick(item)"
              :style="{
                background:
                  savelist.userAccount === item.userAccount ? '#EBEEFB' : '',
              }"
            >
              <div
                class="sex"
                :style="{
                  background: item.gender === 'M' ? '#ad7ef4' : '#4EA5F8',
                }"
              >
                {{ item.userName.substr(item.userName.length - 1, 1) }}
              </div>
              <span class="name">{{ item.userName }}</span>
              <div
                class="accomplish"
                :style="{
                  color:
                    (item.finishedNum === item.allocatedNum) &
                    (item.allocatedNum > 0)
                      ? '#38d594'
                      : 'transparent',
                }"
              >
                √
              </div>
              <div class="performance">
                <div>
                  完成进度：{{ item.finishedNum }}/{{ item.allocatedNum }}
                </div>
                <el-progress
                  :percentage="
                    item.allocatedNum === 0
                      ? 0
                      : (item.finishedNum / item.allocatedNum) * 100
                  "
                  color="#38D695"
                  :show-text="false"
                ></el-progress>
              </div>
            </div>
          </div>
        </div>
        <div class="emr-container-main-right">
          <div class="title">
            <div>文档目录</div>
            <div>
              <span>类型</span>
            </div>
            <div>负责人</div>
            <div>完成情况</div>
          </div>
          <div class="tree">
            <el-tree
              :data="treeData"
              accordion
              :show-checkbox="savelist.userAccount != ''"
              node-key="id"
              ref="tree"
              highlight-current
              :props="defaultProps"
              :default-checked-keys="defaultKeys"
              :default-expand-all="true"
              v-loading="isLoading"
            >
              <div class="custom-tree-node" slot-scope="{ node }">
                <div class="tree-label">{{ node.data.label }}</div>

                <div class="tree-dataType" v-show="node.data.issubmit">
                  <span v-if="node.data.evaluationCategory == '0'" class="jiben"
                    >基本项</span
                  >
                  <span
                    v-else-if="node.data.evaluationCategory == '1'"
                    class="xuanze"
                    >选择项</span
                  >
                </div>
                <div class="tree-principal" v-if="node.data.issubmit">
                  {{ node.data.personInCharge }}
                </div>
                <!-- <div
                  class="tree-principal"
                  v-else-if="
                    node.data.issubmit & (node.data.taskStatus === '0')
                  "
                >
                  未分配
                </div> -->
                <div class="tree-state">
                  <svg-icon
                    icon-class="icon_success"
                    v-show="node.data.issubmit && node.data.taskStatus === '2'"
                  />
                  <span
                    class="fonttask"
                    v-show="node.data.issubmit && !node.data.taskStatus"
                  >
                    未分配</span
                  >
                  <span
                    class="fonttask"
                    v-show="node.data.issubmit && node.data.taskStatus === '0'"
                  >
                    未分配</span
                  >
                  <span
                    class="fonttask"
                    v-show="node.data.issubmit && node.data.taskStatus === '1'"
                  >
                    已分配
                  </span>
                </div>
              </div>
            </el-tree>
          </div>

          <div class="right-button" v-show="savelist.userAccount != ''">
            <el-button
              size="mini"
              @click="handleNodeClick({ loginId: savelist.userAccount })"
              >还原</el-button
            >

            <el-button size="mini" type="primary" @click="savetaskallocation"
              >保存</el-button
            >
          </div>
        </div>
      </div>
    </div>
    <Addpersonnel ref="Addpersonnel" :activeporjectid="activeporject.id" />
  </MainCard>
</template>

<script>
import { queryAllow } from "@/api/document-management/dictionary-configuration";

import {
  getUserListAndTasks,
  getTasktree,
  queryDirectoryTree,
  savetaskallocation,
} from "@/api/empirical-material-mgt";
import Addpersonnel from "./components/Addpersonnel.vue";
// import { queryProject } from "@/api/document-management/project-management";
export default {
  components: {
    Addpersonnel,
  },
  data() {
    return {
      isLoading: false,
      queryLeftTreelevelCode: "", //已选择等级
      // 人员列表
      namedata: {},
      // 底部数据
      numberobj: {
        allNum: 0,
        finished: 0,
        allocated: 0,
      },
      // 保存数据
      savelist: {
        userAccount: "",
        levelCode: "",
        taskAllocations: [],
      },
      levelCodeData: [], //等级
      treeData: [], //左侧树形数据
      defaultKeys: [], //穿梭框默认选中的
      // 树形数据
      defaultProps: {
        children: "children",
        label: "label",
      },
      activeporject: {},
      // projectList: [],
      // selectedProject: "",
    };
  },
  async created() {
    if (this.$route.params.id === undefined) {
      this.activeporject = JSON.parse(sessionStorage.getItem("projectactive"));
    } else {
      this.activeporject = this.$route.params;
    }
    this.queryLeftTreelevelCode = this.activeporject.levelCode;
    this.savelist = {
      userAccount: "",
      taskAllocations: [],
    };
    this.defaultKeys = []; //穿梭框默认选中的
    // 进入页面初始化查询等级
    await queryAllow({}).then((res) => {
      const filteredData = res.data.list.filter(
        (item) => parseInt(item.levelCode) <= this.activeporject.levelCode
      );
      this.levelCodeData = filteredData;
    });
    this.getgetUserListAndTasks();
    this.getLeftTreeList();
  },
  methods: {
    // 获取右树列表数据
    async getLeftTreeList(val) {
      this.isLoading = true;
      this.treeData = []; // 清空左侧树数据
      await queryDirectoryTree({
        levelCode: this.queryLeftTreelevelCode,
        userAccount: this.savelist.userAccount,
        projectId: this.activeporject.id,
      }).then((res) => {
        // const newData = res.data.map((item) => {
        //   return {
        //     label: item["serialNum"] + "、" + item["directoryName"],
        //     directoryCode: item["directoryCode"],
        //     directoryName: item["directoryName"],
        //     issubmit: false,
        //     children: item.secondLevels.map((item2) => {
        //       return {
        //         label: item2["serialNum"] + "、" + item2["directoryName"],
        //         directoryCode: item2["directoryCode"],
        //         directoryName: item2["directoryName"],
        //         issubmit: false,
        //         children: item2.thirdLevels.map((item3) => {
        //           return {
        //             label: item3["directoryCode"] + item3["directoryName"],
        //             issubmit: true,
        //             directoryCode: item3["directoryCode"],
        //             directoryName: item3["directoryName"],
        //             levelCode: item3["levelCode"],
        //             taskStatus: item3["taskStatus"],
        //             personInCharge: item3["personInCharge"],
        //             evaluationCategory: item3["evaluationCategory"],
        //             id: item3["directoryName"] + item3["directoryCode"],
        //           };
        //         }),
        //       };
        //     }),
        //   };
        // });
        const newData = [];
        res.data.forEach((item) => {
          newData.push({
            label: item["directoryName"],
            directoryCode: item["directoryCode"],
            directoryName: item["directoryName"],
            issubmit: false,
            children: [],
            secondLevels: item.secondLevels,
          });
        });

        newData.forEach((item) => {
          item.secondLevels.forEach((item2) => {
            item2.thirdLevels.forEach((item3) => {
              item.children.push({
                label: item3["directoryCode"] + item3["directoryName"],
                issubmit: true,
                directoryCode: item3["directoryCode"],
                directoryName: item3["directoryName"],
                levelCode: item3["levelCode"],
                taskStatus: item3["taskStatus"],
                personInCharge: item3["personInCharge"],
                evaluationCategory: item3["evaluationCategory"],
                id: item3["directoryName"] + item3["directoryCode"],
              });
            });
          });
        });
        this.treeData = newData;
        this.isLoading = false;
      });
    },

    // 获取所有人员
    getgetUserListAndTasks() {
      getUserListAndTasks({
        levelCode: this.queryLeftTreelevelCode,
        userName: this.userName,
        projectId: this.activeporject.id,
      }).then((res) => {
        if (res.status === 0) {
          this.namedata = res.data.empiricalmaterial;
          this.numberobj.allNum = res.data.allNum;
          this.namedata.forEach((item) => {
            this.numberobj.allocated =
              item.allocatedNum + this.numberobj.allocated;
            this.numberobj.finished =
              item.finishedNum + this.numberobj.finished;
          });
          this.getLeftTreeList();
        }
      });
    },
    // 点击相关人员获取已激活列表
    async handleNodeClick(data) {
      this.defaultKeys = [];
      this.savelist.userAccount = data
        ? data.userAccount
        : this.savelist.userAccount;
      await getTasktree({
        levelCode: this.queryLeftTreelevelCode,
        userAccount: this.savelist.userAccount,
        levelCode: this.queryLeftTreelevelCode,
        projectId: this.activeporject.id,
      }).then((res) => {
        if (res.status === 0) {
          if (res.data.taskAllocations.length >= 1) {
            res.data.taskAllocations.forEach((item) => {
              if (item.personInCharge === this.savelist.userAccount) {
                this.defaultKeys.push(
                  item["directoryName"] + item["directoryCode"]
                );
              }
            });
            this.$refs.tree && this.$refs.tree.setCheckedKeys(this.defaultKeys);
          }
        }
      });
      this.getLeftTreeList();
    },
    // 保存
    async savetaskallocation() {
      this.savelist.levelCode = this.queryLeftTreelevelCode;
      var checkedNodes = this.$refs.tree.getCheckedNodes(true);
      if (checkedNodes.length > 0) {
        checkedNodes.forEach((item) => {
          this.savelist.taskAllocations.push({
            directoryCode: item.directoryCode,
            directoryName: item.directoryName,
          });
        });
      } else {
        this.savelist.taskAllocations = [];
      }
      await savetaskallocation({
        ...this.savelist,
        levelCode: this.queryLeftTreelevelCode,
        projectId: this.activeporject.id,
      }).then((res) => {
        if (res.status === 0) {
          this.$message({
            message: "保存成功!",
            type: "success",
          });
          this.savelist.taskAllocations = [];
          this.getgetUserListAndTasks();
          this.handleNodeClick();
        } else {
          this.$message({
            message: res.msg,
            type: "error",
          });
          this.getgetUserListAndTasks();
        }
      });
    },
  },

  // computed: {
  //   activeproject() {
  //     return this.activeporject;
  //   },
  // },
  // watch: {
  //   activeproject: {
  //     async handler(val) {
  //       if (Boolean(val) === true) {
  //         let that = this;
  //         await queryAllow({}).then((res) => {
  //           const filteredData = res.data.list.filter(
  //             (item) => parseInt(item.levelCode) <= this.activeporject.levelCode
  //           );
  //           this.levelCodeData = filteredData;
  //         });
  //         this.savelist.userAccount = "";
  //         this.getgetUserListAndTasks();
  //         this.getLeftTreeList();
  //       }
  //     },
  //   },
  // },
};
</script>

<style scoped lang="scss">
@import "@/styles/emr-styles/emr-main-table.scss";

.emr-container {
  .tree-right {
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    .el-select {
      width: 100px;
    }
    svg {
      color: #5783e6;
    }
  }
  .emr-container-main {
    display: flex;
    position: relative;
    .emr-container-main-left {
      width: 15%;
      min-width: 300px;
      .title {
        margin-top: 10px;
        margin-bottom: 10px;
        display: flex;
        width: 100%;
        justify-content: space-between;
        font-size: 15px;
        color: #333333;
      }
      .tree {
        background: #ffffff;
        height: 90%;
        border-radius: 9px;
        border: 1px solid #e3e6e8;
        padding: 10px;
        overflow: scroll;
        .treeitem {
          font-size: 14px;
          line-height: 40px;
          word-break: break-word;
          width: 100%;
          text-align: center;
          border: 1px solid #6581e9;
          border-radius: 9px;
          cursor: pointer;
          color: #5270dd;
        }
        .treeitem2 {
          border-radius: 9px;
          border: 1px solid #e3e4f2;
          margin-top: 10px;
          line-height: 56px;
          display: flex;
          align-items: center;
          justify-content: space-around;
          cursor: pointer;
          padding: 0px 15px;
          .sex {
            background: #ad7ef4;
            width: 26px;
            height: 26px;
            line-height: 26px;
            text-align: center;
            font-size: 14px;
            border-radius: 50%;
            color: #ffffff;
          }
          .name {
            font-size: 15px;
            color: #000000;
            width: 25%;
          }
          .accomplish {
            font-weight: 800;
          }
          .performance {
            line-height: 30px;
          }
        }
      }
    }
    .emr-container-main-right {
      flex: 1;
      overflow: hidden;
      .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        width: 100%;

        .tree-label {
          width: 60%;
          text-align: left;
          white-space: normal;
        }

        .tree-dataType {
          text-align: left;
          width: 10%;
          min-width: 100px;
          span {
            border-radius: 4px;
            padding: 2px 6px;
          }
          .jiben {
            background: #e0ecfb;
          }
          .xuanze {
            background: #f8eec9;
          }
        }
        .tree-state {
          text-align: left;
          width: 10%;
          min-width: 100px;
          font-size: 30px;
          .fonttask {
            font-size: 14px;
            padding-bottom: 4px;
            line-height: 34px;
          }
        }
        .tree-principal {
          text-align: left;
          width: 10%;
          min-width: 100px;
          right: 120px;
        }
      }
      .title {
        margin-top: 20px;
        margin-left: 30px;
        margin-bottom: 10px;
        font-size: 15px;
        color: #333333;
        border-bottom: 1px solid #ebedef;
        line-height: 40px;
        justify-content: space-around;
        display: flex;
        > div {
          text-align: left;
        }
        :nth-child(1) {
          width: 60%;
        }
        :nth-child(2),
        :nth-child(3),
        :nth-child(4) {
          width: 9%;
        }
      }
      .el-tree {
        height: 60vh;
        overflow: scroll;
      }
    }
  }
  .right-statistics {
    margin-left: 10px;
    // padding-top: 4px;
    color: #666666;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .statistics {
      > span {
        margin-right: 40px;
        span {
          color: #38d594;
        }
      }
      b {
        color: #000;
      }
    }
  }
  .right-button {
    text-align: right;
  }
}
::v-deep .el-tree-node__content {
  height: auto;
  margin: 10px 0px;
}
::v-deep .el-progress-bar__outer {
  background: #fff !important;
}
::v-deep .el-progress__text {
  font-size: 14px !important;
}
.header {
  ::v-deep .el-input__inner {
    border: none;
    box-shadow: none;
    font-size: 20px;
    color: red;
    font-weight: bold;
  }
  ::v-deep .el-input {
    margin-bottom: 20px;
  }
}
</style>
