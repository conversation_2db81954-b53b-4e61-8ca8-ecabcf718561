<template>
  <div>
    <template
      v-if="
        formData.checkRuleType === 'ZTWZX' || formData.checkRuleType === 'TJWZX'
      "
    >
      <!-- 整体完整性 -->
      <el-form-item label="检测表或视图" prop="checkRuleTableOrView">
        <el-autocomplete
          ref="abc"
          v-model="formData.checkRuleTableOrView"
          :fetch-suggestions="querySearchAsync"
          value-key="tableOrViewName"
          @select="handlerTableOrViewSelect"
          placeholder="请输入内容"
          clearable
        ></el-autocomplete>
      </el-form-item>
      <el-form-item label="检测字段" prop="checkRuleColumn">
        <el-input
          :disabled="isDisabled"
          @click.native="handlerclick"
          v-model="formData.checkRuleColumn"
        >
          <el-button slot="append">
            <SelectFieldList
              type="checkRuleColumn"
              selectType="radio"
              @click.native.stop="handlerclick"
              :isDisabled.sync="isDisabled"
              :checkRuleColumnData="checkRuleColumnData"
              @backfillSelectedData="backfillSelectedData"
            />
          </el-button>
        </el-input>
      </el-form-item>
      <el-form-item label="检测方式" prop="isnulltag">
        <el-radio-group v-model="formData.isnulltag">
          <el-radio label="0">NULL</el-radio>
          <el-radio label="1">trim后为空</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 条件完整性 -->
      <template v-if="formData.checkRuleType === 'TJWZX'">
        <el-form-item label="定义WHERE条件" prop="checkRuleWhere">
          <el-input
            type="textarea"
            v-model="formData.checkRuleWhere"
          ></el-input>
        </el-form-item>
      </template>
      <el-form-item label="问题明细显示字段" prop="pbSubsidiaryColumns">
        <el-input
          :disabled="isDisabled"
          @click.native="handlerclick"
          v-model="formData.pbSubsidiaryColumns"
        >
          <el-button slot="append">
            <SelectFieldList
              type="pbSubsidiaryColumns"
              selectType="checkBox"
              @click.native.stop="handlerclick"
              :isDisabled.sync="isDisabled"
              :checkRuleColumnData="checkRuleColumnData"
              @backfillSelectedData="backfillSelectedData"
            />
          </el-button>
        </el-input>
      </el-form-item>
      <slot></slot>
    </template>
    <!-- 自定义完整性 -->
    <template v-if="formData.checkRuleType === 'ZDYWZX'">
      <CustomCheckRuleType :formData.sync="formData" />
    </template>
  </div>
</template>

<script>
import tableViewAndField from "@/mixins/tableViewAndField"
import CustomCheckRuleType from "@/components/CustomCheckRuleType/index.vue"
import SelectFieldList from "./common/SelectFieldList.vue"
export default {
  components: {
    CustomCheckRuleType,
    SelectFieldList,
  },
  mixins: [tableViewAndField],
  props: {
    formData: {
      type: Object,
    },
    dataSourceId: {
      type: Number,
    },
  },
  mounted() {
    if (this.formData.checkRuleTableOrView) {
      this.queryCheckRuleColumnData.tableName =
        this.formData.checkRuleTableOrView
      this.queryCheckRuleColumnData.dataSourceId = this.dataSourceId
      this.getCheckRuleColumnData()
    }
  },
  watch: {
    formData: {
      handler(val) {
        // 只要表单有值变化就清空获取的SQL数据
        this.$emit("clearCheckSQLData")
        if (val.checkRuleTableOrView) {
          this.isDisabled = false
        } else {
          this.clearPartData()
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    //handlerClear
    clearPartData() {
      this.isDisabled = true
      this.checkRuleColumnData = []
      this.formData.checkRuleColumn = ""
      this.formData.pbSubsidiaryColumns = ""
    },
    backfillSelectedData(val, type) {
      if (type === "checkRuleColumn") {
        this.formData.checkRuleColumn = val.join(",")
      } else if (type === "pbSubsidiaryColumns") {
        this.formData.pbSubsidiaryColumns = val.join(",")
      }
    },
    // 先选择表或视图
    handlerclick() {
      if (this.isDisabled) {
        this.$message({
          type: "warning",
          message: "请先选择检测表或视图内容",
        })
        this.$refs["abc"].focus()
      }
    },
    // 当当前系统和数据库的表选择时
    handlerTableOrViewSelect(val) {
      this.queryCheckRuleColumnData.tableName = val.tableOrViewName
      this.queryCheckRuleColumnData.dataSourceId = this.dataSourceId
      this.getCheckRuleColumnData()
    },
  },
}
</script>

<style lang="scss" scoped>
.selected {
  margin-bottom: 20px;
}
</style>
