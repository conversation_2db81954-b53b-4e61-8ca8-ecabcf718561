@import "./global.scss";
@import "./variables.scss";
@import "./mixin.scss";
@import "./transition.scss";
@import "./element-ui.scss";
// @import "./sidebar.scss";

@font-face {
  font-family: 'MySiYuanHeiTi';
  src: url('~@/font/思源黑体 CN-Normal.otf'),
    url('~@/font/思源黑体 CN-Medium.otf'),
    url('~@/font/思源黑体 CN-Heavy.otf'),
    url('~@/font/思源黑体 CN-Bold.otf');
  font-weight: normal;
  font-style: normal;
}

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: 'MySiYuanHeiTi', sans-serif;

}


html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

// main-container global css
.app-container {
  // padding: 20px 15px;
  padding-bottom: 40px;
}

h4 {
  margin: 5px 0px;
}


.transfer-title {
  margin: 0;
  font-size: 14px !important;
  font-weight: 500 !important;
}

.el-image-viewer__close {
  color: #fff;
}
