#app {
  .main-container {
    min-height: 100%;
    transition: margin-left 0.28s;
    // margin-left: $sideBarWidth;
    position: relative;
    background:#F3F4F5;
    footer {
      position: fixed;
      bottom: 0px;
      left: 0px;
      right: 0px;
      background-color: #f0f0f0;
      height: 30px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      font-size: 14px;
      color: rgba(0,0,0,0.4);
      padding-right: 20px;
    }
  }

  .sidebar-container {
    transition: width 0.28s;
    // width: $sideBarWidth !important;
    background-color: $menuBg;
    height: 100%;
    position: fixed;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
      padding-bottom: 19px;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }


    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 12px;
    }

    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
      .el-menu-item {
        &:hover {
          // you can use $subMenuHover
          background-color: $subMenuHover !important;
        }
      }
    

    // menu hover
    .submenu-title-noDropdown,
    .el-submenu__title {
      &:hover {
        background-color: $menuHover !important;
      }
      i {
        color: white;
      }
    }

    .is-active > .el-submenu__title {
      color: $subMenuActiveText !important;
    }

    & .nest-menu .el-submenu > .el-submenu__title,
    & .el-submenu .el-menu-item {
      min-width: $sideBarWidth !important;
      background-color: $subMenuBg;

      &:hover {
        background-color: $subMenuHover !important;
      }
    }
    
    .router-link-active .is-active{
      background-color: $subMenuHover !important;
      color: white;
    }
  }

    .el-submenu {
      overflow: hidden;

      & > .el-submenu__title {
        fill: currentColor;
        .svg-icon {
          font-size: 16px;
        }
        .sub-el-icon {
          font-size: 16px;
        }
      }
    }

    .el-menu--collapse {
      .el-submenu {
        & > .el-submenu__title {
          & > span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-submenu {
    min-width: $sideBarWidth !important;
  }

  .withoutAnimation {
    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  & > .el-menu {
    .svg-icon {
      margin-right: 12px;
    }
    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }
  }

  

  // the scroll bar appears when the subMenu is too long
  > .el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}
