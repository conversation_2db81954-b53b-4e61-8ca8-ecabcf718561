//  新标签打开中间件跳转
<template></template>

<script>
export default {
  data() {
    return {
      datajump: {},
    };
  },
  created() {

    sessionStorage.setItem("projectactive", JSON.stringify(this.$route.query));
    if (this.$route.query.newname) {
      this.$router.push({
        name: this.$route.query.newname,
        params: { ...this.$route.query },
      });
    }
  },
};
</script>

<style>
</style>