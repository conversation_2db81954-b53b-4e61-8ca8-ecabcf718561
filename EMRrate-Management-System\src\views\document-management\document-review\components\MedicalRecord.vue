<template>
  <div class="source-mgt-main">
    <div class="emr-container-left">
      <div>
        <div>
          文档目录
          <span></span>
        </div>
        <div
          v-if="
            activeporject.personInCharge.includes($store.state.user.loginId)
          "
        >
          是否和我相关
          <el-switch
            @change="getLeftTreeList"
            v-model="aboutme"
            active-color="#4969de"
            inactive-color="#aaa"
          >
          </el-switch>
        </div>
      </div>

      <div>
        <span><i></i>正常</span>
        <span style="color: #ff7200">
          <i style="background: #ff7200"></i>
          不满足阀值 ({{ statisticaldata.alarmnum }})
        </span>
        <span style="color: #ef4f4f">
          <i style="background: #ef4f4f"></i>
          错误 ({{ statisticaldata.errornum }})
        </span>
        <span style="color: #5270dd">
          <i style="background: #5270dd"></i>
          批注 ({{ statisticaldata.commentnum }})
        </span>
      </div>
      <div>
        <i class="el-icon-arrow-down"></i><i class="el-icon-arrow-up"></i>
      </div>
      <div>
        <el-tree
          :data="treeData"
          default-expand-all
          node-key="id"
          ref="tree"
          highlight-current
          :props="defaultProps"
          @node-click="handleClick"
        >
          <template #default="props">
            <el-tooltip
              v-if="props.node.label.length > (/^\d+、/.test(props.node.label) ? 12 : 17)"
              class="item"
              effect="dark"
              :content="props.node.label"
              placement="right"
            >
              <span :style="'color:' + props.node.data.color">
                {{ props.node.label.substring(0, /^\d+、/.test(props.node.label) ? 10 : 15) }}...
                <svg-icon
                  v-show="commentshow(props.node.data)"
                  icon-class="comment"
                  style="font-size: 24px"
                ></svg-icon>
              </span>
            </el-tooltip>
            <span v-else :style="'color:' + props.node.data.color">
              {{ props.node.label }}
              <svg-icon
                v-show="commentshow(props.node.data)"
                icon-class="comment"
                style="font-size: 24px"
              ></svg-icon>
            </span>
          </template>
        </el-tree>
      </div>
    </div>

    <div class="emr-container-right" ref="word" v-loading="fileloading">
      <MainCard v-show="queryWord.emrRuleType">
        <!-- <div class="remark">
          <el-input
            placeholder="备注(手动录入):需要说明的具体问题数据，如: 500条记录中，有400条满足逻辑关系，其中100条为空值"
            v-model="problemDataRemarks1"
          >
          </el-input>
          <el-button type="primary" @click="problemDataRemarks" size="mini">
            添加
          </el-button>
        </div> -->
        <div class="regeneration">
          <el-button type="text" @click="regeneratePreviewDocument">
            重新生成预览文档
          </el-button>
        </div>
        <div id="container"></div>
      </MainCard>
      <!-- 备注相关信息 -->
      <div class="noteinformation" v-show="queryWord.emrRuleType">
        <el-popover placement="bottom-start" width="800" trigger="click">
          <el-form ref="form" :model="noteinform" label-width="80px">
            <el-form-item label="文中说明:">
              <el-autocomplete
                v-model="noteinform.problemDataRemarks1"
                :fetch-suggestions="
                  (queryString, cb) => {
                    querySearchAsync(queryString, cb, 'problemDataRemarks1');
                  }
                "
                value-key="problemDataRemarks1"
                type="textarea"
                :rows="4"
                size="mini"
                style="width: 100%"
              ></el-autocomplete>
            </el-form-item>
            <el-form-item label="SQL注解:">
              <el-autocomplete
                v-model="noteinform.problemDataRemarks2"
                :fetch-suggestions="
                  (queryString, cb) => {
                    querySearchAsync(queryString, cb, 'problemDataRemarks2');
                  }
                "
                value-key="problemDataRemarks2"
                type="textarea"
                :rows="4"
                size="mini"
                style="width: 100%"
              ></el-autocomplete>
            </el-form-item>
            <el-form-item label="原因说明:">
              <el-autocomplete
                v-model="noteinform.problemDataRemarks3"
                :fetch-suggestions="
                  (queryString, cb) => {
                    querySearchAsync(queryString, cb, 'problemDataRemarks3');
                  }
                "
                value-key="problemDataRemarks3"
                type="textarea"
                :rows="4"
                size="mini"
                style="width: 100%"
              ></el-autocomplete>
            </el-form-item>
            <el-form-item label="整改措施:">
              <el-autocomplete
                v-model="noteinform.problemDataRemarks4"
                :fetch-suggestions="
                  (queryString, cb) => {
                    querySearchAsync(queryString, cb, 'problemDataRemarks4');
                  }
                "
                value-key="problemDataRemarks4"
                type="textarea"
                :rows="4"
                size="mini"
                style="width: 100%"
              ></el-autocomplete>
            </el-form-item>

            <el-button type="primary" @click="problemDataRemarks" size="mini">
              保存
            </el-button>
            <el-form-item> </el-form-item>
          </el-form>
          <el-button size="mini" type="primary" slot="reference">
            编辑备注信息
          </el-button>
        </el-popover>
      </div>
    </div>
  </div>
</template>

<script>
import {
  previewWord,
  problemDataRemarks,
  getProblemDataRemarks,
  regeneratePreviewDocument,
  getOneProblemDataRemark,
  getErrorDetail,
  getProblemDataHistoryRemarks,
} from "@/api/document-management/document-review";
import {
  queryLeftTreedocumentRuleConfiguration,
  queryDirectoryTree,
} from "@/api/document-management/rule-configuration";
export default {
  data() {
    return {
      // 文件加载
      fileloading: false,
      // 重新生成显示
      ISregeneration: false,
      loading: false,
      aboutme: true,
      treeData: [],
      failureResult: [],
      timer: "",
      // 备注历史
      querySearchAsynctype: "",
      querySearchAsynclist: [],
      queryData: {
        // 查询数据
        exportDocumentLevel: "",
        dataEndTime: "",
        dataStartTime: "",
        timedExportTime: "",
        exportDocumentType: "",
      },
      queryWord: {
        directoryCode: "",
        directoryName: "",
        emrRuleType: "",
        exportRecordId: "",
      },
      // 备注
      noteinform: {
        problemDataRemarks1: "",
        problemDataRemarks2: "",
        problemDataRemarks3: "",
        problemDataRemarks4: "",
      },
      defaultProps: {
        children: "children",
        label: "label",
        disabled: function (data) {
          if (data.children) {
            return true;
          } else {
            return false;
          }
        },
      },
      statisticaldata: {
        alarmnum: 0,
        errornum: 0,
        commentnum: 0,
      },
    };
  },
  created() {
    this.getLeftTreeList(this.exportDocumentLevel);
  },
  props: {
    exportRecordId: {
      type: String,
    },
    alarmfailureResult: {
      type: Array,
    },
    errorfailureResult: {
      type: Array,
    },
    exportDocumentLevel: {
      type: String,
    },
    activeporject: {
      type: Object,
    },
  },
  methods: {
    // 获取左侧树列表数据
    async getLeftTreeList(val) {
      await getErrorDetail(this.exportRecordId).then((res) => {
        if (res.status === 0) {
          this.failureResult = res.data;
        }
      });
      this.treeData = []; // 清空左侧树数据
      await queryDirectoryTree({
        configType: 0,
        projectId: this.activeporject.id,
        levelCode: this.exportDocumentLevel,
        needNotAllocationTask: false,
        userAccount: this.aboutme ? this.$store.state.user.loginId : null,
      }).then((res) => {
        this.statisticaldata = {
          alarmnum: 0,
          errornum: 0,
          commentnum: 0,
        };
        this.treeData = this.directoryredefine(res.data);
      });
    },
    // 数据计算
    statisticaldatacount(type, datas) {
      if (type === "告警") {
        datas.forEach((item) => {
          if (
            this.failureResult.告警.some(
              (items) =>
                (item["directoryCode"] === items.directoryCode) &
                (item["directoryName"] === items.directoryName) &
                (item["emrRuleType"] === items.emrRuleType)
            )
          ) {
            this.statisticaldata.alarmnum = this.statisticaldata.alarmnum + 1;
          } else if (item.children && item.children.length) {
            this.statisticaldatacount(type, item.children);
          }
        });
      }
      if (type === "失败") {
        datas.forEach((item) => {
          if (
            this.failureResult.失败.some(
              (items) =>
                (item["directoryCode"] === items.directoryCode) &
                (item["directoryName"] === items.directoryName) &
                (item["emrRuleType"] === items.emrRuleType)
            )
          ) {
            this.statisticaldata.errornum = this.statisticaldata.errornum + 1;
          } else if (item.children && item.children.length) {
            this.statisticaldatacount(type, item.children);
          }
        });
      }
      if (type === "批注") {
        datas.forEach((item) => {
          if (
            this.failureResult.批注.some(
              (items) =>
                (item["directoryCode"] === items.directoryCode) &
                (item["directoryName"] === items.directoryName) &
                (item["emrRuleType"] === items.emrRuleType)
            )
          ) {
            this.statisticaldata.commentnum =
              this.statisticaldata.commentnum + 1;
          } else if (item.children && item.children.length) {
            this.statisticaldatacount(type, item.children);
          }
        });
      }
    },
    // 质量文档目录重定义
    directoryredefine(arr) {
      const newData = arr.map((item) => {
        return {
          label: item["directoryName"],
          directoryCode: item["directoryCode"],
          directoryName: item["directoryName"],
          emrRuleType: item["emrRuleType"],
          issubmit: false,
          children: item.secondLevels.map((item2) => {
            return {
              label: item2["directoryCode"] + item2["directoryName"],
              directoryCode: item2["directoryCode"],
              directoryName: item2["directoryName"],
              emrRuleType: item2["emrRuleType"],
              issubmit: false,
              children: Object.keys(item2.thirdLevels).map((i) => {
                if (item2.thirdLevels[i].length === 1) {
                  let yellowcolorfont = "";
                  this.failureResult.告警.forEach((items, index) => {
                    if (
                      (item2.thirdLevels[i][0]["directoryCode"] ===
                        items.directoryCode) &
                      (item2.thirdLevels[i][0]["directoryName"] ===
                        items.directoryName) &
                      (item2.thirdLevels[i][0]["emrRuleType"] ===
                        items.emrRuleType)
                    ) {
                      yellowcolorfont = items.msg;
                    }
                  });
                  return {
                    label: yellowcolorfont
                      ? i +
                        "  :  " +
                        item2.thirdLevels[i][0]["fullDirectoryName"] +
                        "(" +
                        yellowcolorfont +
                        ")"
                      : i + "  :  " + item2.thirdLevels[i][0]["directoryName"],
                    issubmit: true,
                    directoryCode: item2.thirdLevels[i][0]["directoryCode"],
                    directoryName: item2.thirdLevels[i][0]["directoryName"],
                    emrRuleType: item2.thirdLevels[i][0]["emrRuleType"],
                    personInCharge: item2.thirdLevels[i][0]["personInCharge"],
                    color: this.renderContent(item2.thirdLevels[i][0]),
                  };
                } else if (item2.thirdLevels[i].length > 1) {
                  return {
                    label: i,
                    issubmit: false,
                    id: item2["directoryName"] + item2["directoryCode"] + i,
                    children: item2.thirdLevels[i].map((item3) => {
                      let yellowcolorfont = "";
                      this.failureResult.告警.forEach((items, index) => {
                        if (
                          (item3["directoryCode"] === items.directoryCode) &
                          (item3["directoryName"] === items.directoryName) &
                          (item3["emrRuleType"] === items.emrRuleType)
                        ) {
                          yellowcolorfont = items.msg;
                        }
                      });
                      return {
                        label: yellowcolorfont
                          ? item3["directoryName"] + "(" + yellowcolorfont + ")"
                          : item3["directoryName"],
                        issubmit: true,
                        directoryCode: item3["directoryCode"],
                        directoryName: item3["directoryName"],
                        emrRuleType: item3["emrRuleType"],
                        color: this.renderContent(item3),
                      };
                    }),
                  };
                }
              }),
            };
          }),
        };
      });
      return newData;
    },
    // 点击树形结构 表 或 view
    handleClick(data1, data2, data3) {
      if (data1.issubmit) {
        this.fileloading = true;
        this.queryWord = {
          directoryCode: data1.directoryCode,
          directoryName: data1.directoryName,
          emrRuleType: data1.emrRuleType,
          exportRecordId: this.exportRecordId,
        };
        // this.ISregeneration =
        //   data1.redcolor || data1.yellowcolor ? true : false;
        this.getProblemDataRemarks();
      }
    },

    // 问题数据备注
    problemDataRemarks() {
      problemDataRemarks({
        ...this.queryWord,
        ...this.noteinform,
      }).then(async (res) => {
        if (res.status === 0) {
          this.$message({
            message: "添加备注成功",
            type: "success",
          });
          await getErrorDetail(this.exportRecordId).then((res) => {
            if (res.status === 0) {
              this.failureResult = res.data;
            }
          });
          this.statisticaldata = {
            alarmnum: 0,
            errornum: 0,
            commentnum: 0,
          };
          this.statisticaldatacount("告警", this.treeData);
          this.statisticaldatacount("失败", this.treeData);
          this.statisticaldatacount("批注", this.treeData);
        }
      });
    },

    // 获取问题数据备注
    async getProblemDataRemarks() {
      this.noteinform = {
        problemDataRemarks1: "",
        problemDataRemarks2: "",
        problemDataRemarks3: "",
        problemDataRemarks4: "",
      };
      await previewWord(this.queryWord).then((res) => {
        document.getElementById("container").innerHTML = res;
        this.fileloading = false;
      });
      await getOneProblemDataRemark(this.queryWord).then((res) => {
        if ((res.status === 0) & Boolean(res.data)) {
          this.noteinform = {
            problemDataRemarks1: res.data.problemDataRemarks1
              ? res.data.problemDataRemarks1
              : "",
            problemDataRemarks2: res.data.problemDataRemarks2
              ? res.data.problemDataRemarks2
              : "",
            problemDataRemarks3: res.data.problemDataRemarks3
              ? res.data.problemDataRemarks3
              : "",
            problemDataRemarks4: res.data.problemDataRemarks4
              ? res.data.problemDataRemarks4
              : "",
          };
        }
      });
    },
    //控制树状颜色
    renderContent(node) {
      if (
        this.failureResult.批注.some(
          (items) =>
            (node["directoryCode"] === items.directoryCode) &
            (node["directoryName"] === items.directoryName) &
            (node["emrRuleType"] === items.emrRuleType)
        )
      ) {
        this.statisticaldata.commentnum = this.statisticaldata.commentnum + 1;
      }
      if (
        this.failureResult.告警.some(
          (items) =>
            (node["directoryCode"] === items.directoryCode) &
            (node["directoryName"] === items.directoryName) &
            (node["emrRuleType"] === items.emrRuleType) &
            (items.errorType === "告警")
        )
      ) {
        this.statisticaldata.alarmnum = this.statisticaldata.alarmnum + 1;
        return "#ff7200";
      } else if (
        this.failureResult.失败.some(
          (items) =>
            (node["directoryCode"] === items.directoryCode) &
            (node["directoryName"] === items.directoryName) &
            (node["emrRuleType"] === items.emrRuleType) &
            (items.errorType === "失败")
        )
      ) {
        this.statisticaldata.errornum = this.statisticaldata.errornum + 1;
        return "#EF4f4f";
      } else {
        return "#606266";
      }
    },
    commentshow(node) {
      if (
        this.failureResult.批注.some(
          (items) =>
            (node["directoryCode"] === items.directoryCode) &
            (node["directoryName"] === items.directoryName) &
            (node["emrRuleType"] === items.emrRuleType)
        )
      ) {
        return true;
      }
    },
    // 重新生成预览文档
    async regeneratePreviewDocument() {
      await regeneratePreviewDocument(this.queryWord).then(async (res) => {
        this.fileloading = true;
        this.getProblemDataRemarks();
        if (res.status === 0) {
          this.backactivetree(this.treeData, res.data);
          this.ISregeneration = false;

          await getErrorDetail(this.exportRecordId).then((res) => {
            if (res.status === 0) {
              this.failureResult = res.data;
            }
          });
          this.statisticaldata = {
            alarmnum: 0,
            errornum: 0,
            commentnum: 0,
          };
          this.statisticaldatacount("告警", this.treeData);
          this.statisticaldatacount("失败", this.treeData);
          this.statisticaldatacount("批注", this.treeData);
          // this.errorfailureResult.forEach((item, index) => {
          //   if (
          //     (this.queryWord.directoryCode === item.directoryCode) &
          //     (this.queryWord.directoryName === item.directoryName) &
          //     (this.queryWord.emrRuleType === item.emrRuleType)
          //   ) {
          //     this.errorfailureResult.splice(index, 1);
          //     // this.getLeftTreeList(this.queryData.exportDocumentLevel);
          //     return;
          //   }
          // });
        }
      });
    },

    backactivetree(datas, resdata) {
      datas.forEach((item) => {
        if (
          (resdata.directoryCode === item.directoryCode) &
          (resdata.directoryName === item.directoryName) &
          (resdata.emrRuleType === item.emrRuleType)
        ) {
          if (resdata.errorType === "正常") {
            item.color = "#606266";
          } else if (resdata.errorType === "告警") {
            item.color = "#ff7200";
          } else if (resdata.errorType === "失败") {
            item.color = "#ef4f4f";
          }
        } else if (item.children && item.children.length) {
          this.backactivetree(item.children, resdata);
        }
      });
    },
    handle: function () {
      var startAt = (new Date(this.date) * 1000) / 1000;
      if (startAt < Date.now()) {
        this.date = new Date();
      }
    },

    querySearchAsync(queryString, cb, type) {
      if (this.querySearchAsynctype !== type) {
        this.querySearchAsynctype = type;
        getProblemDataHistoryRemarks({
          directoryCode: this.queryWord.directoryCode,
          directoryName: this.queryWord.directoryName,
          emrRuleType: this.queryWord.emrRuleType,
          type: type,
        }).then((res) => {
          if (res.status === 0) {
            this.querySearchAsynclist = res.data;
            cb(res.data);
          }
        });
      } else {
        cb(this.querySearchAsynclist);
      }
    },
  },
  destroyed() {
    //清除定时器
    clearInterval(this.timer);
  },
};
</script>

<style  scoped lang="scss">
.source-mgt-main {
  display: flex;
  margin-top: 10px;

  .emr-container-left {
    width: 20%;
    min-width: 300px; // 增加最小宽度
    border-right: 1px solid #dbdde1;
    padding-right: 10px;
    > div:nth-child(1) {
      font-weight: bold;
      font-size: 15px;
      color: #4969de;
      line-height: 20px;
      margin-top: 20px;
      display: flex;
      justify-content: space-between;
      > div:nth-child(1) {
        padding-bottom: 5px;
        // border-bottom: 3px solid #4969de;
      }
      > div:nth-child(2) {
        color: #aaa;
        font-size: 14px;
      }
    }
    > div:nth-child(2) {
      background: #ffffff;
      box-shadow: 0px 3px 9px 0px rgba(0, 0, 0, 0.1);
      border-radius: 9px;
      border: 1px solid #e1e1e1;
      padding: 8px 8px;
      display: flex;
      justify-content: space-around;
      font-size: 12px;
      margin: 20px 0px;
      span {
        color: #606266;
        display: flex;
        justify-content: center;
        height: 20px;
        line-height: 20px;
        i {
          display: block;
          margin-top: 5px;
          margin-right: 5px;
          height: 10px !important;
          width: 10px !important;
          background: #606266;
        }
      }
    }
    > div:nth-child(3) {
      i {
        border: 1px solid #cdcdcd;
        padding: 4px;
        background: #ffffff;
        border-radius: 3px;
        margin-right: 10px;
        cursor: pointer;
      }
    }
    > div:nth-child(4) {
      height: 70vh;
      overflow: scroll;
      background: transparent;
      background-repeat: 10px;
      padding: 10px;
      .el-tree {
        background: transparent;
      }
    }
  }
  .emr-container-right {
    flex: 1;
    margin-left: 20px;
    margin-top: 5vh;
    height: calc(76vh);
    text-align: center;
    display: flex;
    justify-content: space-around;
    position: relative;
    .remark {
      display: flex;
      justify-content: center;
      margin-bottom: 20px;
      .el-input {
        width: 500px;
        margin-right: 20px;
      }
    }
    #container {
      display: flex;
      justify-content: center;
      overflow-y: scroll;
      overflow-x: scroll;
      max-width: 800px !important;
      min-width: 600px !important;
      ::v-deep div {
        width: 100% !important;
        margin: 0px !important;
        ::v-deep span {
          width: 100% !important;
          word-break: break-all !important;
          color: red !important;
        }
      }
    }
    .noteinformation {
      position: absolute;
      right: 0px;
      top: -30px;
      text-align: right;
    }
  }
}
::v-deep .el-tree-node:focus > .el-tree-node__content {
  background-color: rgba(135, 206, 235, 0.3);
  // color: #409eff; //节点的字体颜色
  font-weight: bold;
}
.el-tree {
  font-size: 14px;
}
</style>
