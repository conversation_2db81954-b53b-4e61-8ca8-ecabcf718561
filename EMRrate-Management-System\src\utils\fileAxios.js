import store from '@/store/index.js'
import Axios from 'axios'
import router from '@/router/index.js'
import emitter from '@/utils/mitt.js'

const baseURL =process.env.VUE_APP_BASE_API

const axios = Axios.create({
    baseURL,
    timeout: 10000,// 请求超时 10s
})

/* 加载转圈圈，防抖，Login和Home接收emitter */
let acitveAxios = 0
let timer
const showLoading = () => {
    acitveAxios++
    if (timer) {
        clearTimeout(timer)
    }
    timer = setTimeout(() => {
        if (acitveAxios > 0) {
            emitter.emit('showLoading')
        }
    }, 400)
}

const closeLoading = () => {
    acitveAxios--
    if (acitveAxios <= 0) {
        clearTimeout(timer)
        emitter.emit('closeLoading')
    }
}

// 前置拦截器（发起请求之前的拦截）
axios.interceptors.request.use(
    (config) => {
        /**
         * 根据你的项目实际情况来对 config 做处理
         * 这里对 config 不做任何处理，直接返回
         */

        showLoading()
        config.data = JSON.stringify(config.data)
        config.headers = {
            'X-Token': store.getters.token,
            'Content-Type': 'application/json',
        }
        return config
    },
    (error) => {
        closeLoading()
        return Promise.reject(error)
    }
)

// 后置拦截器（获取到响应时的拦截）
axios.interceptors.response.use(
    (response) => {
        /**
         * 根据你的项目实际情况来对 response 和 error 做处理
         * 这里对 response 和 error 不做任何处理，直接返回
         */
        closeLoading()

        /* ************************TOKEN过期，重新登陆，等做完了再开*********************** */
        if (response.data.code === 510) {
            sessionStorage.removeItem('x-token')
            router.push('/Login')
        }

        return response.data
    },
    (error) => {
        closeLoading()
        if (error.response && error.response.data) {
            const code = error.response.status
            const msg = error.response.data.message
            ElMessage({ type: 'error', message: `Message:${msg}` })
            console.error(`[Axios Error]`, error.response)
        } else {
            ElMessage({ type: 'error', message: error })
        }
        return Promise.reject(error)
    }
)

export default axios