{"name": "vue-admin-template", "version": "4.4.0", "description": "A vue admin template with Element UI & axios & iconfont & permission control & lint", "author": "Pan <<EMAIL>>", "scripts": {"start": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit"}, "dependencies": {"@highlightjs/vue-plugin": "^0.9.0", "@toast-ui/vue-image-editor": "^3.15.2", "af-table-column": "^1.0.3", "axios": "0.18.1", "core-js": "^3.37.0", "docx-preview": "^0.1.15", "echarts": "^5.1.2", "el-tree-transfer": "^2.4.7", "element-ui": "2.13.2", "file-saver": "^2.0.5", "highlight.js": "^10.7.2", "vue-splitpane": "1.0.4", "js-cookie": "2.2.0", "js-web-screen-shot": "^1.9.9-rc.20", "lodash": "^4.17.21", "moment": "^2.29.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "region-screenshot-js": "^1.1.0", "qrcodejs2": "^0.0.2", "qs": "^6.11.2", "tui-image-editor": "^3.15.3", "vkbeautify": "^0.99.3", "vue": "2.6.10", "vue-codemirror": "^4.0.6", "vue-cropper": "^0.5.11", "vue-img-cutter": "^2.2.3", "vue-particles": "^1.0.9", "vue-router": "3.0.6", "vuex": "3.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "connect": "3.6.6", "eslint": "6.7.2", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "3.2.0", "mockjs": "1.0.1-beta3", "runjs": "4.3.2", "sass": "1.26.8", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-template-compiler": "2.6.10", "webpack": "^4.0.0"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "license": "MIT"}