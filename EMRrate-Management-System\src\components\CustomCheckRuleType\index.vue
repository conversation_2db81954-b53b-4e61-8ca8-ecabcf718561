<template>
  <div>
    <el-form-item label="总记录数SQL" prop="totalNmSql">
      <el-input type="textarea" v-model="formData.totalNmSql"></el-input>
    </el-form-item>
    <el-form-item label="问题数SQL" prop="questionNmSql">
      <el-input type="textarea" v-model="formData.questionNmSql"></el-input>
    </el-form-item>
    <el-form-item label="问题明细显示SQL" prop="pbSubsidiarySql">
      <el-input type="textarea" v-model="formData.pbSubsidiarySql"></el-input>
    </el-form-item>
  </div>
</template>
<script>
export default {
  props: {
    formData: {
      type: Object,
    },
  },
}
</script>

<style lang="scss" scoped>
</style>