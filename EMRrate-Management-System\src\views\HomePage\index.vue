<template>
  <div class="home-page-container">
    <div class="user-mgt">
      <UserMgt ref="updatePwd" />
    </div>
    <div class="header">
      <div class="header-left">
        <div class="avatar">{{ !!name ? name.trim().charAt(0) : '' }}</div>
        <div class="info">
          <div class="call">
            <span>{{ name }}</span
            >，{{ curTimeStatus }}好
          </div>
          <div class="date">
            今天是{{ curMonth }}月{{ curDay }}日，{{ curWeek }}
          </div>
        </div>
      </div>
      <div class="header-right">
        <!-- <span class="update-user-info">
          <el-button
            ><svg-icon
              class="icon"
              icon-class="update_info" />
            <span class="text">修改个人信息</span></el-button
          >
        </span> -->
        <span class="update-pwd">
          <el-button @click="updatePwd"
            ><svg-icon
              class="icon"
              icon-class="update_pwd" />
            <span class="text">修改密码</span></el-button
          >
        </span>
      </div>
    </div>
    <div class="banner">
      <div class="description">
        <div class="dep-top">
          <div class="title">
            <span>以评促建</span><span>助力医院</span
            ><span class="digitization">数字化能力</span>
          </div>
          <div class="dep-list">
            <ul>
              <li>
                <img
                  :src="pic1"
                  alt="" /><span>电子病历系统应用水平分级评价</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="main-content">
      <div class="left">
        <div class="introduce card">
          <div class="title">
            <img
              :src="welcome_img"
              alt="" /><span>欢迎使用 电子病历评级协作平台</span>
          </div>
          <div class="content">
            佳缘科技电子病历评级协作平台为医院提供全方位、全周期的电子病历分级评级
            管理服务，致力于医疗资源管理信息化领域平台的研发，以评促建，实现院内医
            疗数据互联互通，建立电子病历分级评价数据质量闭环管理，协助医院轻松应对
            难度逐年升级的电子病历评审。
          </div>
          <div class="list">
            <ul>
              <li>
                <img
                  :src="introduce_img"
                  alt="" /><span>分级评价标准化 </span>
              </li>
              <li>
                <img
                  :src="introduce_img"
                  alt="" /><span>评价规则规范化</span>
              </li>
              <li>
                <img
                  :src="introduce_img"
                  alt="" /><span>数据处理协作化</span>
              </li>
              <li>
                <img
                  :src="introduce_img"
                  alt="" /><span>文档导出结构化</span>
              </li>
            </ul>
          </div>
        </div>
        <div class="schedule card">
          <div class="title">我的任务及进度</div>
          <div
            class="list"
            v-loading="isScheduleLoading">
            <ul>
              <li
                v-for="item in scheduleList"
                :key="item.title">
                <img :src="schedule_img" />
                <div class="item-content">
                  <div class="left">
                    <div class="document-box">
                      <div class="title">{{ item.title }}</div>
                      <div class="bottom">
                        <span class="project-num"
                          >分配项目数：<span class="quality">{{
                            item.allocatedNum
                          }}</span></span
                        >
                        <span class="completed"
                          >已完成项：<span class="quality">{{
                            item.finishedNum
                          }}</span></span
                        >
                      </div>
                    </div>
                  </div>
                  <div class="right">
                    <div class="schedule-text">
                      完成进度：{{ item.perProgress }}%
                    </div>
                    <div class="schedule-line">
                      <el-progress
                        :text-inside="true"
                        :show-text="false"
                        :stroke-width="10"
                        :percentage="item.perProgress"></el-progress>
                    </div>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="right card">
        <div class="rate-info">
          <div class="title">电子病历评级的相关信息</div>
          <div
            class="rate-info-list"
            v-loading="isRateInfoLoading">
            <div
              class="list-item"
              v-for="item in rateInfoList"
              @click="openRateInfo(item)"
              :key="item.id">
              <div class="left">
                <div class="title">
                  {{ item.msgTitle }}
                </div>
                <div class="date">
                  <span>{{ item.sendingTime .split(" ")[0]}}</span>
                  <!-- <span>{{ item.sendingTime }}</span> -->
                  <span>{{ item.msgType }}</span>
                </div>
              </div>
              <div class="right"><i class="el-icon-arrow-right"></i></div>
            </div>
          </div>
        </div>
      </div>
      <RateInfo
        :rateInfoList="rateInfoList"
        :curRateInfo.sync="curRateInfo"
        ref="rateInfo" />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import pic1 from '@/assets/login/icon_1.png'
import introduce_img from '@/assets/home_page/introduce_img.png'
import schedule_img from '@/assets/home_page/schedule_img.png'
import welcome_img from '@/assets/home_page/welcome_img.png'
import UserMgt from '@/layout/components/UserMgt.vue'
import RateInfo from './components/RateInfo.vue'
import {
  getMytasksAndProgress as _getMytasksAndProgress,
  queryAllMsgList as _queryAllMsgList
} from '@/api/homePage/index'
// 获取当天星期
function numberToChinese(num) {
  var chineseNum = ['日', '一', '二', '三', '四', '五', '六']
  return '星期' + chineseNum[num]
}

export default {
  components: { UserMgt, RateInfo },
  data() {
    return {
      pic1,
      introduce_img,
      schedule_img,
      welcome_img,
      scheduleList: [],
      curRateInfo: {},
      rateInfoList: [],
      isScheduleLoading: true,
      isRateInfoLoading: true
    }
  },
  computed: {
    ...mapGetters(['name']),
    curTimeStatus() {
      return new Date().getHours() < 12 ? '上午' : '下午'
    },
    curWeek() {
      return numberToChinese(new Date().getDay())
    },
    curMonth() {
      return new Date().getMonth() + 1
    },
    curDay() {
      return new Date().getDate()
    }
  },
  created() {
    this.getMytasksAndProgress()
    this.queryAllMsgList()
  },
  methods: {
    updatePwd() {
      this.$refs.updatePwd.handleCommand('updatePwd')
    },
    openRateInfo(item) {
      this.$refs.rateInfo.dialogFormVisible = true
      this.curRateInfo = item
    },
    // 计算进度百分比
    calculatedPercentage(allNum, finishNum) {
      if (!finishNum || !allNum) return 0
      return +((finishNum / allNum) * 100).toFixed(0)
    },
    async getMytasksAndProgress() {
      this.isScheduleLoading = true
      const res = await _getMytasksAndProgress({
        levelCode: '',
        userAccount: this.$store.state.user.loginId,
      })
      if (res.status === 0 && res.data) {
        const { empiricalMaterial, emr } = res.data
        const scheduleList = [empiricalMaterial, emr]
        const titleArr = ['电子病历评级实证材料', '电子病历评级']
        this.scheduleList = scheduleList.map(
          ({ allocatedNum, finishedNum }, index) => ({
            title: titleArr[index],
            allocatedNum,
            finishedNum,
            perProgress: this.calculatedPercentage(allocatedNum, finishedNum)
          })
        )
        this.isScheduleLoading = false
      }
    },
    // 查询电子病历评级的相关信息
    async queryAllMsgList() {
      this.isRateInfoLoading = true
      const res = await _queryAllMsgList({
        pageNum: 1,
        pageSize: 9999
      })
      if (res.status === 0 && res.data) {
        this.rateInfoList = res.data.list
        this.isRateInfoLoading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.home-page-container {
  padding: 38px 32px;
  height: 100%;
  background: url(~@/assets/home_page/home_page_bg.jpg) top/cover no-repeat;
  /* display: flex; */
  /* margin-bottom: 30px; */
  position: relative;
  .user-mgt {
    position: absolute;
    right: 16px;
    top: 20px;
  }
  .header {
    display: flex;
    margin-bottom: 20px;
    .header-left {
      display: flex;
      align-items: center;
      .avatar {
        width: 44px;
        height: 44px;
        text-align: center;
        line-height: 44px;
        background-color: #ad7ef4;
        border-radius: 100%;
        color: #fff;
        font-size: 16px;
        margin-right: 12px;
      }
      .info {
        .call {
          margin-bottom: 6px;
          span {
            font-weight: 600;
          }
        }
      }
      margin-right: 46px;
    }
    .header-right {
      .update-user-info {
        margin-right: 10px;
      }
      .text {
        font-size: 14px;
      }
      span .icon {
        font-size: 16px;
        margin-right: 6px;
      }
    }
  }
  .banner {
    height: 130px;
    border-radius: 10px;
    background: url(~@/assets/home_page/home_page_banner.jpg) right / contain
      #fff no-repeat;
    margin-bottom: 16px;
    .description {
      padding: 30px 0 0 30px;
      .dep-top {
        .title {
          letter-spacing: 1px;
          font-size: 32px;
          margin-bottom: 16px;
          white-space: nowrap;
          font-weight: 700;
          color: #2c3148;
          span:first-child {
            margin-right: 20px;
          }
          .digitization {
            color: #5270dd;
          }
        }
        .dep-list {
          font-size: 16px;
        }
        .dep-list ul li {
          span {
            position: relative;
            z-index: 1;
            &::before {
              content: '';
              position: absolute;
              z-index: -1;
              bottom: 0;
              left: 0;
              width: 64px;
              height: 8px;
              background-color: #ffcf72;
            }
          }
          img {
            vertical-align: middle;
            margin-right: 8px;
          }
        }
      }
    }
  }
  .main-content {
    height: calc(100vh - 300px);
    display: flex;
    column-gap: 16px;
    > .left {
      flex: 1;
      display: flex;
      row-gap: 16px;
      flex-direction: column;
      .introduce {
        height: 50%;
        overflow-y: auto;
        background-color: #fff;
        .title {
          font-size: 18px;
          font-weight: 600;
          color: #2c3148;
          margin: 0 0 10px 0;
          img {
            width: 30px;
            vertical-align: middle;
            margin-right: 6px;
          }
        }
        .content {
          line-height: 1.8;
        }
        .list ul {
          display: flex;
          flex-wrap: wrap;
          margin: 16px 10px 0 10px;
          li {
            width: 50%;
            margin-bottom: 16px;
            img {
              vertical-align: middle;
              margin-right: 4px;
              margin-top: -1px;
            }
          }
        }
      }
      .schedule {
        height: 50%;
        background-color: #fff;
        > .title {
          font-weight: bold;
          font-size: 18px;
          color: #2c3148;
        }
        .list {
          margin-top: 20px;
          overflow-y: auto;
          height: calc(100% - 40px);
          ul li {
            display: flex;
            align-items: flex-start;
            & + li {
              margin-top: 16px;
            }
            img {
              margin-right: 6px;
              margin-top: -2px;
            }
            .item-content {
              flex: 1;
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding-bottom: 16px;
              border-bottom: 1px solid #edeff0;
              .left {
                display: flex;
                .document-box {
                  .title {
                    margin-bottom: 10px;
                    font-weight: 600;
                  }
                  .bottom {
                    font-size: 14px;
                    color: #888;
                    > span:first-child {
                      margin-right: 45px;
                    }
                    .quality {
                      font-weight: 600;
                    }
                  }
                }
              }
              .right {
                width: 134px;
                font-size: 14px;
                .schedule-text {
                  margin-bottom: 8px;
                  color: #888;
                }
                ::v-deep .el-progress-bar__inner {
                  background-color: #38d695;
                }
              }
            }
          }
        }
      }
    }
    > .right {
      flex: 1;
      background-color: #fff;
      .rate-info {
        height: 100%;
        > .title {
          font-weight: 600;
          font-size: 18px;
          color: #2c3148;
        }
        .rate-info-list {
          overflow-y: auto;
          height: calc(100% - 40px);
          .list-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 16px;
            border-bottom: 1px solid #edeff0;
            padding: 20px 0;
            transition: padding 0.5s;
            cursor: pointer;
            &:hover {
              background-color: #fcfdff;
              padding: 20px 20px;
            }
            .left .date {
              padding-top: 10px;
              span {
                margin-right: 30px;
                font-size: 14px;
                color: #888;
              }
            }
          }
        }
      }
    }
    .card {
      border-radius: 10px;
      padding: 22px;
    }
  }
}
</style>
