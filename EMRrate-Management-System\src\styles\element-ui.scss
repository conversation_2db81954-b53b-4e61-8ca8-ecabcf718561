// $primaryColor: rgb(27, 136, 192);
$primaryColor: rgb(82, 112, 221);
$primaryBgColor: #eaeefb;
$primaryColorLucency: rgb(107, 136, 241);
$secondaryColor: rgb(107, 207, 192);
$secondaryColorLucency: rgba(107, 207, 192, 0.8);
$thirdColor: rgb(243, 178, 85);
$thirdColorLucency: rgba(243, 178, 85, 0.8);
$--color-primary: teal;

// cover some element-ui styles
.el-upload {
  input[type='file'] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-form--label-top .el-form-item__label {
  padding: 0px;
  font-size: 13px;
}

.el-button {
  border-radius: 9px;
}
.el-button--primary {
  background: $primaryColor;
  border-color: $primaryColor;
  color: #fff;
  &:hover,
  &:focus {
    background-color: $primaryColorLucency;
    border-color: $primaryColorLucency;
    color: #fff;
  }
}

.el-button:focus,
.el-button:hover {
  color: $primaryColor;
  border-color: $primaryColor;
  background-color: $primaryBgColor;
}

.el-button--danger:focus,
.el-button--danger:hover {
  background: #ffeaea !important;
  border-color: #f78989 !important;
  color: #f78989;
}

.el-button--primary.is-plain {
  background: $primaryBgColor;
  border-color: $primaryColor;
  color: $primaryColor;
  &:hover,
  &:focus {
    background-color: $primaryColor;
    border-color: $primaryColor;
    color: #fff;
  }
}

.el-link.el-link--primary,
.el-link.el-link--primary:hover {
  color: $primaryColor;
}

.el-button--secondary {
  background: $secondaryColor;
  border-color: $secondaryColor;
  color: #fff;
  &:hover,
  &:focus {
    background-color: $secondaryColorLucency;
    border-color: $secondaryColorLucency;
    color: #fff;
  }
}
.el-autocomplete {
  width: 240px;
}
.el-autocomplete-suggestion.el-popper {
  // 修改远程输入下拉列表宽度
  width: 400px !important;
}

.el-button--third {
  background: $thirdColor;
  border-color: $thirdColor;
  color: #fff;
  &:hover,
  &:focus {
    background-color: $thirdColorLucency;
    border-color: $thirdColorLucency;
    color: #fff;
  }
}

.el-button--text {
  color: $primaryColor;
  padding: 10px 10px;
  border: none;
}
.el-button--text:focus,
.el-button--text:hover {
  border: none;
  border-radius: 2px;
}
.el-dropdown {
  color: $primaryColor;
  padding: 6px 10px;
}
.el-dropdown:hover,.el-dropdown:focus {
  background-color: $primaryBgColor;
  border-radius: 2px;
}

.el-breadcrumb {
  font-size: 16px;
}

.cell {
  // text-align: center;
  // line-height: 24px!important;
}
/**修改全局的滚动条*/
/**滚动条的宽度*/
::-webkit-scrollbar {
  width: 10px; //y轴宽度
  height: 10px; //x轴高度
}
//滚动条的滑块
::-webkit-scrollbar-thumb {
  background-color: #e4e7ed;
  border-radius: 10px;
  &:hover {
    background-color: #c1c5cc;
  }
}

.el-table {
  font-size: 14px;
}

//修改表格的滚动条
.el-table__body-wrapper::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
// 滚动条的滑块
.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #e4e7ed;
  border-radius: 10px;
  &:hover {
    cursor: pointer !important;
    background-color: #c1c5cc;
  }
}

.el-button--mini,
.el-button--small {
  font-size: 14px;
}

.scrollbar-wrapper {
  margin-bottom: 0 !important;
}

.el-dialog {
  border-radius: 10px;
  .el-dialog__header {
    color: #333;
    background-color: #fff;
    padding-top: 15px;
    border-radius: 10px;
    line-height: 20px;
    .el-dialog__headerbtn {
      top: 15px;
      .el-dialog__close {
        color: #ccc;
        font-size: 20px;
      }
    }
    .el-dialog__title {
      font-size: 18px;
      color: #333;
    }
  }
  .el-dialog__body {
    padding: 15px 10px;
  }
}
.el-textarea__inner {
  border-radius: 9px;
}

.has-gutter tr th {
  background-color: #f0f0f0;
}

.el-divider {
  margin: 16px 0px;
}

.el-dialog__headerbtn .el-dialog__close {
  font-size: 2em !important;
  margin-top: -5px;
}
.el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: $primaryColor;
}
.el-dialog__wrapper {
  z-index: 10000;
}
.el-input__inner {
  border-radius: 9px;
}

.el-tabs__active-bar {
  background-color: $primaryColor;
  width: 4px !important;
  height: 24px !important;
  margin-top: 8px;
}

.el-tabs__item:hover {
  color: $primaryColor;
}

.el-tabs__item.is-active {
  color: $primaryColor;
}

.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: $primaryColor;
  border-color: $primaryColor;
}

.el-checkbox__inner:hover {
  border-color: $primaryColor;
}

.el-dropdown-menu__item:focus,
.el-dropdown-menu__item:not(.is-disabled):hover {
  background-color: $primaryBgColor;
  color: $primaryColor;
}


.el-table td.gutter, .el-table th.gutter {
  background-color: #fff;
}