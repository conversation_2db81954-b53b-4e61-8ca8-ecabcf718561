import request from '@/utils/request'


// 查询等级字典
export function querylevelDictionary(data) {
  return request({
    url: '/emr/levelDictionary/query',
    method: 'post',
    data
  })
}



// 更新等级字典
export function updatelevelDictionary(data) {
  return request({
    url: '/emr/levelDictionary/update',
    method: 'post',
    data
  })
}


// 删除等级字典
export function deletelevelDictionary(params) {
  return request({
    url: '/emr/levelDictionary/delete',
    method: 'DELETE',
    params
  })
}



// 新增等级字典
export function addlevelDictionary(data) {
  return request({
    url: '/emr/levelDictionary/add',
    method: 'post',
    data
  })
}

// 查询允许的等级字典
export function queryAllow(data) {
  return request({
    url: '/emr/levelDictionary/queryAllow',
    method: 'post',
    data
  })
}