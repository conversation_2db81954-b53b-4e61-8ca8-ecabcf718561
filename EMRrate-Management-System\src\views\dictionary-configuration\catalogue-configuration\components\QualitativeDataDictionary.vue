<template>
  <div class="emr-container-main">
    <div class="emr-container-main-left">
      <div class="title">
        <b>文档目录</b>
        <el-tooltip
          class="item"
          effect="dark"
          content="刷新"
          placement="top-end"
        >
          <el-button
            class="addicon"
            style="padding: 6px"
            icon="el-icon-refresh"
            @click="loadNode(0)"
          ></el-button>
        </el-tooltip>
      </div>
      <div class="tree">
        <div v-for="(item1, index1) in treedata" :key="item1.id">
          <div @click="loadNode(1, index1)" class="treeitem">
            <i
              :class="
                item1.developedstate
                  ? 'el-icon-arrow-down'
                  : 'el-icon-arrow-right'
              "
            ></i>
            {{ item1.directoryName }}
          </div>
          <div
            class="treeitem treeitem2"
            :style="[{ background: index2 === activemenu ? '#E0E4F9' : '' }]"
            @click="handleNodeClick(item2, index1, index2)"
            v-for="(item2, index2) in item1.secondtree"
            :key="item2.id"
          >
            {{ item2.directoryCode }} {{ item2.directoryName }}
          </div>
        </div>
      </div>
    </div>
    <div class="emr-container-main-right" v-show="showtabel.directoryCode">
      <div class="title">
        <b>{{ showtabel.directoryName }} </b>
        <span v-show="showtabel.directoryCode"
          >(编号：{{ showtabel.directoryCode }} )</span
        >
      </div>

      <el-table
        class="emr-container-table"
        :data="tableData"
        ref="sourceMgtTable"
        v-loading="loading"
        :header-cell-style="{ background: '#fff', color: '#606266' }"
      >
        <!-- <el-table-column width="60">
          <template slot-scope="scope">
            <svg-icon
              v-if="!Boolean(scope.row.id)"
              icon-class="new"
              style="font-size: 36px"
            />
          </template>
        </el-table-column> -->
        <el-table-column prop="levelName" label="编号" min-width="150">
          <template slot="header">
            编号
            <el-tooltip placement="top">
              <div slot="content">
                质量编号规则：<br />
                <span class="wid60">01 </span> 病房医师<br />
                <span class="wid60">01.01 </span>一、病房医嘱处理部分<br />
                <span class="wid60">01.01.3 </span>
                总记录数参考值：医院运行基础数据：医嘱记录数：<br />
                <span class="wid60">01.01.3. </span>
                (3级基础项)一致性：医嘱记录(医嘱项目编码，医嘱项目名称)
                <br />
                <span class="wid60">01.01.4 </span>
                总记录数参考值：医院运行基础数据：医嘱记录数：
                <br />
                <span class="wid60">01.01.4.</span>
                (4级基础项)完整性：医嘱记录(患者标识、医嘱号、医嘱分类、医嘱项目编码、医嘱项目名称、医嘱开始时间)<br />
                <span class="wid60">01.01.5_1</span>
                总记录数参考值：医院运行基础数据：医嘱记录数：<br />
                <span class="wid60">01.01.5_1.</span>
                (5级基础项)完整性：医嘱记录(下达医嘱医师编码、下达医嘱医师姓名、医嘱状态)<br />
                <span class="wid60">01.01.5_2</span>
                总记录数参考值：医院运行基础数据：药疗医嘱记录：<br />
                <span class="wid60">01.01.5_2.</span>
                (5级基础项)整合性：药疗医嘱记录与护理执行记录可对照(医嘱号、医嘱项目编码、药疗医嘱给药途径、药疗医嘱用法)
              </div>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <div v-if="scope.row.editstate">
              <el-input
                :class="!Boolean(scope.row.id) ? 'noidinput' : ''"
                v-model="scope.row.directoryCode"
                placeholder="请输入编号"
              ></el-input>
            </div>
            <div v-else>
              {{ scope.row.directoryCode }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="levelName" label="名称" min-width="400">
          <template slot-scope="scope">
            <div v-if="scope.row.editstate">
              <el-input
                :class="!Boolean(scope.row.id) ? 'noidinput' : ''"
                v-model="scope.row.directoryName"
                :placeholder="scope.row.parentCode ? '' : '请输入名称'"
              ></el-input>
            </div>
            <div v-else>
              {{ scope.row.directoryName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="levelName" label="等级" min-width="100">
          <template slot-scope="scope">
            <div>
              <div v-if="scope.row.editstate">
                <el-select v-model="scope.row.associationLevel">
                  <el-option
                    v-for="item in levelCodeData"
                    :key="item.levelName"
                    :label="item.levelName"
                    :value="item.levelCode"
                  ></el-option>
                </el-select>
              </div>
              <div
                v-else-if="!scope.row.editstate && scope.row.associationLevel"
              >
                {{ scope.row.associationLevel }}级
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="levelName" label="类型" min-width="100">
          <template slot-scope="scope">
            <div>
              <div v-if="scope.row.editstate">
                <el-select v-model="scope.row.dataType">
                  <el-option label="基础" value="0"></el-option>
                  <el-option label="选择" value="1"></el-option>
                </el-select>
              </div>
              <div v-else-if="!scope.row.editstate">
                <span v-show="scope.row.dataType === '0'">基础</span>
                <span v-show="scope.row.dataType === '1'">选择</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="levelName" label="单位" min-width="100">
          <template slot-scope="scope">
            <div v-if="scope.row.editstate">
              <el-input
                :class="!Boolean(scope.row.id) ? 'noidinput' : ''"
                v-model="scope.row.dataUnit"
                :placeholder="scope.row.parentCode ? '' : '请输入单位'"
              ></el-input>
            </div>
            <div v-else>
              {{ scope.row.dataUnit }}
            </div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="220">
          <template slot-scope="scope">
            <span v-show="!scope.row.editstate">
              <el-button
                size="mini"
                type="text"
                @click="scope.row.editstate = true"
                >编辑</el-button
              ><el-divider direction="vertical"></el-divider>
            </span>

            <el-popconfirm
              title="此操作将删除该目录, 是否继续?"
              @onConfirm="deleteBase(1, scope.row, scope.$index)"
            >
              <el-button
                slot="reference"
                type="text"
                v-show="!scope.row.editstate"
                >删除</el-button
              >
            </el-popconfirm>
            <span v-show="scope.row.editstate">
              <el-button
                size="mini"
                type="primary"
                @click="
                  updateandadd(1, scope.row, scope.$index);
                  scope.row.editstate = false;
                "
                >保存</el-button
              ><el-divider direction="vertical"></el-divider>
            </span>
            <el-button
              v-show="scope.row.editstate"
              @click="editstatechange(1, scope.row, scope.$index)"
              >取消</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="add">
        <el-button
          size="mini"
          type="text"
          icon="el-icon-plus"
          @click="addnewoperation"
          >添加项目</el-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import {
  deleteBase,
  batchUpdateBase,
  getMedicaRecordsOrQuality,
  addBase,
} from "@/api/document-management/catalogue-configuration";
import { querylevelDictionary } from "@/api/document-management/dictionary-configuration";
export default {
  data() {
    return {
      props: {
        label: "label",
        children: "zones",
        isLeaf: "leaf",
      },
      tableData: [], // 表格数据

      loading: false,
      levelCodeData: [], //等级展示
      showtabel: {
        directoryName: "   ",
      },
      treedata: [],
      treedatacopy: [],
      activemenu: "",
    };
  },
  created() {
    // 进入页面初始化查询
    // 查询等级
    querylevelDictionary({}).then((res) => {
      this.levelCodeData = res.data.list;
    });
    this.loadNode(0);
  },
  methods: {
    // 获取左侧列表
    loadNode(level, index1) {
      if (level === 0) {
        getMedicaRecordsOrQuality({
          directoryType: 3,
          directoryCode: null,
        }).then((res) => {
          this.treedata = [];
          res.data.map((v) => {
            this.treedata.push(
              Object.assign(v, {
                developedstate: false,
                secondtree: [],
              })
            );
          });
          this.tableData = [];
          this.treedatacopy = JSON.parse(JSON.stringify(this.treedata));
          this.loadNode(1, 0);
        });
      }
      if (level === 1) {
        this.activemenu = "";
        this.treedata = JSON.parse(JSON.stringify(this.treedatacopy));
        getMedicaRecordsOrQuality({
          directoryType: 3,
          directoryCode: this.treedata[index1].directoryCode,
        }).then((res) => {
          let newarr = [];
          res.data.map((v) => {
            newarr.push(Object.assign(v));
          });
          this.treedata[index1].secondtree = newarr;
          this.treedata[index1].developedstate = true;
          this.handleNodeClick(this.treedata[index1].secondtree[0], index1, 0);
        });
      }
    },
    // 查询表格
    async handleNodeClick(data, index1, index2) {
      if (data.directoryCode.length === 5) {
        if (index1+1) {
          this.treedata[index1].secondtree[index2].active = true;
          this.activemenu = index2;
        }
        this.loading = true;
        this.tableData = [];
        this.showtabel = data;
        await getMedicaRecordsOrQuality({
          directoryType: 3,
          directoryCode: data.directoryCode,
        }).then((res) => {
          res.data.map((v) => {
            this.tableData.push(
              Object.assign(v, {
                editstate: false,
              })
            );
          });
          this.loading = false;
        });
      }
    },
    // 新增操作，添加的目录
    addnewoperation() {
      let newobj = {
        directoryType: "3",
        directoryName: "",
        directoryCode: "",
        dataUnit: "",
        associationLevel: "",
        id: null,
        editstate: true,
      };
      this.tableData.push(newobj);
    },

    //提交保存和编辑操作
    async updateandadd(type, item1, index1) {
      if (type === 1 && !Boolean(item1.id)) {
        await this.addBase(item1);
      } else if (type === 1 && Boolean(item1.id)) {
        await this.batchUpdateBase(item1, index1);
      }
    },
    // 新增操作请求
    async addBase(item) {
      await addBase(item).then((res) => {
        if (res.status !== 0) {
          this.$message({
            message: res.msg,
            type: "error",
          });
          return;
        } else {
          this.$message({
            message: "新增成功",
            type: "success",
          });
          this.handleNodeClick(this.showtabel);
        }
      });
    },
    // 修改操作请求
    async batchUpdateBase(item, index1) {
      await batchUpdateBase([item]).then((res) => {
        if (res.status === 0) {
          this.$message({
            message: "保存成功!",
            type: "success",
          });
        } else {
          this.$message({
            message: res.msg,
            type: "error",
          });
          this.editstatechange(1, 0, index1);
        }
      });
    },

    // 改变编辑状态(取消)
    editstatechange(type, item1, index1, item2, index2, item3, index3) {
      if (type === 1) {
        if (this.tableData[index1].id) {
          getMedicaRecordsOrQuality({
            directoryType: 3,
            directoryCode: this.showtabel.directoryCode,
          }).then((res) => {
            this.tableData[index1].directoryName =
              res.data[index1].directoryName;
            this.tableData[index1].directoryCode =
              res.data[index1].directoryCode;
            this.tableData[index1].dataUnit = res.data[index1].dataUnit;
            this.tableData[index1].associationLevel =
              res.data[index1].associationLevel;
            this.tableData[index1].editstate = false;
          });
        } else {
          this.tableData.splice(index1, 1);
        }
      }
    },
    // 删除操作
    async deleteBase(type, item1, index1) {
      if (type === 1) {
        deleteBase({ ids: item1.id }).then((res) => {
          if (res.status === 0) {
            this.$message({
              message: "删除成功!",
              type: "success",
            });
            this.tableData.splice(index1, 1);
          } else {
            this.$message({
              message: res.msg,
              type: "error",
            });
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/emr-styles/emr-main-table.scss";

.emr-container-main {
  display: flex;
  .emr-container-main-left {
    width: 15%;
    .title {
      margin-top: 20px;
      margin-bottom: 10px;
      display: flex;
      width: 100%;
      justify-content: space-between;
      font-size: 15px;
      color: #333333;
    }
    .tree {
      height: 80%;
      background: #ffffff;
      border-radius: 9px;
      border: 1px solid #e3e6e8;
      padding: 10px;
      overflow: scroll;
      .treeitem {
        // height: 30px;
        font-size: 14px;
        line-height: 30px;
        word-break: break-word;
        width: 100%;
        cursor: pointer;
      }
      .treeitem2 {
        padding-left: 20px;
        border-radius: 5px;
      }
    }
  }
  .emr-container-main-right {
    flex: 1;
    width: 80%;
    .title {
      margin-top: 20px;
      margin-left: 30px;
      margin-bottom: 10px;
      font-size: 15px;
      color: #333333;
    }
    .add {
      padding-left: 20px;
      border-bottom: 1px solid #ebeef5;
      height: 40px;
      line-height: 40px;
    }
  }
}

.wid60 {
  width: 60px;
  display: inline-block;
}
</style>
