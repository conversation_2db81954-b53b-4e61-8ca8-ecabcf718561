<template>
  <span>
    <svg-icon
      :class="{ arrowTransform: !flag, arrowTransformReturn: flag, icon: true}"
      icon-class="down"
    />
  </span>
</template>

<script>
export default {
  data() {
    return {
      // flag: false,
    }
  },
  props: {
    flag: {
      type: <PERSON><PERSON>an,
    }
  }
}
</script>

<style lang="scss" scoped>
.icon {
  margin-right: 4px;
  width: 10px;
}
.arrowTransform {
  transition: 0.2s;
  transform-origin: center;
  transform: rotateZ(90deg);
}

.arrowTransformReturn {
  transition: 0.2s;
  transform-origin: center;
  transform: rotateZ(0deg);
}
</style>