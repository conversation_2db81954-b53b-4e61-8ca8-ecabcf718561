import request from '@/utils/request'

/* 1. 系统码值内容 */

// 获取/查询系统码值内容
export function queryCodeValueContent(data) {
  return request({
    url: '/sysCodetabContent/query',
    method: 'post',
    data
  })
}
// 新增系统码值内容
export function addCodeValueContent(data) {
  return request({
    url: '/sysCodetabContent/add',
    method: 'post',
    data
  })
}
// 更新系统码值内容
export function updateCodeValueContent(data) {
  return request({
    url: '/sysCodetabContent/modify',
    method: 'post',
    data
  })
}
// 删除系统码值内容
export function deleteCodeValueContent(params) {
  return request({
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    url: '/sysCodetabContent/delete',
    method: 'delete',
    params
  })
}