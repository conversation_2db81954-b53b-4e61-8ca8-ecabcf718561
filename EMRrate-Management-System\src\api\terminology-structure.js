import request from '@/utils/request'

//获取标准类别
export function getStandards() {
    return request({
        url: '/termlist/getStandards',
        method: 'get'
    })
}

//术语列表 删除术语
export function deleteTerm(params) {
    return request({
        url: '/termlist/delete',
        method: 'delete',
        params
    })
}


//获取消费者列表
export function getConsumers() {
    return request({
        url: '/termlist/getConsumers',
        method: 'get'
    })
}

//新增术语
export function insertTerm(data) {
    return request({
        url: '/termlist/insert',
        method: 'post',
        data
    })
}

//术语列表
export function queryTermlist(data) {
    return request({
        url: '/termlist/query',
        method: 'post',
        data
    })
}

//术语列表 编辑术语
export function updateTerm(data) {
    return request({
        url: '/termlist/update',
        method: 'post',
        data
    })
}
//新增术语结构和术语
export function insertTermstructure(data) {
    return request({
        url: '/termstructure/insert',
        method: 'post',
        data
    })
}

//订阅术语列表
export function subRelationQuery(data) {
    return request({
        url: '/SubscriptionRelationship/query',
        method: 'post',
        data
    })
}

//订阅术语 编辑页面的查询，参数传系统编码
export function queryByTargetSystemId(data) {
    return request({
        url: '/SubscriptionRelationship/queryByTargetSystemId',
        method: 'post',
        data
    })
}

//订阅术语 编辑页面 批量新增
export function addTermList(data) {
    return request({
        url: '/SubscriptionRelationship/add',
        method: 'post',
        data
    })
}

//订阅术语 术语列表
export function subQueryTermlist(data) {
    return request({
        url: '/SubscriptionRelationship/queryTerm',
        method: 'post',
        data
    })
}
//订阅术语 编辑页面 删除
export function subDeleteTermlist(params) {
    return request({
        url: '/SubscriptionRelationship/delete',
        method: 'delete',
        params
    })
}




/***********************************术语结构编辑 ***********************************/
//获取术语结构列
export function getTermStructure(termId) {
    return request({
        url: `/termstructure/getTermStructure/${termId}`,
        method: 'get'
    })
}
//术语结构 编辑/添加/删除术语 三合一 
export function updateTermstructure(data) {
    return request({
        url: '/termstructure/update',
        method: 'post',
        data
    })
}

