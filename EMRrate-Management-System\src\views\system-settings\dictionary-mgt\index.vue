<template>
  <div class="dictionary-mgt-container">
    <h1 class="dictionary-mgt-title">字典管理</h1>
    <div class="dictionary-mgt-header">
      <el-form :model="queryData" ref="ruleForm" :inline="true">
        <el-form-item>
          <el-button
            type="primary"
            @click="startSyncDictData"
            :disabled="isDisabled"
            >更新主数据系统数据</el-button
          >
        </el-form-item>
        <el-form-item label="术语名称/编码" prop="term">
          <el-input
            v-model="queryData.term"
            placeholder="请输入术语名称/编码"
          ></el-input>
        </el-form-item>
        <el-form-item label="类别" prop="mappingsType">
          <el-select
            v-model="queryData.mappingsType"
            placeholder="请选择映射类别"
          >
            <el-option label="全部" value=""></el-option>
            <el-option label="互联互通字典映射" value="1"></el-option>
            <el-option label="院内字典映射" value="2"></el-option>
            <el-option label="三医监管" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="系统名称" prop="sysCode">
          <el-select v-model="queryData.sysCode" placeholder="请选择">
            <el-option label="全部" value=""></el-option>
            <el-option
              v-for="item in sysNameData"
              :key="item.sysName"
              :label="item.sysName"
              :value="item.sysCode"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="searchDictManagementList"
            icon="el-icon-search"
            >搜索</el-button
          >
        </el-form-item>
        <el-form-item>
          <el-button @click="resetForm('ruleForm')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="dictionary-mgt-main">
      <div class="dictionary-mgt-table">
        <el-table
          :data="tableData"
          style="width: 100%"
          border
          v-loading="loading"
          :header-cell-style="{ background: '#F5F7FA', color: '#606266' }"
          width="300"
        >
          <el-table-column prop="termId" label="术语编码"> </el-table-column>
          <el-table-column width="160" prop="termName" label="术语名称">
          </el-table-column>
          <el-table-column width="100" prop="mappingsType" label="映射类别">
            <template slot-scope="scope">
              <span v-if="scope.row.mappingsType === '1'"
                >互联互通字典映射</span
              >
              <span v-else-if="scope.row.mappingsType === '2'"
                >院内字典映射</span
              >
              <span v-else-if="scope.row.mappingsType === '3'">三医监管</span>
            </template>
          </el-table-column>
          <el-table-column
            width="160"
            prop="mappingsTable"
            label="字典对应表名"
          ></el-table-column>
          <el-table-column
            width="200"
            prop="codeField"
            label="编码字段"
          ></el-table-column>
          <el-table-column
            width="200"
            prop="nameField"
            label="名称字段"
          ></el-table-column>
          <el-table-column prop="sysCode" label="系统编码"></el-table-column>
          <el-table-column
            width="140"
            prop="sysName"
            label="系统名称"
          ></el-table-column>
          <el-table-column
            width="180"
            prop="bdCode"
            label="数据库编码"
          ></el-table-column>
          <el-table-column
            width="180"
            prop="bdName"
            label="数据库名称"
          ></el-table-column>
          <el-table-column prop="matchType" label="匹配类型">
            <template slot-scope="scope">
              <span v-if="scope.row.matchType === '0'">名称字段</span>
              <span v-else-if="scope.row.matchType === '1'">编码字段</span>
            </template>
          </el-table-column>
          <el-table-column
            width="180"
            prop="createTime"
            label="创建时间"
          ></el-table-column>
          <el-table-column fixed="right" label="操作" width="100">
            <template slot-scope="scope">
              <el-link
                type="primary"
                @click="
                  $router.push({
                    name: 'Dictionary-details',
                    params: { curDictionaryData: scope.row },
                  })
                "
                >查看数据</el-link
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="dictionary-mgt-pag">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryData.pageNum"
          :page-sizes="[5, 10, 15, 20]"
          :page-size="queryData.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalNum"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getDictManagementList,
  syncDictData,
  getSysInfoList,
} from "@/api/dictionary-mgt/dictionary-mgt"
export default {
  data() {
    return {
      queryData: {
        // 查询数据
        mappingsType: "", // 映射类型
        sysCode: "", // 系统编码
        sysName: "", // 系统名称
        pageNum: 1,
        pageSize: 10,
        term: "", // 术语名称/术语编码
      },
      isDisabled: false,
      tableData: [], // 表格数据
      sysNameData: [],
      totalNum: 1,
      loading: false,
    }
  },
  created() {
    // 进入页面初始化查询
    this.queryDictManagementList()
    this.getSysInfoList()
  },
  methods: {
    // 查询字典管理列表
    queryDictManagementList() {
      this.loading = true
      getDictManagementList(this.queryData).then((res) => {
        if (res.status !== 0) {
          this.$message({
            type: "error",
            message: res.msg,
          })
          this.loading = false
          return
        }
        this.tableData = res.data.list
        this.totalNum = res.data.total
        this.loading = false
      })
    },
    // 更新主数据系统数据
    startSyncDictData() {
      this.isDisabled = true
      syncDictData().then((res) => {
        if (res.status === 0) {
          this.$message({
            type: "success",
            message: "更新成功",
          })
          this.queryDictManagementList()
        } else {
          this.$message({
            type: "error",
            message: res.msg,
          })
        }
        this.isDisabled = false
      })
    },
    // 获取系统信息列表
    getSysInfoList() {
      getSysInfoList().then((res) => {
        if (res.status === 0) {
          this.sysNameData = res.data
        }
      })
    },
    // 重置
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.queryDictManagementList()
    },
    // 搜索任务组
    searchDictManagementList() {
      this.queryDictManagementList()
    },
    // 改变页面显示条数
    handleSizeChange(val) {
      this.queryData.pageSize = val
      this.queryDictManagementList()
    },
    // 改变页数
    handleCurrentChange(val) {
      this.queryData.pageNum = val
      this.queryDictManagementList()
    },
  },
}
</script>

<style scoped lang="scss">
.dictionary-mgt-container {
  margin-bottom: 40px;
  // padding-left: 10px;
  .dictionary-mgt-header {
    display: flex;
    margin: 10px 0;
    min-width: 800px;
    .search {
      margin: 0 20px;
    }
    .delete {
      margin-left: 20px;
    }
  }
  .dictionary-mgt-main {
    .dictionary-mgt-table {
      margin-top: 10px;
    }
    .dictionary-mgt-dialog {
      .mgt-dialog-upload {
        margin-left: 50px;
      }
    }
    .dictionary-mgt-pag {
      margin-top: 10px;
    }
  }
  ::v-deep .el-dialog {
    // dialog的定位
    top: -60px;
    .el-dialog__body {
      // height: 74vh;
      overflow: auto;
    }
  }
}
</style>
