<template>
  <el-dialog
    v-dialogDrag
    :visible.sync="dialogFormVisible"
    :close-on-click-modal="false"
    width="1200px"
    :show-close="false"
  >
    <div class="outsidedialog">
      <div class="dialog-new-title">
        <h4>质量数据-记录数（SQL）</h4>
        <i class="el-icon-close" @click="handlerClose()"></i>
      </div>
      <div class="dialog-show-directoryName">
        {{ formData.directoryCode }} {{ formData.directoryName }}
      </div>
      <el-form :model="formData" label-width="80px" ref="ruleForm">
        <div class="formmain">
          <div class="formleft">
            <div class="emr-headline"><i></i>符合要求记录数</div>
            <el-form-item label="选择类型:" prop="associatedType">
              <el-radio-group v-model="formData.associatedType">
                <el-radio label="0">{{
                  formData.dataSql.length > 0 ? "编辑SQL" : "添加SQL"
                }}</el-radio>
                <el-radio label="1">选择数据及运算公式</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="选择数源:"
              prop="associatedType"
              v-show="formData.associatedType === '0'"
            >
              <el-autocomplete
                class="inline-input"
                v-model="datasourcename"
                :fetch-suggestions="querydatasourcelist"
                placeholder="输入数据源检索"
                @select="getdatasource"
                ><template slot-scope="{ item }">
                  <div class="name">{{ item.dataSourceName }}</div>
                </template></el-autocomplete
              >
            </el-form-item>
            <el-form-item
              label="数量:"
              prop="associatedType"
              v-show="formData.associatedType === '0'"
            >
              <div class="editdataSql">
                <span class="sqlyuju">SQL语句</span>
                <el-input
                  type="textarea"
                  :rows="8"
                  placeholder="请输入内容"
                  v-model="formData.dataSql"
                >
                </el-input>
                <div class="editdataSqlbutton">
                  <el-button
                    size="mini"
                    icon="el-icon-date"
                    :disabled="recordsSqlbuttondisabl"
                    type="text"
                    @click="getTimeConditionSql('recordsSql')"
                    >插入时间</el-button
                  >
                  <el-divider direction="vertical"></el-divider>
                  <el-dropdown @command="handleCommand" :hide-on-click="false">
                    <el-button type="text" size="mini">
                      查询{{ recordsSqltime }}小时<i
                        class="el-icon-arrow-down el-icon--right"
                      ></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item :command="1"
                        >最近1小时</el-dropdown-item
                      >
                      <el-dropdown-item :command="2"
                        >最近2小时</el-dropdown-item
                      >
                      <el-dropdown-item :command="3"
                        >最近3小时</el-dropdown-item
                      >
                      <el-dropdown-item :command="4"
                        >最近4小时</el-dropdown-item
                      >
                      <el-dropdown-item :command="5"
                        >最近5小时</el-dropdown-item
                      >
                      <el-dropdown-item :command="null"
                        >自定义<el-input
                          size="mini"
                          v-model="recordsSqltime"
                          style="width: 60px; padding: 0px"
                          @change="
                            recordsSqltimechange(
                              recordsSqltime,
                              'recordsSqltime'
                            )
                          "
                        ></el-input
                      ></el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                  <el-divider direction="vertical"></el-divider>
                  <el-button
                    size="mini"
                    type="text"
                    :loading="formData.dataSql === '' || executestatus"
                    @click="
                      execute(formData.dataSql, 'recordsSql', recordsSqltime)
                    "
                    :disabled="formData.dataSql === '' || executestatus"
                    >语法检查</el-button
                  >
                  <span class="testresult">
                    测试执行结果：时间范围为近{{ recordsSqltime }}小时，共
                    {{ recordsSqlCount }} 条数据
                  </span>
                </div>
              </div>
            </el-form-item>
            <div v-show="formData.associatedType === '1'">
              <el-button
                slot="reference"
                size="mini"
                @click="
                  innerVisible = true;
                  addinnerVisiblestate = true;
                "
                >+ 添加</el-button
              >
              <el-table
                class="emr-container-table"
                :data="formData.associatedList"
                :header-cell-style="{ color: '#777' }"
              >
                <el-table-column
                  prop="directoryName"
                  label="已选择数据字段"
                  min-width="100"
                >
                  <template slot-scope="scope">
                    <div>
                      {{ scope.row.associatedDirectoryCode
                      }}{{ scope.row.associatedDirectoryName }}_{{
                        scope.row.associatedEmrRuleType
                      }}
                      / {{ scope.row.requireProjectName }}
                    </div>
                    <span class="recordcount">
                      {{
                        scope.row.associatedDataCategory === "0"
                          ? "总记录数"
                          : scope.row.associatedDataCategory === "1"
                          ? "满足条件记录数"
                          : ""
                      }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="directoryName" label="运算符" width="80">
                  <template slot-scope="scope">
                    <div>
                      {{
                        scope.row.operatorType === "0"
                          ? "+"
                          : scope.row.operatorType === "1"
                          ? "-"
                          : ""
                      }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="levelName" label="操作" width="160">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      @click="editpopoverform(scope.row, scope.$index)"
                      >编辑</el-button
                    >
                    <el-button
                      size="mini"
                      type="text"
                      @click="handleDelete(scope.row, 1)"
                      >删除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>

          <div class="formright">
            <div class="emr-headline"><i></i>符合要求记录数</div>
            <el-form-item label="选择类型:" prop="associatedType">
              <el-radio-group v-model="formData.conditionalAssociatedType">
                <el-radio label="0">{{
                  formData.dataSql.length > 0 ? "编辑SQL" : "添加SQL"
                }}</el-radio>
                <el-radio label="1">选择数据及运算公式</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="选择数源:"
              prop="associatedType"
              v-show="formData.conditionalAssociatedType === '0'"
            >
              <el-autocomplete
                class="inline-input"
                v-model="datasourcenameconditional"
                :fetch-suggestions="querydatasourcelist"
                placeholder="输入数据源检索"
                @select="getdatasourceconditional"
                ><template slot-scope="{ item }">
                  <div class="name">{{ item.dataSourceName }}</div>
                </template></el-autocomplete
              >
            </el-form-item>
            <el-form-item
              label="数量:"
              prop="associatedType"
              v-show="formData.conditionalAssociatedType === '0'"
            >
              <div class="editdataSql">
                <span class="sqlyuju">SQL语句</span>
                <el-input
                  type="textarea"
                  :rows="8"
                  placeholder="请输入内容"
                  v-model="formData.conditionalDataSql"
                >
                </el-input>
                <div class="editdataSqlbutton">
                  <el-button
                    size="mini"
                    icon="el-icon-date"
                    :disabled="recordsSqlbuttondisabl"
                    type="text"
                    @click="getTimeConditionSql('conditionalDataSql')"
                    >插入时间</el-button
                  >
                  <el-divider direction="vertical"></el-divider>
                  <el-dropdown @command="handleCommand" :hide-on-click="false">
                    <el-button type="text" size="mini">
                      查询{{ recordsSqltimeconditional }}小时<i
                        class="el-icon-arrow-down el-icon--right"
                      ></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item :command="1"
                        >最近1小时</el-dropdown-item
                      >
                      <el-dropdown-item :command="2"
                        >最近2小时</el-dropdown-item
                      >
                      <el-dropdown-item :command="3"
                        >最近3小时</el-dropdown-item
                      >
                      <el-dropdown-item :command="4"
                        >最近4小时</el-dropdown-item
                      >
                      <el-dropdown-item :command="5"
                        >最近5小时</el-dropdown-item
                      >
                      <el-dropdown-item :command="null"
                        >自定义<el-input
                          size="mini"
                          v-model="recordsSqltimeconditional"
                          style="width: 60px; padding: 0px"
                          @change="
                            recordsSqltimechange(
                              recordsSqltimeconditional,
                              'conditionalDataSql'
                            )
                          "
                        ></el-input
                      ></el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                  <el-divider direction="vertical"></el-divider>
                  <el-button
                    size="mini"
                    type="text"
                    :loading="
                      formData.conditionalDataSql === '' || executestatus
                    "
                    @click="
                      execute(
                        formData.conditionalDataSql,
                        'conditionalDataSql',
                        recordsSqltime
                      )
                    "
                    :disabled="
                      formData.conditionalDataSql === '' || executestatus
                    "
                    >语法检查</el-button
                  >
                  <span class="testresult">
                    测试执行结果：时间范围为近{{
                      recordsSqltimeconditional
                    }}小时，共 {{ recordsSqlCountconditional }} 条数据
                  </span>
                </div>
              </div>
            </el-form-item>
            <div v-show="formData.conditionalAssociatedType === '1'">
              <el-button
                slot="reference"
                size="mini"
                @click="
                  innerVisible = true;
                  addinnerVisiblestate = false;
                "
                >+ 添加</el-button
              >
              <el-table
                class="emr-container-table"
                :data="formData.conditionalAssociatedList"
                :header-cell-style="{ color: '#777' }"
              >
                <el-table-column
                  prop="directoryName"
                  label="已选择数据字段"
                  min-width="100"
                >
                  <template slot-scope="scope">
                    <div>
                      {{ scope.row.associatedDirectoryCode
                      }}{{ scope.row.associatedDirectoryName }}_{{
                        scope.row.associatedEmrRuleType
                      }}
                      / {{ scope.row.requireProjectName }}
                    </div>
                    <span class="recordcount">
                      {{
                        scope.row.associatedDataCategory === "0"
                          ? "总记录数"
                          : scope.row.associatedDataCategory === "1"
                          ? "满足条件记录数"
                          : ""
                      }}
                    </span>
                  </template>
                </el-table-column>

                <el-table-column prop="directoryName" label="运算符" width="80">
                  <template slot-scope="scope">
                    <div>
                      {{
                        scope.row.operatorType === "0"
                          ? "+"
                          : scope.row.operatorType === "1"
                          ? "-"
                          : ""
                      }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="levelName" label="操作" width="160">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      @click="editpopoverform(scope.row, scope.$index)"
                      >编辑</el-button
                    >
                    <el-button
                      size="mini"
                      type="text"
                      @click="handleDelete(scope.row, 2)"
                      >删除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
        <el-dialog
          class="test-dialog"
          :style="{ right: addinnerVisiblestate ? '30vw' : '-30vw' }"
          width="350px"
          :visible.sync="innerVisible"
          append-to-body
          :close-on-click-modal="false"
          :show-close="false"
          top="32vh"
          :modal="false"
          center
        >
          <div class="innerdialog">
            <div class="dialog-new-title">
              <h4>编辑数据及运算公式</h4>
              <i class="el-icon-close" @click="handlerCloseinnerVisible()"></i>
            </div>
            <el-form :model="popoverform" :rules="ruless" ref="rulessForm">
              <el-form-item label="文档目录：" prop="associatedDirectoryCode">
                <el-select
                  v-model="associatedDirectoryName"
                  filterable
                  remote
                  :remote-method="filterdirectory"
                  @change="currStationChange"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.id"
                    :label="
                      item.directoryCode +
                      item.directoryName +
                      '_' +
                      item.emrRuleType
                    "
                    :value="item.id"
                  >
                  </el-option> </el-select
              ></el-form-item>
              <el-form-item label="要求项目：" prop="requireProjectName">
                <el-select v-model="popoverform.requireProjectName">
                  <el-option
                    v-for="item in optionss"
                    :key="item.hospitalProject"
                    :label="item.hospitalProject"
                    :value="item.hospitalProject"
                  >
                  </el-option> </el-select
              ></el-form-item>
              <el-form-item label="运  算  符  ：" prop="operatorType"
                ><el-select v-model="popoverform.operatorType">
                  <el-option label="无" value=" "> </el-option>
                  <el-option label="+" value="0"> </el-option>
                  <el-option label="-" value="1"> </el-option> </el-select
              ></el-form-item>
              <el-form-item label="所属类别：" prop="associatedDataCategory">
                <el-select v-model="popoverform.associatedDataCategory">
                  <el-option label="总记录数" value="0"> </el-option>
                  <el-option label="满足条件记录数" value="1"> </el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button
              size="mini"
              type="primary"
              @click="handleAdd('rulessForm')"
              >确定</el-button
            >
            <el-button size="mini" @click="handlerCloseinnerVisible()"
              >关闭</el-button
            >
          </span>
        </el-dialog>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogFormVisible = false">取 消</el-button>
      <el-button type="primary" @click="submitForm('ruleForm')">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  queryDirectoryAndProject,
  batchUpdateBase,
} from "@/api/document-management/catalogue-configuration";
import {
  datasourcelist,
  getTimeConditionSql,
  execute,
} from "@/api/document-management/rule-configuration";
export default {
  data() {
    return {
      dialogFormVisible: false, // 弹框状态
      innerVisible: false, //新增弹窗打开
      addinnerVisiblestate: false, //新增位置弹窗打开
      recordsSqlbuttondisabl: false, //插入时间按钮禁用状态
      executestatus: false, //语法检查禁用状态
      loading: false,
      datasourcename: "", //数据源搜索框绑定字段
      recordsSqlCount: 0, //语法检查测试数据
      recordsSqltime: 1, //语法检查时间绑定字段
      dbType: "", //获取时间数据库类型
      datasourcenameconditional: "", //数据源搜索框绑定字段(符合要求)
      recordsSqlCountconditional: 0, //语法检查测试数据(符合要求)
      recordsSqltimeconditional: 1, //语法检查时间绑定字段(符合要求)
      dbTypeconditional: "", //获取时间数据库类型(符合要求)
      recordsSqlbuttondisablconditional: false, //插入时间按钮禁用状态(符合要求)
      // 表单数据
      formData: {
        directoryType: "3",
        directoryName: "",
        directoryCode: "",
        dataUnit: "",
        associatedType: "1",
        dataSourceId: "",
        dataSql: "",
        associatedList: [],
        parentId: "",
      },
      // 选择数组及运算公式模式新增表单
      popoverform: {
        associatedDataCategory: "",
        associatedDirectoryCode: "",
        associatedDirectoryName: "",
        associatedEmrRuleType: "",
        requireProjectName: "",
        directoryId: "",
        directoryCode: "",
        directoryName: "",
        operatorType: null,
      },
      options: [],
      optionss: [],
      associatedDirectoryName: "",
      popoverformnum: undefined,
      ruless: {
        associatedDirectoryCode: [
          { required: true, message: "请选择文档目录", trigger: "blur" },
        ],
        requireProjectName: [
          { required: true, message: "请选择要求项目", trigger: "blur" },
        ],
        operatorType: [
          { required: true, message: "请选择运算符", trigger: "blur" },
        ],
        associatedDataCategory: [
          { required: true, message: "请选择所属类别", trigger: "blur" },
        ],
      },
    };
  },
  props: {
    row: {
      type: Object,
    },
  },
  created() {
    queryDirectoryAndProject({
      directoryName: "",
    }).then((res) => {
      this.options = res.data;
      this.loading = false;
    });
  },
  methods: {
    // 编辑打开
    handlerOpenredact(row) {
      this.dialogFormVisible = true;
      datasourcelist({
        dataSourceName: "",
        whetherFilterCrossDb: "1",
        pageNum: 1,
        pageSize: 9999,
      }).then((res) => {
        res.data.list.forEach((element) => {
          if (Number(row.dataSourceId) === element.dataSourceId) {
            this.datasourcename = element.dataSourceName;
            this.dbType = element.databaseType;
          }
        });
      });
      this.formData = JSON.parse(JSON.stringify(row));
    },
    //数据源选择
    querydatasourcelist(queryString, cb) {
      datasourcelist({
        dataSourceName: queryString,
        whetherFilterCrossDb: "1",
        pageNum: 1,
        pageSize: 9999,
      }).then((res) => {
        cb(res.data.list);
      });
    },
    // 确定数据源
    getdatasource(val) {
      this.formData.dataSourceId = val.dataSourceId;
      this.datasourcename = val.dataSourceName;
      this.dbType = val.databaseType;
    },

    // 确定数据源(符合要求)
    getdatasourceconditional(val) {
      this.formData.conditionalDataSourceId = val.dataSourceId;
      this.datasourcenameconditional = val.dataSourceName;
      this.dbTypeconditional = val.databaseType;
    },

    // 获取时间条件
    getTimeConditionSql(type) {
      if (type === "dataSql") {
        getTimeConditionSql({ dbType: this.dbType }).then((res) => {
          if (this.formData.dataSql.includes(res.data.timeTemplateSql)) {
            this.recordsSqlbuttondisabl = true;
            this.$message({
              type: "info",
              message: "时间已插入，请勿重复插入",
            });
          } else {
            this.formData.dataSql =
              this.formData.dataSql + res.data.timeTemplateSql;
            this.recordsSqlbuttondisabl = true;
          }
        });
      } else if (type === "conditionalDataSql") {
        getTimeConditionSql({ dbType: this.dbTypeconditional }).then((res) => {
          if (
            this.formData.conditionalDataSql.includes(res.data.timeTemplateSql)
          ) {
            this.recordsSqlbuttondisablconditional = true;
            this.$message({
              type: "info",
              message: "时间已插入，请勿重复插入",
            });
          } else {
            this.formData.conditionalDataSql =
              this.formData.conditionalDataSql + res.data.timeTemplateSql;
            this.recordsSqlbuttondisablconditional = true;
          }
        });
      }
    },
    handleCommand(command) {
      this.recordsSqltime = command;
    },
    recordsSqltimechange(data, type) {
      if (data % 1 != 0) {
        this.$message({
          type: "info",
          message: "请输入正确的整数时间",
        });
      }
    },
    // 测试
    execute(sql, type, time) {
      this.executestatus = true;
      if (type === "dataSql") {
        if (this.formData.dataSourceId === "") {
          this.$message({
            type: "error",
            message: "请选择数源",
          });
          this.executestatus = false;
        } else {
          execute({
            dataSourceId: this.formData.dataSourceId,
            sql: sql,
            offsetHour: time,
          }).then((res) => {
            if (res.status === 0) {
              if (res.data.result.length === 1) {
                let key = Object.keys(res.data.result[0])[0];
                this.recordsSqlCount = res.data.result[0][key];
                this.$message({
                  message: `时间范围为最近${this.recordsSqltime}小时，得到结果为${this.recordsSqlCount}条数据`,
                  type: "success",
                });
              } else {
                this.$message({
                  type: "success",
                  message: res.msg,
                });
              }
              this.executestatus = false;
            } else {
              this.$message({
                type: "error",
                message: res.msg,
              });
              this.executestatus = false;
            }
          });
        }
      } else if (type === "conditionalDataSql") {
        if (this.formData.conditionalDataSourceId === "") {
          this.$message({
            type: "error",
            message: "请选择数源",
          });
          this.executestatus = false;
        } else {
          execute({
            dataSourceId: this.formData.conditionalDataSourceId,
            sql: sql,
            offsetHour: time,
          }).then((res) => {
            if (res.status === 0) {
              if (res.data.result.length === 1) {
                let key = Object.keys(res.data.result[0])[0];
                this.recordsSqlCountconditional = res.data.result[0][key];
                this.$message({
                  message: `时间范围为最近${this.recordsSqltimeconditional}小时，得到结果为${this.recordsSqlCountconditional}条数据`,
                  type: "success",
                });
              } else {
                this.$message({
                  type: "success",
                  message: res.msg,
                });
              }
              this.executestatus = false;
            } else {
              this.$message({
                type: "error",
                message: res.msg,
              });
              this.executestatus = false;
            }
          });
        }
      }
    },
    // 选择文档目录
    currStationChange(val) {
      this.popoverform.requireProjectName = "";
      this.optionss = [];
      this.options.forEach((item) => {
        if (item.id === val) {
          this.popoverform.associatedDirectoryCode = item.directoryCode;
          this.popoverform.associatedDirectoryName = item.directoryName;
          this.popoverform.associatedEmrRuleType = item.emrRuleType;
          this.popoverform.directoryId = this.formData.id;
          this.popoverform.directoryCode = this.formData.directoryCode;
          this.popoverform.directoryName = this.formData.directoryName;
          this.associatedDirectoryName =
            item.directoryCode + item.directoryName + "_" + item.emrRuleType;

          queryDirectoryAndProject({
            directoryName: item.directoryName,
            directoryCode: item.directoryCode,
            emrRuleType: item.emrRuleType,
          }).then((res) => {
            this.optionss = res.data.documentRuleConfigurationList;
          });
        }
      });
    },
    // 编辑数据选择
    editpopoverform(item, index) {
      this.innerVisible = true;
      this.popoverform = JSON.parse(JSON.stringify(item));
      this.popoverformnum = index;
      queryDirectoryAndProject({
        directoryName: item.associatedDirectoryName,
        directoryCode: item.associatedDirectoryCode,
        emrRuleType: item.associatedEmrRuleType,
      }).then((res) => {
        this.optionss = res.data.documentRuleConfigurationList;
      });
      this.associatedDirectoryName =
        this.popoverform.associatedDirectoryCode +
        this.popoverform.associatedDirectoryName +
        "_" +
        this.popoverform.associatedEmrRuleType;
    },
    // 删除一行
    handleDelete(row, type) {
      if (type === 1) {
        this.formData.associatedList.splice(
          this.formData.associatedList.indexOf(row),
          1
        );
      } else if (type === 2) {
        this.formData.conditionalAssociatedList.splice(
          this.formData.conditionalAssociatedList.indexOf(row),
          1
        );
      }
    },
    // 新增一行
    handleAdd(formName) {
      if (this.popoverform.id) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            this.addinnerVisiblestate
              ? this.$set(
                  this.formData.associatedList,
                  this.popoverformnum,
                  this.popoverform
                )
              : this.$set(
                  this.formData.conditionalAssociatedList,
                  this.popoverformnum,
                  this.popoverform
                );
            this.handlerCloseinnerVisible();
          } else {
            return false;
          }
        });
      } else {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            this.addinnerVisiblestate
              ? this.formData.associatedList.push(
                  JSON.parse(JSON.stringify(this.popoverform))
                )
              : this.formData.conditionalAssociatedList.push(
                  JSON.parse(JSON.stringify(this.popoverform))
                );

            this.handlerCloseinnerVisible();
          } else {
            return false;
          }
        });
      }
    },
    // 校验
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          batchUpdateBase([this.formData]).then((res) => {
            if (res.status !== 0) {
              this.$message({
                message: res.msg,
                type: "error",
              });
              return;
            } else {
              this.$message({
                message: "编辑成功",
                type: "success",
              });
              // this.$parent.getBase();
              this.handlerClose();
            }
          });
        } else {
          return false;
        }
      });
    },
    // 处理dialog关闭后
    handlerClose() {
      this.formData = {
        directoryType: "3",
        directoryName: "",
        directoryCode: "",
        dataUnit: "",
        associatedType: "0",
        dataSourceId: "",
        dataSql: "",
        associatedList: [],
        parentId: "",
      };
      this.recordsSqlbuttondisabl = false; //插入时间按钮禁用状态
      this.popoverformnum = undefined;
      this.executestatus = false; //语法检查禁用状态
      this.formLabelWidth = "120px";
      this.datasourcename = ""; //数据源搜索框绑定字段
      this.recordsSqlCount = 0; //语法检查测试数据
      this.recordsSqltime = 1; //语法检查时间绑定字段
      this.dbType = ""; //获取时间数据库类型
      this.dialogFormVisible = false;
    },
    // 关闭第第二层弹窗
    handlerCloseinnerVisible() {
      this.popoverform = {
        associatedDataCategory: "",
        associatedDirectoryCode: "",
        associatedDirectoryName: "",
        associatedEmrRuleType: "",
        requireProjectName: "",
        directoryId: "",
        directoryCode: "",
        directoryName: "",
        operatorType: null,
      };
      this.associatedDirectoryName = "";
      this.innerVisible = false;
    },
    // 筛选文档目录
    filterdirectory(query) {
      if (query !== "") {
        // console.log("22");
        queryDirectoryAndProject({
          directory: query,
        }).then((res) => {
          this.options = res.data;
          this.loading = false;
        });
      } else {
        queryDirectoryAndProject({
          directory: "",
        }).then((res) => {
          this.options = res.data;
          this.loading = false;
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/emr-styles/emr-dialog.scss";
::v-deep .el-dialog__body {
  // display: flex;
  // min-height: 600px;
  height: 100%;
  padding: 0px;
  padding: 10px 20px 20px 20px;
  .outsidedialog {
    min-height: 540px;
    .dialog-new-title {
      width: 100%;
      display: flex;
      justify-content: space-between;
    }
    .dialog-show-directoryName {
      background: #eaeefb;
      border-radius: 9px;
      padding: 10px 10px;
      margin-bottom: 20px;
    }
    .dialog-label-row {
      margin-top: 10px;
      margin-bottom: 20px;
      .label-item {
        background: #dee4fa;
        border-radius: 4px;
        text-align: center;
        padding: 4px 6px;
        margin-right: 10px;
        color: #333333;
        img {
          vertical-align: middle;
        }
      }
    }
    .editdataSql {
      border-radius: 9px;
      border: 1px solid #e3e6e8;
      min-height: 100px;
      position: relative;
      padding-top: 10px;
      .sqlyuju {
        position: absolute;
        top: -16px;
        left: 20px;
        font-size: 12px;
        color: #4565d8;
        background: #fff;
        padding: 0px 4px;
        z-index: 99999999;
      }
      .el-textarea__inner {
        border: 1px solid transparent;
      }
      .editdataSqlbutton {
        height: 50px;
        background: #f3f3f3;
        border-radius: 0px 0px 9px 9px;
        padding: 0px 15px;
        position: relative;
        .el-divider {
          margin: 10px 15px;
        }
        .el-button--text {
          color: #4c4c4c;
        }
        .testresult {
          position: absolute;
          right: 5px;
          bottom: 0px;
          color: #888888;
        }
      }
    }
    .formmain {
      display: flex;
      > div {
        flex: 1;
        width: 500px;
        .emr-headline {
          font-weight: bold;
          font-size: 16px;
          color: #333333;
          margin-bottom: 10px;
          i {
            border-left: 5px solid #5270dd;
            margin-right: 10px;
          }

          span {
            font-weight: 400;
            font-size: 14px;
            color: #333333;
          }
        }
      }
      .formleft {
        border-right: 1px solid #e3e3e5;
        padding-right: 20px;
      }
      .formright {
        padding-left: 20px;
      }
    }
  }
}
::v-deep .innerdialog {
  height: 220px;
  .dialog-new-title {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
}
.test-dialog {
  position: absolute;
  right: -30vw;
  top: 5vh;
}
</style>
