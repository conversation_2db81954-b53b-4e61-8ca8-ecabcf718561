<template>
  <div class="structure-config-container">
    <el-dialog
      v-dialogDrag
      title="结构设置"
      :visible.sync="dialogFormVisible"
      @open="handlerOpen"
      @closed="handlerClose"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="structure-config-header">
        <h3>系统名称：{{ row.dataSourceName }}</h3>
        <el-divider></el-divider>
      </div>
      <div class="structure-config-main">
        <div class="left">
          <div class="search">
            <el-input
              @change="handlerTreeQuery"
              style="width: 50%; margin-right: 10px"
              v-model="filterText"
              placeholder="请输入表名"
            ></el-input>
            <el-button @click="reset">重置</el-button>
          </div>
          <div v-if="isLoading" class="loading" v-loading="isLoading"></div>
          <div class="tree-structure">
            <el-tree
              :data="treeData"
              show-checkbox
              default-expand-all
              node-key="id"
              ref="tree"
              highlight-current
              @node-click="handlerNodeClick"
              :default-checked-keys="defaultCheckedArr"
              :props="defaultProps"
              @check="handlerChecked"
              @check-change="handleCheckChange"
            >
              <template #default="props">
                <span>
                  <i :class="getIconClass(props.node)"></i>
                  <span class="tree-itemLabel" style="margin-left: 6px">{{
                    props.node.label
                  }}</span>
                </span>
              </template>
            </el-tree>
          </div>
          <div class="button">
            <el-button type="primary" @click="synchronousDataStructure"
              >同步数据结构</el-button
            >
            <!-- <el-button type="primary" @click="addTable">添加</el-button> -->
          </div>
        </div>
        <div class="right">
          <h4>已添加表：</h4>
          <el-card shadow="never" v-loading="isSelLoading">
            <div
              v-if="savedStructureData.length === 0"
              style="color: #999; text-align: center; margin-top: 150px"
            >
              暂无数据
            </div>
            <el-tag
              v-else
              v-for="(item, index) in savedStructureData"
              :key="item"
              closable
              disable-transitions
              type="info"
              @close="handleClose(index)"
            >
              {{ item }}
            </el-tag>
          </el-card>
          <div class="set-relationship">
            <span>以上列表在更新库表结构时，关系设置: </span>
            <el-select v-model="saveDbStructureData.chooseResult" size="mini">
              <el-option label="有且" :value="0"></el-option>
              <el-option label="不包含" :value="1"></el-option>
            </el-select>
          </div>
          <div class="save">
            <el-button type="primary" @click="saveStructure">保存</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ArrowAnimation from "@/components/ArrowAnimation"

import {
  synchronousDataStructure,
  querySavedStructures,
  saveDbStructure,
} from "@/api/dataSourceStructureMgt/dataSourceMgt"

import { getLeftTree } from "@/api/dataSourceStructureMgt/dataStructureDetails"

// 找到树形结构某一节点
function findItemByLabel(data, label) {
  for (let i = 0; i < data.length; i++) {
    const item = data[i]
    if (item.label === label) {
      return item
    }
    if (item.children) {
      const result = findItemByLabel(item.children, label)
      if (result) {
        return result
      }
    }
  }
  return null
}
function filterNodesByLabel(node, keyword) {
  if (node.label.includes(keyword)) {
    return node // 当前节点包含关键字，返回该节点
  } else if (node.children) {
    const filteredChildren = node.children
      .map((child) => filterNodesByLabel(child, keyword))
      .filter(Boolean) // 递归过滤子节点
    if (filteredChildren.length > 0) {
      return {
        ...node,
        children: filteredChildren,
      } // 子节点包含关键字，返回该节点及其子节点
    }
  }
}
// 模糊查询
function filterTreeByLabel(tree, keyword) {
  return tree.map((node) => filterNodesByLabel(node, keyword)).filter(Boolean)
}
// 找到前后两个数组的变化，并返回新增或者移除的元素
function findAddedOrRemovedItems(oldArray, newArray) {
  const addedItems = newArray.filter((item) => !oldArray.includes(item))
  const removedItems = oldArray.filter((item) => !newArray.includes(item))
  return {
    addedItems,
    removedItems,
  }
}

export default {
  components: {
    ArrowAnimation,
  },
  data() {
    return {
      treeData: [],
      defaultCheckedArr: [],
      loadQuantity: 10, // 每次点击加载的数量
      defaultShowQuantity: 20, // 默认展示的数量
      defaultProps: {
        children: "children",
        label: "label",
      },
      dialogFormVisible: false, // 弹框状态
      saveDbStructureData: {
        catalogName: "", //	CATALOG名称
        chooseResult: 0, // 0/1 0有且，1不包含
        dataSourceId: "",
        databaseName: "",
        schemaName: "",
        sysCode: "",
        sysName: "",
        tableNames: [],
      },
      filterText: "",
      moreNode: {
        id: -1,
        label: "加载更多",
        disabled: true,
      },
      savedStructureData: [], // 已保存的表结构数据
      curSelectedTableData: [], // 当前选中的表
      queryLeftTree: {
        // 查询左侧树数据
        dataSourceId: "",
      },
      savedStructuresPromise: "",
      leftTreePromise: "",
      preLength: 0,
      treeCopyAllData: [],
      treeAllData: [],
      tableChildNodes: [],
      residueTableNodes: [],
      viewChildNodes: [],
      residueViewNodes: [],
      flatArr: [],
      isLoading: false,
      isSelLoading: false,
    }
  },
  props: {
    row: {
      type: Object,
    },
  },
  methods: {
    // 递归根据右侧已选表生成默认选中id数组
    lookForAllId(data = [], arr = []) {
      for (let item of data) {
        arr.push(item)
        if (item.children && item.children.length)
          this.lookForAllId(item.children, arr)
      }
      return arr
    },
    // 点击复选框
    handlerChecked(val1, val2) {
      val2.checkedNodes = val2.checkedNodes.filter((item) => item.pid > 2) // 过滤当全选时非table或非view里面的非法表格数据
      this.curSelectedTableData = [
        ...new Set(val2.checkedNodes.map((item) => item.label)),
      ]
      // 向右侧添加
      this.savedStructureData = [
        ...new Set([...this.curSelectedTableData, ...this.savedStructureData]),
      ]
    },
    // 点击复选框 删除右侧元素
    handleCheckChange(val1, val2) {
      if (val2 === false) {
        let index = this.savedStructureData.indexOf(val1.label)
        if (index > -1) {
          this.savedStructureData.splice(index, 1)
        }
      }
    },
    // 删除已添加表标签
    handleClose(index) {
      this.savedStructureData.splice(index, 1)
      // 设置默认选中
      this.setDefaultSelect()
    },
    // 设置默认选中
    setDefaultSelect() {
      this.flatArr = this.lookForAllId(this.treeData)
      this.defaultCheckedArr = this.flatArr
        .filter((item) => this.savedStructureData.includes(item.label))
        .map((item) => item.id)
      this.$nextTick(() => {
        this.$refs.tree.setCheckedKeys(this.defaultCheckedArr)
      })
    },
    // 获取左侧树列表数据
    getLeftTreeList() {
      this.treeData = [] // 先置空树
      this.isLoading = true
      this.leftTreePromise = getLeftTree(this.queryLeftTree)
        .then((res) => {
          if (res.status === 0) {
            if (res.data.length === 0) {
              this.isLoading = false
              this.treeData = []
            } else {
              this.treeAllData = res.data
              this.treeCopyAllData = JSON.parse(
                JSON.stringify(this.treeAllData)
              )
              this.treeAllData = res.data
              this.handlerLoadMoreNode(this.treeAllData)
              this.setDefaultSelect()
              this.isLoading = false
            }
          }
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    // 重置
    reset() {
      this.filterText = ""
      this.handlerTreeQuery()
    },
    // 模糊查询
    handlerTreeQuery() {
      let filterNodes = []
      filterNodes = JSON.parse(
        JSON.stringify(filterTreeByLabel(this.treeCopyAllData, this.filterText))
      )
      this.handlerLoadMoreNode(filterNodes)
    },

    // 处理表根视图子节点加载更多
    handlerLoadMoreNode(treeAllData) {
      // 处理表
      const tableNodes = findItemByLabel(treeAllData, "TABLE")
      if (tableNodes) {
        this.tableChildNodes = tableNodes.children
        if (this.tableChildNodes && this.tableChildNodes.length > 0) {
          const length = this.tableChildNodes.length
          this.residueTableNodes = this.tableChildNodes.splice(
            this.defaultShowQuantity
          )
          if (length > this.defaultShowQuantity) {
            this.tableChildNodes.push(this.moreNode)
          }
        }
      }
      // 处理视图
      const viewNodes = findItemByLabel(treeAllData, "VIEW")
      if (viewNodes) {
        this.viewChildNodes = findItemByLabel(treeAllData, "VIEW").children
        if (this.viewChildNodes && this.viewChildNodes.length > 0) {
          const length = this.viewChildNodes.length
          this.residueViewNodes = this.viewChildNodes.splice(
            this.defaultShowQuantity
          )
          if (length > this.defaultShowQuantity) {
            this.viewChildNodes.push(this.moreNode)
          }
        }
      }

      this.treeData = treeAllData
      this.setDefaultSelect()
    },
    // 获取已保存结构
    getSavedStructures() {
      this.isSelLoading = true
      this.savedStructureData = []
      this.savedStructuresPromise = querySavedStructures({
        dataSourceId: this.row.dataSourceId,
      }).then((res) => {
        if (res.status === 0) {
          res.data.forEach((item, index) => {
            this.savedStructureData.push(item.tableName)
          })
        }
        this.isSelLoading = false
      })
    },
    // 处理加载更多图标
    getIconClass(node) {
      if (node.level == 4 && node.label == "加载更多") {
        return "el-icon-arrow-down"
      }
    },
    // 节点被点击时
    handlerNodeClick(node, Node) {
      if (node.label == "加载更多") {
        if (Node.parent.label == "TABLE") {
          this.$refs.tree.remove(Node)
          const curNodes = this.residueTableNodes.splice(0, this.loadQuantity)
          if (this.residueTableNodes.length > this.loadQuantity) {
            this.tableChildNodes.push(...curNodes, this.moreNode)
          } else {
            this.tableChildNodes.push(...curNodes)
          }
        } else {
          this.$refs.tree.remove(Node)
          const curNodes = this.residueViewNodes.splice(0, this.loadQuantity)
          if (this.residueViewNodes.length > this.loadQuantity) {
            this.viewChildNodes.push(...curNodes, this.moreNode)
          } else {
            this.viewChildNodes.push(...curNodes)
          }
        }
        this.setDefaultSelect()
      }
    },
    // 同步数据结构
    synchronousDataStructure() {
      this.isLoading = true
      synchronousDataStructure({ dataSourceId: this.row.dataSourceId }).then(
        (res) => {
          if (res.status !== 0) {
            this.$message({
              message: res.msg,
              type: "error",
            })
            this.isLoading = false
            return
          }
          this.$message({
            type: "success",
            message: "同步成功",
          })
          this.getLeftTreeList()
        }
      )
    },
    // 保存结构
    saveStructure() {
      this.saveDbStructureData.dataSourceId = this.row.dataSourceId
      this.saveDbStructureData.tableNames = this.savedStructureData
      saveDbStructure(this.saveDbStructureData).then((res) => {
        if (res.status === 0) {
          this.$message({
            type: "success",
            message: "保存成功",
          })
          this.getSavedStructures()
        }
      })
    },
    // 当diolog打开时渲染表格数据及下拉数据
    async handlerOpen() {
      this.savedStructureData = []
      this.queryLeftTree.dataSourceId = this.row.dataSourceId
      this.getSavedStructures()
      this.getLeftTreeList()
      // 异步请求都完成后再设置默认选中
      await Promise.all([this.savedStructuresPromise, this.leftTreePromise])
      this.setDefaultSelect()
    },
    // Dialog 关闭动画结束时的回调,直接销毁弹窗组件，节省性能开销
    handlerClose() {
      this.$emit("dialogChange")
    },
  },
}
</script>

<style lang="scss" scoped>
.structure-config-container {
  ::v-deep.el-dialog {
    background-color: rgb(246, 246, 246);
  }
  .structure-config-header {
    padding-left: 10px;
  }
  .structure-config-main {
    display: flex;
    height: 600px;
    .left {
      // width: 500px;
      position: relative;
      min-width: 400px;
      margin-left: 20px;
      .search {
        margin-bottom: 10px;
      }
      .loading {
        width: 100%;
        height: 500px;
        position: absolute;
      }
      .tree-structure {
        height: 500px;
        overflow: auto;
        padding: 6px 10px;
        background-color: #fff;
        .tree-itemLabel {
          margin-left: 8px;
          &:hover {
            color: #409eff;
          }
        }
      }
      .button {
        margin-top: 20px;
      }
    }
    .right {
      flex: 1;
      margin-left: 20px;
      margin-right: 20px;
      .el-card {
        height: 70%;
        overflow: auto;
        .el-tag {
          margin-right: 14px;
          margin-bottom: 14px;
        }
      }
      .set-relationship {
        margin-top: 10px;
      }
      .save {
        margin-top: 10px;
      }
    }
  }
}
::v-deep.structure-config-container .el-dialog {
  /* height: 80vh; */
  top: -70px;
}
</style>
