import { queryAllSysConfig } from "@/api/sys-config"
const mixin = {
  data() {
    return {
      lastTime: null, // 鼠标最后一次点击时间
      currentTime: null, // 当前时间
      autoLogout: null, //自动登出时间间隔
      timeOutId: null,  // 定时器
      countdown: 10 // 倒计时
    }
  },
  methods: {
    enableAutoLogout() {
      if (localStorage.getItem('timeOutId')){
        clearTimeout(localStorage.getItem('timeOutId'))
        localStorage.removeItem('timeOutId')
      }
      window.document.onmousemove = null
      this.currentTime = new Date().getTime();
      this.lastTime = new Date().getTime();
      this.queryAllSysConfig()
    },
    // 间隔特定时间检测系统无操作时间
    onload() {
      this.timeOutId = window.setTimeout(this.checkTimeOut, this.autoLogout)
      localStorage.setItem('timeOutId', JSON.stringify(this.timeOutId))
    },
    // 判断系统无操作时间是否大于自动登出时间
    async checkTimeOut() {
      this.currentTime = new Date().getTime();
      if (this.currentTime - this.lastTime > this.autoLogout) {
        // window.removeEventListener('mousemove', this.handlerMoveEvent)
        window.document.onmousemove = null
        await this.$store.dispatch("user/logout")
        this.$router.push("/login")
        clearTimeout(this.timeOutId) // 清除定时器
        this.$message({
          type: "warning",
          message: "系统暂无操作，请重新登录"
        })
      }
    },
    handlerMoveEvent() {
      if (this.timeOutId) {
        clearTimeout(this.timeOutId)
      }
      this.lastTime = new Date().getTime();
      this.onload()
    },
    // 获取自动登出时间间隔
    async queryAllSysConfig() {
      let res = await queryAllSysConfig()
      if (res.status !== 0) {
        this.$message({
          type: "error",
          message: res.msg,
        })
        return
      }
      for (let key in res.data) {
        if (key === "auto.logout.time") {
          this.autoLogout = Number(res.data[key]) * 60 * 1000
          // 监听鼠标移动时间，获取最后一次移动的时间
          window.document.onmousemove = this.handlerMoveEvent
          // window.addEventListener('mousemove', this.handlerMoveEvent)
        }
      }
    },
  }
}
export default mixin
