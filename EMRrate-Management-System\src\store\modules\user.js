import { login, logout, getInfo, singleLogin } from "@/api/user"
import { getToken, setToken, removeToken, setSingleToken } from "@/utils/auth"
import { Message } from "element-ui"
import { resetRouter } from "@/router"
import _ from "lodash"

//深度优先遍历，获取所有节点
function DFS(arr) {
  let nodes = []
  let noop = (tArray) => {
    if (tArray && Array.isArray(tArray)) {
      tArray.forEach((ele) => {
        nodes.push(ele)
        if ("children" in ele) {
          noop(ele.children)
        }
      })
    }
  }
  noop(arr)
  return nodes
}

const getDefaultState = () => {
  return {
    token: getToken(),
    name: "",
    avatar: "",
    id: "",
    loginId: "",
    pgPermissionKeys: [],
    btPermissionKeys: [],
    isDownload:false,
    isDownloadmgt:false,
    projectMsg: {},
    addproject:''
  }
}

const state = getDefaultState()
const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState())
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_ID: (state, id) => {
    state.id = id
  },
  SET_LOGINID: (state, loginId) => {
    state.loginId = loginId
  },

  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_PAGE_PERMISSION: (state, pgPermissionKeys) => {
    state.pgPermissionKeys = pgPermissionKeys
  },
  SET_BUTTON_PERMISSION: (state, btPermissionKeys) => {
    state.btPermissionKeys = btPermissionKeys
  },
  SET_ISDOWNLOAD:(state,isDownload)=>{
    state.isDownload = isDownload
  },
  SET_ISDOWNLOADMGT:(state,isDownloadmgt)=>{
    state.isDownloadmgt = isDownloadmgt
  },
  SET_PROJECTMSG:(state,SET_PROJECTMSG)=>{
    state.projectMsg = SET_PROJECTMSG
  },
  SET_ADDPROJECT:(state,SET_ADDPROJECT)=>{
    state.addproject = SET_ADDPROJECT
  }
}

const actions = {
  // user login
  login({ commit }, userInfo) {
    const {
      username,
      authenticationType,
      password,
      verificationCode,
      phoneNumber,
      identifyType,
      uuid,
    } = userInfo
    return new Promise((resolve, reject) => {
      login({
        loginId: username.trim(),
        password: password,
        identifyType,
        authenticationType,
        uuid,
        verificationCode,
        phoneNumber,
      })
        .then((response) => {
          if (
            response.msg == "ADD_ACCOUNT_CODE" ||
            response.msg == "ADD_ACCOUNT_PASSWORD"
          ) {
            reject(response.msg)
            return
          } else if (response.msg == "CHOOSE_ACCOUNT_PASSWORD") {
            Message.error("请密码登录")
            reject(response.msg)
            return
          } else if (response.msg == "CHOOSE_ACCOUNT_CODE") {
            Message.error("请短信登录")
            reject(response.msg)
            return
          } else if (response.status != 0) {
            Message.error(response.msg)
            reject(response.msg)
            return
          }
          const { data } = response
          commit("SET_TOKEN", data)
          setToken(data)
          resolve()
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
  singleLogin({ commit }, singleToken) {
    return new Promise((resolve, reject) => {
      setSingleToken(singleToken)
      singleLogin(singleToken)
        .then((response) => {
          if (response.status != 0) {
            Message.error(response.msg)
            return reject(response.msg)
          }
          const { data } = response
          commit("SET_TOKEN", data)
          setToken(data)
          console.log(getToken(),'chongxin')
          resolve()
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  // get user info
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getInfo(state.token).then((response) => {
        const { data } = response
        if (!data) {
          return reject("用户信息获取失败，请稍后重试！")
        }
        const { list, userName, userId, loginId } = data
        // 角色列表 must be a non-empty array
        if (!list || list.length <= 0) {
          return reject('用户必须要有一个角色!')
        }
        //将所有角色的权限ru成一坨，然后再去重，提取页面的权限和按钮权限
        let repeatedPermissionIds = []
        list.forEach((v) => {
          if (v.permissionIds && Array.isArray(v.permissionIds)) {
            repeatedPermissionIds = repeatedPermissionIds.concat(
              v.permissionIds
            )
          }
        })
        let flatRepeatedPermissionIds = DFS(repeatedPermissionIds)
        let flatPermissionKeys = flatRepeatedPermissionIds.map(
          (v) => v.description
        )
        //去重   筛选按钮和界面权限
        let [pgPermissionKeys, btPermissionKeys] = [
          ...new Set(flatPermissionKeys),
        ].reduce(
          (pre, cur, index) => {
            if (cur.indexOf(":view") != -1) {
              //界面权限
              pre[0].push(cur)
            } else if (cur.indexOf(":") != -1) {
              //按钮权限
              pre[1].push(cur)
            } else {
              pre[0].push(cur)
            }
            return pre
          },
          [[], []]
        )
        // console.log("@@@@",pgPermissionKeys,btPermissionKeys)
        commit("SET_PAGE_PERMISSION", pgPermissionKeys)
        commit("SET_BUTTON_PERMISSION", btPermissionKeys)
        commit("SET_NAME", userName)
        commit("SET_ID", userId)
        commit("SET_LOGINID", loginId)
        resolve({ pgPermissionKeys })
      })
    })
  },

  // user logout
  logout({ commit, state }) {
    return new Promise((resolve, reject) => {
      console.log(112233)
      logout(getToken())
        .then(() => {
          removeToken() // must remove  token  first
          resetRouter()
          // console.log(123)
          commit("RESET_STATE")
          resolve()
        })
        .catch((error) => {
          console.log(error)
          reject(error)
        })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise((resolve) => {
      removeToken() // must remove  token  first
      commit("RESET_STATE")
      resolve()
    })
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
