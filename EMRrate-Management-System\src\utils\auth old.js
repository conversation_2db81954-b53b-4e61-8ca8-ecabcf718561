const TokenKey = 'system_token'
const SingleTokenKey='system_single_token'
import store from '@/store'

export function getToken() {
  return  localStorage.getItem(TokenKey)
}

export function setToken(token) {
  return localStorage.setItem(TokenKey, token)
}

export function removeToken() {
  return localStorage.removeItem(TokenKey)
}


export function getSingleToken() {
  return  localStorage.getItem(SingleTokenKey)
}

export function setSingleToken(token) {
  return localStorage.setItem(SingleTokenKey, token)
}

export function removeSingleToken() {
  return localStorage.removeItem(SingleTokenKey)
}


export function checkPermission(key){
  const {btPermissionKeys}=store.getters
  if(btPermissionKeys.length==0){
    return false;
  }
  return btPermissionKeys.indexOf(key)!=-1
}
