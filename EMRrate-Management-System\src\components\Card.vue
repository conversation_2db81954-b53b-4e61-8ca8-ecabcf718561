<template>
  <div class="card">
    <div class="icon-title" :style="cardStyle">
      <span :class="icon"></span>
      <span>{{ text }}</span>
    </div>
    <div class="count">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    shadowColor: String,
    icon: String,
    text: String,
    color: String,
    count: Number,
  },
  computed: {
    cardStyle: function () {
      return {
        "box-shadow": `0px 0px 5px ${this.shadowColor}`,
        color: this.color,
      };
    },
  },
};

</script>

<style scoped lang="scss">
.card {
  height: 125px;
  border-radius: 8px;
  background-size: cover;
  position: relative;
  overflow: hidden;
  text-align: center;
  transform-origin: center center;
  transition-duration: 1000ms;
  transition-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
  .icon-title {
    position: absolute;
    left: -22px;
    top: 10px;
    padding: 0 30px;
    border-radius: 30px;
    background-color: #ffffff;
    > span {
      line-height: 25px;
      font-weight: bold;
    }
    > span:nth-child(1) {
      margin-right: 5px;
    }
  }
  .count {
    padding-top: 50px;
    font-weight: bold;
    text-align: center;
    font-size: 30px;
    color: #ffffff;
  }
  &:hover {
    transform: rotateX(10deg) rotateY(2deg) translate3d(10px, 0, 40px);
  }
}
</style>