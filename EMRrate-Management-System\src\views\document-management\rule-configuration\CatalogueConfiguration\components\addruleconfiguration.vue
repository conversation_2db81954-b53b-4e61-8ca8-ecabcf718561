<template>
  <el-dialog
    v-dialogDrag
    :visible.sync="dialogFormVisible"
    :close-on-click-modal="false"
    width="950px"
    :show-close="false"
  >
    <div class="outsidedialog">
      <div class="dialog-new-title">
        <h4>{{ savetype }}质量数据项目</h4>
        <i class="el-icon-close" @click="handlerClose()"></i>
      </div>
      <div class="dialog-container">
        <!-- 左侧菜单 -->
        <div class="dialog-container-left">
          <div
            @click="changemenu(0)"
            :class="activemenu === 0 ? 'activeitem' : ''"
          >
            数据项目名称
          </div>
          <div
            @click="changemenu(1)"
            :class="activemenu === 1 ? 'activeitem' : ''"
          >
            表与字段名
          </div>
          <div
            v-show="formData.needStatistics === '0'"
            @click="changemenu(2)"
            :class="activemenu === 2 ? 'activeitem' : ''"
          >
            记录数 (SQL)
          </div>
          <div
            v-show="formData.needStatistics === '0'"
            @click="changemenu(3)"
            :class="activemenu === 3 ? 'activeitem' : ''"
          >
            SQL说明
          </div>
        </div>
        <!-- 右侧表单 -->
        <div class="dialog-container-right">
          <el-form label-width="200px" :rules="ruless">
            <div v-show="activemenu === 0">
              <div class="title01 title">
                {{ formData.directoryCode }} {{ formData.directoryName }}
                <span :style="{ color: '#5270DD' }">
                  {{ formData.emrRuleType }}
                </span>
              </div>

              <el-form-item label="要求项目：" prop="requiredProject">
                <el-input
                  placeholder="请输入要求项目"
                  v-model="formData.requiredProject"
                ></el-input>
              </el-form-item>
              <el-form-item label="医院项目：" prop="hospitalProject">
                <el-input
                  placeholder="请输入医院项目"
                  v-model="formData.hospitalProject"
                ></el-input>
              </el-form-item>
              <el-form-item label="跨库查询：">
                <el-switch
                  v-model="formData.whetherCrossDbQuery"
                  active-color="#5270DD"
                  active-value="1"
                  inactive-value="0"
                >
                </el-switch>
              </el-form-item>
              <el-form-item
                label="跨库数据源："
                v-show="formData.whetherCrossDbQuery === '1'"
              >
                <el-select
                  v-model="formData.crossDbQueryDataSourceId"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in datasourcelist1"
                    :key="item.dataSourceId"
                    :label="item.sysCode"
                    :value="String(item.dataSourceId)"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="统计项：">
                <el-switch
                  v-model="formData.needStatistics"
                  active-color="#5270DD"
                  active-value="0"
                  inactive-value="1"
                >
                </el-switch>
              </el-form-item>
            </div>
            <div v-show="activemenu === 1">
              <div class="customization">
                是否自定义输入:
                <el-switch
                  v-model="formData.tableAndFiledType"
                  active-color="#5270DD"
                  active-value="1"
                  inactive-value="0"
                >
                </el-switch>
              </div>
              <div
                class="title02 title"
                v-show="
                  formData.emrRuleType === '一致性' ||
                  formData.emrRuleType === '完整性'
                "
              >
                数据库表与字段名
              </div>
              <div
                class="title02 title"
                v-show="
                  formData.emrRuleType === '及时性' ||
                  formData.emrRuleType === '整合性'
                "
              >
                {{ tableData.headerName1 + "记录表与字段名" }}
              </div>
              <div class="container02">
                <div class="left">
                  <h5>数据源</h5>
                  <el-form-item label-width="0px">
                    <el-select
                      v-model="formData.dataSourceId"
                      filterable
                      placeholder="请选择"
                      @change="querytablename()"
                    >
                      <el-option
                        v-for="item in datasourcelist"
                        :key="item.dataSourceId"
                        :label="item.sysCode"
                        :value="String(item.dataSourceId)"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <h5 v-show="formData.tableAndFiledType === '0'">
                    数据库表名
                  </h5>
                  <el-form-item
                    label-width="0px"
                    v-show="formData.tableAndFiledType === '0'"
                  >
                    <el-select
                      v-model="formData.structureName1"
                      filterable
                      placeholder="请选择"
                      @change="
                        querygetRightStructures(1, formData.structureName1)
                      "
                    >
                      <el-option
                        v-for="item in structureNamelist"
                        :key="item"
                        :label="item"
                        :value="String(item)"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </div>
                <div class="right">
                  <h5>选择字段名</h5>

                  <div
                    class="container02"
                    v-show="formData.tableAndFiledType === '1'"
                  >
                    <el-input
                      type="textarea"
                      :rows="10"
                      placeholder="请输入内容"
                      v-model="formData.tableFieldName1"
                    >
                    </el-input>
                  </div>
                  <el-table
                    v-show="formData.tableAndFiledType === '0'"
                    width="110%"
                    height="200"
                    border
                    ref="table1"
                    :data="tableFieldNamelist1"
                    :header-cell-class-name="cellClass"
                    @select="handleSelect1"
                  >
                    <el-table-column type="selection" width="55">
                    </el-table-column>
                    <el-table-column label="字段名" prop="name" min-width="120">
                      <template slot-scope="scope">
                        {{ scope.row.name }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="remarks"
                      label="字段中文名"
                      min-width="120"
                    >
                    </el-table-column>
                  </el-table>
                </div>
              </div>
              <div
                class="title02 title"
                v-show="formData.emrRuleType === '一致性'"
              >
                数据库字典表与字段名
              </div>
              <div
                class="title02 title"
                v-show="formData.emrRuleType === '整合性'"
              >
                {{ tableData.headerName2 + "记录表与字段名" }}
              </div>
              <div
                class="container02"
                v-show="
                  formData.emrRuleType === '整合性' ||
                  formData.emrRuleType === '一致性'
                "
              >
                <div class="left">
                  <h5>数据源</h5>
                  <el-form-item label-width="0px">
                    <el-select
                      v-model="formData.dataSourceId2"
                      filterable
                      placeholder="请选择"
                      @change="querytablename2()"
                    >
                      <el-option
                        v-for="item in datasourcelist"
                        :key="item.dataSourceId"
                        :label="item.sysCode"
                        :value="String(item.dataSourceId)"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <h5 v-show="formData.tableAndFiledType === '0'">
                    数据库表名
                  </h5>
                  <el-form-item
                    label-width="0px"
                    v-show="formData.tableAndFiledType === '0'"
                  >
                    <el-select
                      v-model="formData.structureName2"
                      filterable
                      placeholder="请选择"
                      @change="
                        querygetRightStructures2(2, formData.structureName2)
                      "
                    >
                      <el-option
                        v-for="item in structureNamelist2"
                        :key="item"
                        :label="item"
                        :value="String(item)"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </div>
                <div class="right">
                  <h5>选择字段名</h5>
                  <div
                    class="container02"
                    v-show="formData.tableAndFiledType === '1'"
                  >
                    <el-input
                      type="textarea"
                      :rows="10"
                      placeholder="请输入内容"
                      v-model="formData.tableFieldName2"
                    >
                    </el-input>
                  </div>
                  <el-table
                    width="110%"
                    height="200"
                    border
                    ref="table2"
                    :data="tableFieldNamelist2"
                    :header-cell-class-name="cellClass"
                    @selection-change="handleSelect2"
                    v-show="formData.tableAndFiledType === '0'"
                  >
                    <el-table-column type="selection" width="55">
                    </el-table-column>
                    <el-table-column label="字段名" prop="name" min-width="120">
                    </el-table-column>
                    <el-table-column
                      prop="remarks"
                      label="字段中文名"
                      min-width="120"
                    >
                    </el-table-column
                  ></el-table>
                </div>
              </div>
            </div>
            <div v-show="activemenu === 2">
              <div
                class="title02 title"
                v-show="
                  formData.emrRuleType === '一致性' ||
                  formData.emrRuleType === '完整性'
                "
              >
                记录总数 T
              </div>
              <div
                class="title02 title"
                v-show="
                  formData.emrRuleType === '及时性' ||
                  formData.emrRuleType === '整合性'
                "
              >
                {{ tableData.headerName1 + "记录数T（SQL语句）" }}
              </div>
              <div class="editdataSql">
                <span class="sqlyuju">SQL语句</span>
                <el-input
                  type="textarea"
                  :rows="8"
                  placeholder="请输入内容"
                  v-model="formData.recordsSql"
                >
                </el-input>
                <div class="editdataSqlbutton">
                  <span>
                    <el-button
                      size="mini"
                      icon="el-icon-date"
                      type="text"
                      @click="getTimeConditionSql('recordsSql')"
                      >插入时间</el-button
                    >
                    <el-divider direction="vertical"></el-divider>
                    <el-dropdown
                      :hide-on-click="false"
                      @command="handleCommand"
                    >
                      <el-button type="text" size="mini">
                        查询{{ recordsSqltime }}小时<i
                          class="el-icon-arrow-down el-icon--right"
                        ></i>
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item :command="1"
                          >最近1小时</el-dropdown-item
                        >
                        <el-dropdown-item :command="2"
                          >最近2小时</el-dropdown-item
                        >
                        <el-dropdown-item :command="3"
                          >最近3小时</el-dropdown-item
                        >
                        <el-dropdown-item :command="4"
                          >最近4小时</el-dropdown-item
                        >
                        <el-dropdown-item :command="5"
                          >最近5小时</el-dropdown-item
                        >
                        <el-dropdown-item :command="null"
                          >自定义<el-input
                            size="mini"
                            v-model="recordsSqltime"
                            style="width: 60px; padding: 0px"
                          ></el-input
                        ></el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                    <el-divider direction="vertical"></el-divider>
                    <el-button
                      size="mini"
                      type="text"
                      @click="
                        execute(
                          formData.recordsSql,
                          'recordsSql',
                          recordsSqltime
                        )
                      "
                    >
                      语法检查
                    </el-button>
                  </span>
                  <span class="testresult">
                    测试执行结果：时间范围为近
                    {{ recordsSqltime }}
                    小时，共
                    {{ recordsSqlCount }}条数据
                  </span>
                </div>
              </div>
              <div
                class="title02 title"
                v-show="
                  formData.emrRuleType === '一致性' ||
                  formData.emrRuleType === '完整性'
                "
              >
                有对照记录数 C
              </div>
              <div
                class="title02 title"
                v-show="formData.emrRuleType === '整合性'"
              >
                {{ tableData.headerName2 + "可关联对照记录数L（SQL语句）" }} C
              </div>

              <div class="editdataSql">
                <span class="sqlyuju">SQL语句</span>
                <el-input
                  type="textarea"
                  :rows="8"
                  placeholder="请输入内容"
                  v-model="formData.conditionalRecordsSql"
                >
                </el-input>
                <div class="editdataSqlbutton">
                  <span>
                    <el-button
                      size="mini"
                      icon="el-icon-date"
                      type="text"
                      @click="getTimeConditionSql2('conditionalRecordsSql')"
                      >插入时间</el-button
                    >
                    <el-divider direction="vertical"></el-divider>
                    <el-dropdown
                      :hide-on-click="false"
                      @command="handleCommands"
                    >
                      <el-button type="text" size="mini">
                        查询{{ conditionalRecordstime }}小时<i
                          class="el-icon-arrow-down el-icon--right"
                        ></i>
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item :command="1"
                          >最近1小时</el-dropdown-item
                        >
                        <el-dropdown-item :command="2"
                          >最近2小时</el-dropdown-item
                        >
                        <el-dropdown-item :command="3"
                          >最近3小时</el-dropdown-item
                        >
                        <el-dropdown-item :command="4"
                          >最近4小时</el-dropdown-item
                        >
                        <el-dropdown-item :command="5"
                          >最近5小时</el-dropdown-item
                        >
                        <el-dropdown-item :command="null"
                          >自定义<el-input
                            size="mini"
                            v-model="conditionalRecordstime"
                            style="width: 60px; padding: 0px"
                          ></el-input
                        ></el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                    <el-divider direction="vertical"></el-divider>

                    <el-button
                      v-if="formData.emrRuleType === '完整性'"
                      size="mini"
                      type="text"
                      @click="
                        execute(
                          formData.conditionalRecordsSql,
                          'conditionalRecordsSql',
                          conditionalRecordstime
                        )
                      "
                    >
                      语法检查
                    </el-button>

                    <el-button
                      v-else
                      size="mini"
                      type="text"
                      @click="
                        execute2(
                          formData.conditionalRecordsSql,
                          'conditionalRecordsSql',
                          conditionalRecordstime
                        )
                      "
                    >
                      语法检查
                    </el-button>
                  </span>
                  <span class="testresult">
                    测试执行结果：时间范围为 近
                    {{ conditionalRecordstime }}
                    小时，共
                    {{ conditionalRecordsSqlCount }} 条数据
                  </span>
                </div>
              </div>
            </div>

            <!-- SQL说明页面 -->
            <div v-show="activemenu === 3">
              <div
                class="title02 title"
                v-show="
                  formData.emrRuleType === '一致性' ||
                  formData.emrRuleType === '完整性'
                "
              >
                记录总数 T
              </div>
              <div
                class="title02 title"
                v-show="
                  formData.emrRuleType === '及时性' ||
                  formData.emrRuleType === '整合性'
                "
              >
                {{ tableData.headerName1 + "记录数T" }}
              </div>
              <div class="editdataSql">
                <span class="sqlyuju">SQL说明</span>
                <el-input
                  type="textarea"
                  :rows="8"
                  placeholder="请输入SQL说明"
                  v-model="formData.recordsSqlDesc"
                  maxlength="1000"
                  show-word-limit
                >
                </el-input>
              </div>

              <div
                class="title02 title"
                v-show="
                  formData.emrRuleType === '一致性' ||
                  formData.emrRuleType === '完整性'
                "
              >
                有对照记录数 C
              </div>
              <div
                class="title02 title"
                v-show="formData.emrRuleType === '整合性'"
              >
                {{ tableData.headerName2 + "可关联对照记录数L" }}
              </div>
              <div
                class="title02 title"
                v-show="formData.emrRuleType === '及时性'"
              >
                {{ tableData.headerName2 + "完整记录数N" }}
              </div>

              <div class="editdataSql">
                <span class="sqlyuju">SQL说明</span>
                <el-input
                  type="textarea"
                  :rows="8"
                  :placeholder="getConditionalSqlDescPlaceholder()"
                  v-model="formData.conditionalRecordsSqlDesc"
                  maxlength="1000"
                  show-word-limit
                >
                </el-input>
              </div>
            </div>
          </el-form>
        </div>
      </div>
    </div>
    <!-- 底部取消，保存按钮 -->
    <div slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handlerClose()">取 消</el-button>
      <el-button
        size="mini"
        type="primary"
        @click="savedocumentRuleConfiguration()"
        >保存</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import {
  getStructureInfo,
  datasourcelist,
  getRightStructures,
  getTimeConditionSql,
  execute,
  savedocumentRuleConfiguration,
} from "@/api/document-management/rule-configuration";
export default {
  data() {
    return {
      dialogFormVisible: false, // 弹框状态
      activemenu: 1, //激活菜单
      dbType: "", //数据库类型
      datasourcelist1: [], //数据源列表
      datasourcelist: [], //数据源列表
      structureNamelist: [], //数据库表名
      structureNamelist2: [], //数据库表名2
      tableFieldNamelist1: [], //字段名列表1
      tableFieldNamelist2: [], //字段名列表2
      recordsSqltime: 1, //测试时间1
      conditionalRecordstime: 1, //测试时间2
      recordsSqlCount: 0, //测试获得数据1
      conditionalRecordsSqlCount: 0, //测试获得数据2
      tableData: {}, //外部表格信息
      formData: {
        //表单信息
        directoryCode: "",
        directoryName: "",
        dataSourceId: "",
        dataSourceId2: "",
        emrRuleType: "",
        requiredProject: "",
        hospitalProject: "",
        whetherCrossDbQuery: "0",
        crossDbQueryDataSourceId: "",
        structureName1: "",
        structureName2: "",
        tableFieldName1: "",
        tableFieldName2: "",
        headerName1: "",
        headerName2: "",
        recordsSql: "",
        conditionalRecordsSql: "",
        needStatistics: "0",
        tableAndFiledType: "0",
      },
      ruless: {
        requiredProject: [
          { required: true, message: "请输入要求项目", trigger: "blur" },
        ],
        hospitalProject: [
          { required: true, message: "请输入医院项目", trigger: "blur" },
        ],
      },
      savetype: "新增",
    };
  },
  props: {
    selectedProject: {
      type: Object, // 根据实际情况调整类型
      required: true,
    },
    queryRightTable: {
      type: Object,
    },
  },
  methods: {
    // 打开弹窗
    async opendialogFormVisible(type, data, tableData, emrRuleType) {
      // 查询
      this.changemenu(type < 5 ? type : 0);
      await datasourcelist({
        whetherFilterCrossDb: 1,
        pageNum: 1,
        pageSize: 9999,
      }).then((res) => {
        if (res.status === 0) {
          this.datasourcelist = res.data.list;
        }
      });
      if (type === 0) {
        this.savetype = "新增";
        this.formData.directoryCode = data.directoryCode;
        this.formData.directoryName = data.directoryName;
        this.formData.emrRuleType = data.emrRuleType;
        this.tableData = JSON.parse(JSON.stringify(tableData));
      } else if (type !== 0) {
        this.savetype = "编辑";
        this.tableData = JSON.parse(JSON.stringify(tableData));
        this.formData = JSON.parse(JSON.stringify(data));
        this.formData.emrRuleType = emrRuleType;
        await getStructureInfo({
          dataSourceId: this.formData.dataSourceId2,
        }).then((res) => {
          res.data.length > 0
            ? (this.structureNamelist2 = res.data[0].tables)
            : (this.structureNamelist2 = []);
          if (Boolean(this.formData.structureName2)) {
            this.querygetRightStructures2(2, this.formData.structureName2);
          }
        });
        await getStructureInfo({
          dataSourceId: this.formData.dataSourceId,
        }).then((res) => {
          res.data.length > 0
            ? (this.structureNamelist = res.data[0].tables)
            : (this.structureNamelist = []);
          if (Boolean(this.formData.structureName1)) {
            this.querygetRightStructures(1, this.formData.structureName1);
          }
        });
      }
      this.dialogFormVisible = true;
    },
    changemenu(data) {
      this.activemenu = data;
      if (this.activemenu === 0) {
        return this.querydatasourcelist("2");
      } else if (this.activemenu === 1) {
        // return this.querydatasourcelist("1");
        return;
      }
      return Promise.resolve(); // 如果 activemenu 不为 0 或 1，则返回一个已解决的 Promise
    },
    // 查询 数据源
    querydatasourcelist(type) {
      datasourcelist({
        whetherFilterCrossDb: type,
        pageNum: 1,
        pageSize: 9999,
      }).then((res) => {
        if (res.status === 0) {
          if (type === "1") {
            this.datasourcelist = res.data.list;
          } else if (type === "2") {
            this.datasourcelist1 = res.data.list;
          }
        }
      });
    },
    // 查询数据库表名
    querytablename() {
      this.formData.structureName1 = "";
      // this.formData.structureName2 = "";
      this.tableFieldNamelist1 = [];
      // this.tableFieldNamelist2 = [];
      this.structureName1 = [];
      // this.structureName2 = [];
      getStructureInfo({
        dataSourceId: this.formData.dataSourceId,
      }).then((res) => {
        res.data.length > 0
          ? (this.structureNamelist = res.data[0].tables)
          : (this.structureNamelist = []);
      });
    },

    // 查询数据库表名2
    querytablename2() {
      // this.formData.structureName1 = "";
      this.formData.structureName2 = "";
      // this.tableFieldNamelist1 = [];
      this.tableFieldNamelist2 = [];
      // this.structureName1 = [];
      this.structureName2 = [];
      getStructureInfo({
        dataSourceId: this.formData.dataSourceId2,
      }).then((res) => {
        res.data.length > 0
          ? (this.structureNamelist2 = res.data[0].tables)
          : (this.structureNamelist2 = []);
      });
    },
    // 获取表字段
    async querygetRightStructures(type, data) {
      // await this.changemenu(this.activemenu);
      let databaseSchema = "";
      this.datasourcelist.forEach((item) => {
        if (item.dataSourceId === Number(this.formData.dataSourceId)) {
          databaseSchema = item.databaseSchema;
        }
      });
      await getRightStructures({
        dataSourceId: this.formData.dataSourceId,
        name: data,
        schema: databaseSchema,
        pageNum: 1,
        pageSize: 99999,
      }).then((res) => {
        if (type === 1) {
          this.tableFieldNamelist1 = res.data.list;
          this.$nextTick(() => {
            this.tableFieldNamelist1.forEach((item) => {
              if (item.name === this.formData.tableFieldName1) {
                // 添加安全检查，确保表格引用存在且方法可用
                if (this.$refs.table1 && typeof this.$refs.table1.toggleRowSelection === 'function') {
                  this.$refs.table1.toggleRowSelection(item, true);
                }
              }
            });
          });
        }
      });
    },
    // 获取表字段
    async querygetRightStructures2(type, data) {
      // await this.changemenu(this.activemenu);
      let databaseSchema = "";
      this.datasourcelist.forEach((item) => {
        if (item.dataSourceId === Number(this.formData.dataSourceId2)) {
          databaseSchema = item.databaseSchema;
        }
      });
      await getRightStructures({
        dataSourceId: this.formData.dataSourceId2,
        name: data,
        schema: databaseSchema,
        pageNum: 1,
        pageSize: 99999,
      }).then((res) => {
        if (type === 2) {
          this.tableFieldNamelist2 = res.data.list;
          this.$nextTick(() => {
            this.tableFieldNamelist2.forEach((item) => {
              if (item.name === this.formData.tableFieldName2) {
                // 添加安全检查，确保表格引用存在且方法可用
                if (this.$refs.table2 && typeof this.$refs.table2.toggleRowSelection === 'function') {
                  this.$refs.table2.toggleRowSelection(item, true);
                }
              }
            });
          });
        }
      });
    },
    handleCommand(command) {
      this.recordsSqltime = command;
    },
    handleCommands(command) {
      this.conditionalRecordstime = command;
    },
    // 插入时间
    getTimeConditionSql(type) {
      this.datasourcelist.forEach((element) => {
        if (element.dataSourceId === Number(this.formData.dataSourceId)) {
          this.dbType = element.databaseType;
          return;
        }
      });
      getTimeConditionSql({ dbType: this.dbType }).then((res) => {
        if (type === "recordsSql") {
          if (this.formData.recordsSql.includes(res.data.timeTemplateSql)) {
            this.$message({
              type: "info",
              message: "时间已插入，请勿重复插入",
            });
          } else {
            this.formData.recordsSql =
              this.formData.recordsSql + res.data.timeTemplateSql;
          }
        } else {
          if (
            this.formData.conditionalRecordsSql.includes(
              res.data.timeTemplateSql
            )
          ) {
            this.$message({
              type: "info",
              message: "时间已插入，请勿重复插入",
            });
          } else {
            this.formData.conditionalRecordsSql =
              this.formData.conditionalRecordsSql + res.data.timeTemplateSql;
          }
        }
      });
    },

    // 插入时间
    getTimeConditionSql2(type) {
      this.datasourcelist.forEach((element) => {
        if (element.dataSourceId === Number(this.formData.dataSourceId2)) {
          this.dbType = element.databaseType;
          return;
        }
      });
      getTimeConditionSql({ dbType: this.dbType }).then((res) => {
        if (type === "recordsSql") {
          if (this.formData.recordsSql.includes(res.data.timeTemplateSql)) {
            this.$message({
              type: "info",
              message: "时间已插入，请勿重复插入",
            });
          } else {
            this.formData.recordsSql =
              this.formData.recordsSql + res.data.timeTemplateSql;
          }
        } else {
          if (
            this.formData.conditionalRecordsSql.includes(
              res.data.timeTemplateSql
            )
          ) {
            this.$message({
              type: "info",
              message: "时间已插入，请勿重复插入",
            });
          } else {
            this.formData.conditionalRecordsSql =
              this.formData.conditionalRecordsSql + res.data.timeTemplateSql;
          }
        }
      });
    },
    // 测试
    execute(sql, type, time) {
      execute({
        dataSourceId:
          this.formData.whetherCrossDbQuery === "0"
            ? this.formData.dataSourceId
            : this.formData.crossDbQueryDataSourceId,
        sql: sql,
        offsetHour: time,
      }).then((res) => {
        if (res.status === 0) {
          if ((type === "recordsSql") & (res.data.result.length === 1)) {
            let key = Object.keys(res.data.result[0])[0];
            this.recordsSqlCount = res.data.result[0][key];
            this.$message({
              message: `时间范围为最近${this.recordsSqltime}小时，得到结果为${this.recordsSqlCount}条数据`,
              type: "success",
            });
          } else if (
            (type === "conditionalRecordsSql") &
            (res.data.result.length === 1)
          ) {
            let key = Object.keys(res.data.result[0])[0];
            this.conditionalRecordsSqlCount = res.data.result[0][key];
            this.$message({
              message: `时间范围为最近${this.conditionalRecordstime}小时，得到结果为${this.conditionalRecordsSqlCount}条数据`,
              type: "success",
            });
          } else {
            this.$message({
              type: "success",
              message: res.msg,
            });
          }
        } else {
          this.$message({
            type: "error",
            message: res.msg,
          });
        }
      });
    },
    // 测试
    execute2(sql, type, time) {
      execute({
        dataSourceId:
          this.formData.whetherCrossDbQuery === "0"
            ? this.formData.dataSourceId2
            : this.formData.crossDbQueryDataSourceId,
        sql: sql,
        offsetHour: time,
      }).then((res) => {
        if (res.status === 0) {
          if ((type === "recordsSql") & (res.data.result.length === 1)) {
            let key = Object.keys(res.data.result[0])[0];
            this.recordsSqlCount = res.data.result[0][key];
            this.$message({
              message: `时间范围为最近${this.recordsSqltime}小时，得到结果为${this.recordsSqlCount}条数据`,
              type: "success",
            });
          } else if (
            (type === "conditionalRecordsSql") &
            (res.data.result.length === 1)
          ) {
            let key = Object.keys(res.data.result[0])[0];
            this.conditionalRecordsSqlCount = res.data.result[0][key];
            this.$message({
              message: `时间范围为最近${this.conditionalRecordstime}小时，得到结果为${this.conditionalRecordsSqlCount}条数据`,
              type: "success",
            });
          } else {
            this.$message({
              type: "success",
              message: res.msg,
            });
          }
        } else {
          this.$message({
            type: "error",
            message: res.msg,
          });
        }
      });
    },
    // 保存
    async savedocumentRuleConfiguration() {
      if (this.savetype === "新增") {
        this.tableData.documentRuleConfigurationList.push(this.formData);
      } else {
        this.tableData.documentRuleConfigurationList.forEach((item, index) => {
          if (item.id === this.formData.id) {
            this.tableData.documentRuleConfigurationList[index] = this.formData;
          }
        });
      }
      await savedocumentRuleConfiguration({
        ...this.tableData,
        projectId: this.selectedProject.id,
      }).then((res) => {
        if (res.status === 0) {
          this.$message({
            message: this.savetype + "成功!",
            type: "success",
          });
          let activetree = this.$parent.$refs.tree.getCurrentNode();
          this.backactivetree(this.$parent.treeData, activetree);
          this.handlerClose();
        } else {
          this.$message({
            message: res.msg,
            type: "error",
          });
        }
      });
    },

    backactivetree(datas, activetree) {
      datas.forEach((item) => {
        if (item.id === activetree.id) {
          item.taskStatus = "1";
        } else if (item.children && item.children.length) {
          this.backactivetree(item.children, activetree);
        }
      });
    },
    handleSelect1(selection) {
      //只能选择一行，选择其他，清除上一行
      if (selection.length > 1) {
        let del_row = selection.shift();
        // 添加安全检查，确保表格引用存在且方法可用
        if (this.$refs.table1 && typeof this.$refs.table1.toggleRowSelection === 'function') {
          this.$refs.table1.toggleRowSelection(del_row, false); //设置这一行取消选中
        }
        this.formData.tableFieldName1 = selection[0].name;
      } else if (selection.length === 1) {
        this.formData.tableFieldName1 = selection[0].name;
      }
    },
    handleSelect2(selection) {
      //只能选择一行，选择其他，清除上一行
      if (selection.length > 1) {
        let del_row = selection.shift();
        // 添加安全检查，确保表格引用存在且方法可用
        if (this.$refs.table2 && typeof this.$refs.table2.toggleRowSelection === 'function') {
          this.$refs.table2.toggleRowSelection(del_row, false); //设置这一行取消选中
        }
        this.formData.tableFieldName2 = selection[0].name;
      } else if (selection.length === 1) {
        this.formData.tableFieldName2 = selection[0].name;
      }
    },
    cellClass(row) {
      if (row.columnIndex === 0) {
        return "disabledCheck";
      }
    },
    // 处理dialog关闭后
    handlerClose() {
      this.formData = {
        directoryCode: "",
        directoryName: "",
        dataSourceId: "",
        dataSourceId2: "",
        emrRuleType: "",
        requiredProject: "",
        hospitalProject: "",
        whetherCrossDbQuery: "0",
        crossDbQueryDataSourceId: "",
        structureName1: "",
        structureName2: "",
        tableFieldName1: "",
        tableFieldName2: "",
        headerName1: "",
        headerName2: "",
        recordsSql: "",
        conditionalRecordsSql: "",
        needStatistics: "0",
        tableAndFiledType: "0",
      };
      this.activemenu = 1; //激活菜单
      this.dbType = "";
      this.datasourcelist = []; //数据源列表
      this.structureNamelist = []; //数据库表名
      this.structureNamelist2 = []; //数据库表名2
      this.tableFieldNamelist1 = []; //字段名列表1
      this.tableFieldNamelist2 = []; //字段名列表2
      this.recordsSqltime = 1;
      this.conditionalRecordstime = 1;
      this.recordsSqlCount = 0;
      this.conditionalRecordsSqlCount = 0;
      this.dialogFormVisible = false; // 弹框状态p
      this.$parent.querydocumentRuleConfiguration(this.queryRightTable);
    },
    // 获取满足条件记录数SQL说明的占位符文本
    getConditionalSqlDescPlaceholder() {
      const ruleType = this.formData.emrRuleType;
      switch (ruleType) {
        case '完整性':
          return '请输入SQL说明';
        case '一致性':
          return '请输入SQL说明';
        case '整合性':
          return '请输入SQL说明';
        case '及时性':
          return '请输入SQL说明';
        default:
          return '请输入SQL说明';
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/emr-styles/emr-dialog.scss";
::v-deep .el-dialog__body {
  height: 100%;
  padding: 0px;
  padding: 10px 20px 20px 20px;
  .outsidedialog {
    min-height: 540px;
    .dialog-new-title {
      width: 100%;
      display: flex;
      justify-content: space-between;
    }
    .dialog-container {
      display: flex;
      height: 500px;
      .dialog-container-left {
        height: 100%;
        border-right: 1px solid #e1e3e7;
        flex: 1;
        text-align: right;
        padding-top: 20px;
        div {
          padding-right: 20px;
          margin: 20px 0px;
        }
        .activeitem {
          color: #5270dd;
          border-right: 3px solid #5270dd;
          font-weight: bold;
        }
      }
      .dialog-container-right {
        height: 100%;
        flex: 5;
        padding: 0px 20px;
        .title {
          font-weight: bold;
          font-size: 16px;
          color: #333333;
        }
        .title01 {
          padding: 6px 16px;
          margin-bottom: 30px;
          background: url("./../../../../../assets/emrimg/titleBg.png")
            no-repeat;
        }
        .title02 {
          padding: 6px 16px;
        }
        .container02 {
          display: flex;
          height: 240px;
          h5 {
            line-height: 30px;
          }
          .left {
            flex: 2;
          }
          .right {
            margin-left: 20px;
            flex: 5;
            .el-table {
              border-radius: 10px;
              overflow: hidden;
            }
          }
        }

        .editdataSql {
          border-radius: 9px;
          border: 1px solid #e3e6e8;
          height: 200px;
          margin-bottom: 20px;
          position: relative;
          padding-top: 10px;
          .sqlyuju {
            position: absolute;
            top: -6px;
            left: 20px;
            font-size: 12px;
            color: #4565d8;
            background: #fff;
            padding: 0px 4px;
            z-index: 99999999;
          }
          .el-textarea__inner {
            border: 1px solid transparent;
          }
          .editdataSqlbutton {
            height: 30px;
            background: #f3f3f3;
            border-radius: 0px 0px 9px 9px;
            padding: 0px 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .el-divider {
              margin: 10px 15px;
            }
            .el-button--text {
              color: #4c4c4c;
            }
            .testresult {
              // position: absolute;
              // right: 5px;
              // bottom: 6px;
              flex: 1;
              margin-left: 20px;
              color: #888888;
              text-align: right;
            }
          }
        }
        .el-input {
          max-width: 300px;
        }
      }
    }
  }
}
// 深度选择器 去掉全选按钮
::v-deep .el-table .disabledCheck .cell .el-checkbox__inner {
  display: none;
}

::v-deep .el-table .disabledCheck .cell::before {
  content: "";
  text-align: center;
  line-height: 37px;
}
.dialog-footer {
  margin-top: 10px;
}
.el-button--text:hover {
  background: #ffffff;
  padding: 6px 10px;
  border-radius: 3px;
}
.el-dropdown:hover {
  background: transparent;
}
.customization {
  position: absolute;
  right: 30px;
}
</style>
