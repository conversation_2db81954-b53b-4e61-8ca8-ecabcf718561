import request from "@/utils/request"

/****************************** 系統配置 ******************************/
//分页查询系统
export function querySystemList(data) {
  return request({
    url: "/system/query",
    method: "post",
    data,
  })
}

//更新系统
export function updateSystem(data) {
  return request({
    url: "/system/update",
    method: "put",
    data,
  })
}

//删除系统
export function deleteSystem(params) {
  return request({
    url: "/system/delete",
    method: "delete",
    params,
  })
}

//获取医院图标
export function getHospitalLogoPath(params) {
  return request({
    url: "/system/getHospitalLogoPath",
    method: "get",
    params,
  })
}

//新增系统
export function addSystem(data) {
  return request({
    url: "/system/add",
    method: "post",
    data,
  })
}
export function needShowAddSysButton() {
  return request({
    url: "/system/needShowAddSysButton",
    method: "get",
  })
}
//上传文件
export function uploadFile(file, systemId) {
  let formData = new FormData()
  formData.append("file", file)
  return request({
    url: "/system/uploadFile",
    method: "post",
    data: formData,
    params: { systemId },
  })
}
// 获取所有系统设置
export function querySysConfig() {
  return request({
    url: "/system/querySysConfig",
    method: "get",
  })
}
// 获取所有系统设置（包括webShow为N和Y的）
export function queryAllSysConfig() {
  return request({
    url: "/system/queryAllSysConfig",
    method: "get",
  })
}
// 更新系统设置
export function updateSysConfigs(data) {
  return request({
    url: "/system/updateSysConfigs",
    method: "post",
    data,
  })
}

/****************************** 账号关联 ******************************/
//查询账号关联信息列表
export function getAccountAssociationList(data) {
  return request({
    url: "/ssoSysUserRuser/getAccountAssociation",
    method: "put",
    data,
  })
}

//获取该用户所有注册系统的账号
export function getAllSysAccounts(data) {
  return request({
    url: "/ssoSysUserRuser/getAllSysAccounts",
    method: "post",
    data,
  })
}
//通过用户ID获取关联账户信息（分页）
export function getAccountAssociationByUserId(data) {
  return request({
    url: "/ssoSysUserRuser/getAccountAssociationByUserId",
    method: "put",
    data,
  })
}

//更新账号关联 包含刪除
export function updateAccountAssociation(data) {
  return request({
    url: "/ssoSysUserRuser/updateAccountAssociation",
    method: "put",
    data,
  })
}
//添加账号关联
export function addAccountAssociation(data) {
  return request({
    url: "/ssoSysUserRuser/addAccountAssociation",
    method: "post",
    data,
  })
}
//删除账号关联
export function deleteAccountAssociation(data) {
  return request({
    url: "/ssoSysUserRuser/deleteAccountAssociation",
    method: "put",
    data,
  })
}
// 检查用户信息的状态，包括密码是否过期，是否停用
export function checkUserInfoStatus(params) {
  return request({
    url: "/user/checkUserInfoStatus",
    method: "get",
    params,
  })
}
// 通过旧密码更新密码
export function updateUserPasswordByOldPS(data) {
  return request({
    url: "/user/updateUserPasswordByOldPS",
    method: "post",
    data,
  })
}
