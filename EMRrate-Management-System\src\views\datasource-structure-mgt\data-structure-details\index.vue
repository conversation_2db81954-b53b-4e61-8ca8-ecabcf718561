<template>
  <MainCard :isOverflowAuto="false">
    <div class="data-structure-details">
      <div class="structure-details-main">
        <div class="source-list-left">
          <div class="source-header">
            <div class="title">全部数据源</div>
            <div
              class="refresh"
              @click="queryDataSourceList">
              <i class="el-icon-refresh"></i>
            </div>
          </div>
          <div
            class="list-box"
            v-loading="isLoading">
            <div class="source-list">
              <div
                :class="{
                  'list-item': true,
                  active: curActiveIndex == item.dataSourceId
                }"
                @click="handlerClickDataSource(item)"
                v-for="item in dataSourceOptions"
                :key="item.dataSourceId">
                <div class="left">
                  <div class="source-info">
                    <div class="info-left">
                      <svg-icon
                        v-if="curActiveIndex == item.dataSourceId"
                        style="font-size: 24px; margin-top: -2px"
                        icon-class="icon_data" />
                      <svg-icon
                        v-else
                        style="font-size: 24px; margin-top: -2px"
                        icon-class="icon_data-full" />
                    </div>
                    <div
                      :class="{
                        'info-right': true,
                        active: curActiveIndex == item.dataSourceId
                      }">
                      <div class="des-title">
                        {{ item.dataSourceName }}
                      </div>
                      <div
                        :style="{
                          color:
                            curActiveIndex == item.dataSourceId
                              ? '#5270dd'
                              : '#666'
                        }"
                        class="des-content">
                        描述：{{ item.dataSourceName }}
                      </div>
                    </div>
                  </div>
                </div>
                <div class="right">
                  <i
                    :style="{
                      color:
                        curActiveIndex == item.dataSourceId ? '#5270dd' : '#666'
                    }"
                    class="el-icon-arrow-right"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="table-right">
          <div class="table-header">
            <div class="title">{{ curDataSourceName }}</div>
            <!-- <div class="control-tool">
              <el-tooltip
                class="item"
                effect="dark"
                content="全部展开"
                placement="top">
                <el-button
                  size="mini"
                  icon="el-icon-arrow-down"></el-button>
              </el-tooltip>
              <el-tooltip
                class="item"
                effect="dark"
                content="全部收起"
                placement="top">
                <el-button
                  size="mini"
                  icon="el-icon-arrow-up"></el-button>
              </el-tooltip>
            </div> -->
          </div>
          <div class="table">
            <el-table
              :data="tableData"
              v-loading="loading"
              ref="Table"
              lazy
              :load="load"
              @expand-change="handlerExpandChange"
              row-key="id"
              :header-cell-style="{ background: '#fff', color: '#606266' }"
              :tree-props="{
                children: 'children',
                hasChildren: 'hasChildren'
              }">
              <el-table-column
                prop="tableOrViewName"
                width="380"
                label="数据库表/视图">
                <template slot-scope="scope">
                  <img
                    v-show="!!scope.row.tableOrViewName"
                    style="vertical-align: middle; margin-right: 6px"
                    :src="
                      scope.row.curTpye == 'VIEW'
                        ? treeIcons.tree_view
                        : treeIcons.tree_table
                    "
                    alt="" />
                  <span>{{ scope.row.tableOrViewName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="name"
                width="200"
                label="字段名称">
                <template slot-scope="scope">
                  <span
                    style="margin-right: 4px; color: #888"
                    v-show="!scope.row.name && scope.row.islayLoad"
                    >包含 {{ scope.row.fieldNumber }} 个字段</span
                  >
                  <span
                    @click="handlerRowExpansion(scope.row)"
                    class="ziduan-show"
                    v-show="!scope.row.name && scope.row.islayLoad"
                    >&lsqb;{{
                      scope.row.isExpansion == false ? '显示' : '隐藏'
                    }}&rsqb;</span
                  >
                  <span v-show="scope.row.name">{{ scope.row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column
                width="160px"
                prop="remarks"
                label="字段注释"></el-table-column>
              <el-table-column
                prop="type"
                width="120px"
                label="字段类型"></el-table-column>
              <el-table-column
                show-overflow-tooltip
                prop="length"
                label="字段长度"></el-table-column>
              <el-table-column
                prop="scale"
                label="精度"></el-table-column>
              <el-table-column
                prop="defaultvalue"
                label="默认值">
                <template slot-scope="scope">
                  <span v-if="scope.row.defaultvalue === null">null</span>
                  <span v-else>{{ scope.row.defaultvalue }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="nullable"
                width="80"
                label="是否为空">
                <template slot-scope="scope">
                  <span v-if="scope.row.nullable === 'false'">否</span>
                  <span v-else-if="scope.row.nullable === 'true'">是</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="pk"
                width="80"
                label="是否主键">
                <template slot-scope="scope">
                  <span v-if="scope.row.pk === 'false'">否</span>
                  <span v-else-if="scope.row.pk === 'true'">是</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
  </MainCard>
</template>

<script>
import { queryDataSourceList } from '@/api/dataSourceStructureMgt/dataSourceMgt'
import ArrowAnimation from '@/components/ArrowAnimation'
import TreeCard from './TreeCard'
import {
  getLeftTree,
  getRightStructures
} from '@/api/dataSourceStructureMgt/dataStructureDetails'

// 筛选 VIEW 和 TABLE的chilren 并合并
function extractTableAndViewChildren(data) {
  var tableAndViewChildren = []
  data.forEach(function (item) {
    if (item.label === 'TABLE' && item.children !== undefined) {
      const curArr = item.children.map(({ label, id }) => ({
        curTpye: 'TABLE',
        id,
        tableOrViewName: label
      }))
      tableAndViewChildren = tableAndViewChildren.concat(curArr)
    }

    if (item.label === 'VIEW' && item.children !== undefined) {
      const curArr = item.children.map(({ label, id }) => ({
        curTpye: 'VIEW',
        id,
        tableOrViewName: label
      }))
      tableAndViewChildren = tableAndViewChildren.concat(curArr)
    }

    if (item.children && item.children.length > 0) {
      var extractedChildren = extractTableAndViewChildren(item.children)
      tableAndViewChildren = tableAndViewChildren.concat(extractedChildren)
    }
  })
  return tableAndViewChildren
}

export default {
  components: {
    ArrowAnimation,
    TreeCard
  },
  data() {
    return {
      queryData: {
        // 查询数据
        pageNum: 1,
        pageSize: 10
      },
      curActiveIndex: 0,
      isExpansion: false,
      treeIcons: {
        tree_data_sourse: require('@/assets/tree/tree_data_sourse.png'),
        tree_folder: require('@/assets/tree/tree_folder.png'),
        tree_shouqi: require('@/assets/tree/tree_shouqi.png'),
        tree_table: require('@/assets/tree/tree_table.png'),
        tree_view: require('@/assets/tree/tree_view.png'),
        tree_zhankai: require('@/assets/tree/tree_zhankai.png')
      },
      tree_card_header: require('@/assets/tree/tree_card_header.png'),
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
        disabled: function (data) {
          if (data.children) {
            return true
          } else {
            return false
          }
        }
      },
      querySelectData: {}, // 查询所有数据源
      queryLeftTree: {
        // 查询左侧树数据
        dataSourceId: 39
      },
      queryRightTable: {
        // 查询右侧表数据
        dataSourceId: '',
        databaseName: '',
        name: '',
        pageNum: 1,
        pageSize: 9999,
        schema: ''
      },
      dataSourceOptions: [], // 数据源下拉框数据
      leftTreeData: [],
      curDatabaseName: '',
      tableData: [],
      totalNum: 1,
      isleftTreeData: false,
      loading: false,
      isLoading: false,
      sourceCardTitle: '',
      tableOrViewTitle: '',
      curDataSourceName: ''
    }
  },
  created() {
    // 初始化数据源下拉框
    this.queryDataSourceList()
  },
  watch: {
    tableData: {
      handler(val) {
        this.tableData = val
      },
      deep: true
    }
  },
  methods: {
    load(tree, treeNode, resolve) {
      this.queryRightTable.dataSourceId = this.queryLeftTree.dataSourceId
      this.queryRightTable.name = tree.tableOrViewName
      this.queryRightTable.databaseName = this.curDatabaseName
      this.queryRightTable.schema = this.curSchema
      tree.isExpansion = true
      tree.islayLoad = true
      getRightStructures(this.queryRightTable).then((res) => {
        if (res.status === 0) {
          tree.fieldNumber = res.data.list.length
          resolve(res.data.list)
        } else {
          this.loading = false
          this.$message({
            message: res.msg,
            type: 'error'
          })
        }
      })
    },
    handlerExpandChange(row, isExpansion) {
      row.isExpansion = isExpansion
    },
    handlerRowExpansion(row) {
      row.isExpansion = !row.isExpansion
      this.$refs['Table'].toggleRowExpansion(row, row.isExpansion)
    },
    handlerClickDataSource({ dataSourceId, databaseName, dataSourceName }) {
      this.queryLeftTree.dataSourceId = dataSourceId
      this.curActiveIndex = dataSourceId
      this.curDatabaseName = databaseName
      this.curDataSourceName = dataSourceName
      this.getLeftTreeList()
    },
    // 处理各级别目录图标
    getIconClass(node) {
      // 数据源目录图标
      if (node.level == 1) {
        return this.treeIcons.tree_data_sourse
      }
      // 模式、表、视图目录图标
      if (node.level == 2 || node.level == 3) {
        return this.treeIcons.tree_folder
      }
      // 表内容
      if (node.level == 4 || node.parent.label === 'TABLE') {
        return this.treeIcons.tree_table
      }
      // 视图内容
      if (node.level == 4 || node.parent.label === 'VIEW') {
        return this.treeIcons.tree_table
      }
    },
    getLabelName(node) {
      if (node.level == 2) {
        return '模式'
      }
      if (node.label === 'TABLE') {
        return '表'
      }
      if (node.label === 'VIEW') {
        return '视图'
      }
      return node.label
    },
    // 查询数据源列表
    queryDataSourceList() {
      this.isLoading = true
      queryDataSourceList(this.querySelectData).then((res) => {
        if (res.status === 0) {
          this.isLoading = false
          this.dataSourceOptions = res.data.list

          if (this.$route.params.dataSourceId) {
            // 数据源页面跳转而来
            this.queryLeftTree.dataSourceId = Number(
              this.$route.params.dataSourceId
            )
            this.curDataSourceName = this.$route.params.dataSourceName
            this.curActiveIndex = Number(this.$route.params.dataSourceId)
            this.queryLeftTree.dataSourceId = Number(
              this.$route.params.dataSourceId
            )
            this.getLeftTreeList()

            return
          }
          if (this.dataSourceOptions.length > 0) {
            this.curActiveIndex = this.dataSourceOptions[0].dataSourceId
            this.curDataSourceName = this.dataSourceOptions[0].dataSourceName
            this.queryLeftTree.dataSourceId =
              this.dataSourceOptions[0].dataSourceId
            this.getLeftTreeList()
          }
        }
      })
    },
    // 获取左侧树列表数据
    getLeftTreeList() {
      this.loading = true
      this.leftTreeDataoading = true
      this.tableData = [] // 将表格数据清空
      getLeftTree(this.queryLeftTree)
        .then((res) => {
          if (res.status !== 0) {
            this.$message({
              message: res.msg,
              type: 'error'
            })
            this.leftTreeDataoading = false
            return
          }
          this.loading = false
          this.curSchema = res.data[0].children[0].label
          this.tableData = extractTableAndViewChildren(res.data).map(
            (item) => ({
              ...item,
              hasChildren: true,
              isExpansion: false,
              islayLoad: false,
              fieldNumber: 0
            })
          )
          this.leftTreeDataoading = false
        })
        .catch((err) => {
          this.leftTreeDataoading = false
        })
    },
    // 点击树形结构 表 或 view
    // handleClick(data1, data2, data3) {
    //   if (data1.whether) {
    //     this.tableOrViewTitle = data1.label
    //     this.queryRightTable.databaseName =
    //       data2.parent.parent.parent.data.label
    //     this.queryRightTable.name = data1.label
    //     this.queryRightTable.schema = data2.parent.parent.data.label
    //     this.getRightTableData()
    //   }
    // },
    // 改变页面显示条数
    handleSizeChange(val) {
      this.queryRightTable.pageSize = val
      this.getRightTableData()
    },
    // 改变页数
    handleCurrentChange(val) {
      this.queryRightTable.pageNum = val
      this.getRightTableData()
    }
  }
}
</script>

<style scoped lang="scss">
.data-structure-details {
  height: 100%;
  .structure-details-main {
    display: flex;
    height: 100%;
    .source-list-left {
      flex: 0 0 304px;
      display: flex;
      flex-direction: column;
      .source-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        align-items: center;
        .el-icon-refresh {
          font-size: 20px;
          cursor: pointer;
          &:hover {
            color: #5270dd;
          }
        }
      }
      .list-box {
        border: 1px solid #e3e6e8;
        border-radius: 9px;
        overflow: auto;
        .source-list {
          line-height: 1.5;
          padding: 16px;
          .list-item {
            padding: 16px;
            border: 1px solid #e3e6e8;
            margin-bottom: 10px;
            border-radius: 9px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            > .left {
              .source-info {
                display: flex;
                .info-left {
                  margin-right: 6px;
                }
                .info-right {
                  margin-right: 10px;
                  .des-content {
                    color: #666;
                    font-size: 12px;
                    white-space: nowrap;
                  }
                }
                .info-right.active {
                  color: #5270dd;
                }
              }
            }
            cursor: pointer;
            &:hover {
              background-color: rgb(248, 249, 255);
            }
          }
          .list-item.active {
            border: 1px solid #5270dd;
            background-color: #ebeefb;
          }
        }
      }
    }
    .table-right {
      padding-left: 20px;
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: auto;
      .table-header {
        font-size: 16px;
        font-weight: 600;
        .title {
          line-height: 32px;
          margin-bottom: 10px;
          background: url(~@/assets/dataSource/titleBg.png) left / contain
            no-repeat;
          padding-left: 26px;
        }
      }
      > .table {
        flex-grow: 1 ;
        overflow: auto;
      }
    }
    .structure-details-pag {
      margin-top: 10px;
    }
    .card-header {
      position: relative;
      .gimg {
        position: absolute;
        top: -33px;
        left: 0;
      }
    }
  }
}
.el-card {
  border: 1.6px solid #e7eaec;
}
.ziduan-show {
  cursor: pointer;
  color: #5270dd;
  &:hover {
    text-decoration: underline;
  }
}
</style>
