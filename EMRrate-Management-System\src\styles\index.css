@charset "UTF-8";
html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td {
  margin: 0;
  padding: 0;
}

ul, li, ol, li {
  list-style: none;
}

h1 {
  position: relative;
  font-size: 24px;
  display: inline-block;
  margin-bottom: 10px;
  margin-top: 10px;
}

h1::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 2px;
  height: 7px;
  width: 100%;
  background-color: rgba(18, 145, 195, 0.6);
}

h2 {
  font-size: 20px;
}

:export {
  menuText: #ffffff;
  menuActiveText: #ffffff;
  subMenuActiveText: #ffffff;
  menuBg: transparent;
  menuHover: transparent;
  subMenuBg: transparent;
  subMenuHover: rgba(255, 255, 255, 0.1);
  sideBarWidth: 210px;
}

/* fade */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.28s;
}

.fade-enter,
.fade-leave-active {
  opacity: 0;
}

/* fade-transform */
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all .5s;
}

.fade-transform-enter {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* breadcrumb transition */
.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all .5s;
}

.breadcrumb-enter,
.breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(20px);
}

.breadcrumb-move {
  transition: all .5s;
}

.breadcrumb-leave-active {
  position: absolute;
}

.el-upload input[type="file"] {
  display: none !important;
}

.el-upload__input {
  display: none;
}

.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

.upload-container .el-upload {
  width: 100%;
}

.upload-container .el-upload .el-upload-dragger {
  width: 100%;
  height: 200px;
}

.el-dropdown-menu a {
  display: block;
}

.el-range-separator {
  box-sizing: content-box;
}

.el-form--label-top .el-form-item__label {
  padding: 0px;
  font-size: 13px;
}

.el-button--primary {
  background: #1b88c0;
  border-color: #1b88c0;
  color: #fff;
}

.el-button--primary:hover, .el-button--primary:focus {
  background-color: rgba(27, 136, 192, 0.8);
  border-color: rgba(27, 136, 192, 0.8);
  color: #fff;
}

.el-button--secondary {
  background: #6bcfc0;
  border-color: #6bcfc0;
  color: #fff;
}

.el-button--secondary:hover, .el-button--secondary:focus {
  background-color: rgba(107, 207, 192, 0.8);
  border-color: rgba(107, 207, 192, 0.8);
  color: #fff;
}

.el-autocomplete {
  width: 240px;
}

.el-autocomplete-suggestion.el-popper {
  width: 400px !important;
}

.el-button--third {
  background: #f3b255;
  border-color: #f3b255;
  color: #fff;
}

.el-button--third:hover, .el-button--third:focus {
  background-color: rgba(243, 178, 85, 0.8);
  border-color: rgba(243, 178, 85, 0.8);
  color: #fff;
}

.el-breadcrumb {
  font-size: 16px;
}

.cell {
  text-align: center;
}

/**修改全局的滚动条*/
/**滚动条的宽度*/
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-thumb {
  background-color: #e4e7ed;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #c1c5cc;
}

.el-table__body-wrapper::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #e4e7ed;
  border-radius: 10px;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
  cursor: pointer !important;
  background-color: #c1c5cc;
}

.scrollbar-wrapper {
  margin-bottom: 0 !important;
}

.el-dialog {
  border-radius: 5px;
}

.el-dialog .el-dialog__header {
  color: white;
  background-color: #369cc4;
  padding-top: 15px;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  line-height: 20px;
}

.el-dialog .el-dialog__header .el-dialog__headerbtn {
  top: 15px;
}

.el-dialog .el-dialog__header .el-dialog__headerbtn .el-dialog__close {
  color: white;
  font-size: 20px;
}

.el-dialog .el-dialog__header .el-dialog__title {
  font-size: 16px;
  color: white;
}

.el-dialog .el-dialog__body {
  padding: 15px 10px;
}

.has-gutter tr th {
  background-color: #f0f0f0;
}

.el-divider {
  margin: 16px 0px;
}

#app .main-container {
  min-height: 100%;
  transition: margin-left 0.28s;
  margin-left: 210px;
  position: relative;
}

#app .main-container footer {
  position: fixed;
  bottom: 0px;
  left: 0px;
  right: 0px;
  background-color: #f0f0f0;
  height: 30px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.4);
  padding-right: 20px;
}

#app .sidebar-container {
  transition: width 0.28s;
  width: 210px !important;
  background-color: transparent;
  height: 100%;
  position: fixed;
  font-size: 0px;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 1001;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

#app .sidebar-container .horizontal-collapse-transition {
  transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
}

#app .sidebar-container .scrollbar-wrapper {
  overflow-x: hidden !important;
  padding-bottom: 19px;
}

#app .sidebar-container .el-scrollbar__bar.is-vertical {
  right: 0px;
}

#app .sidebar-container .is-horizontal {
  display: none;
}

#app .sidebar-container a {
  display: inline-block;
  width: 100%;
  overflow: hidden;
}

#app .sidebar-container .svg-icon {
  margin-right: 12px;
}

#app .sidebar-container .sub-el-icon {
  margin-right: 12px;
  margin-left: -2px;
}

#app .sidebar-container .el-menu {
  border: none;
  height: 100%;
  width: 100% !important;
}

#app .sidebar-container .el-menu .el-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

#app .sidebar-container .el-menu .submenu-title-noDropdown:hover,
#app .sidebar-container .el-menu .el-submenu__title:hover {
  background-color: transparent !important;
}

#app .sidebar-container .el-menu .submenu-title-noDropdown i,
#app .sidebar-container .el-menu .el-submenu__title i {
  color: white;
}

#app .sidebar-container .el-menu .is-active > .el-submenu__title {
  color: #ffffff !important;
}

#app .sidebar-container .el-menu .nest-menu .el-submenu > .el-submenu__title,
#app .sidebar-container .el-menu .el-submenu .el-menu-item {
  min-width: 210px !important;
  background-color: transparent;
}

#app .sidebar-container .el-menu .nest-menu .el-submenu > .el-submenu__title:hover,
#app .sidebar-container .el-menu .el-submenu .el-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

#app .sidebar-container .el-menu .router-link-active .is-active {
  background-color: rgba(255, 255, 255, 0.1) !important;
  color: white;
}

#app .sidebar-container .el-submenu {
  overflow: hidden;
}

#app .sidebar-container .el-submenu > .el-submenu__title {
  fill: currentColor;
}

#app .sidebar-container .el-submenu > .el-submenu__title .svg-icon {
  font-size: 16px;
}

#app .sidebar-container .el-submenu > .el-submenu__title .sub-el-icon {
  font-size: 16px;
}

#app .sidebar-container .el-menu--collapse .el-submenu > .el-submenu__title > span {
  height: 0;
  width: 0;
  overflow: hidden;
  visibility: hidden;
  display: inline-block;
}

#app .el-menu--collapse .el-menu .el-submenu {
  min-width: 210px !important;
}

#app .withoutAnimation .main-container,
#app .withoutAnimation .sidebar-container {
  transition: none;
}

.el-menu--vertical > .el-menu .svg-icon {
  margin-right: 12px;
}

.el-menu--vertical > .el-menu .sub-el-icon {
  margin-right: 12px;
  margin-left: -2px;
}

.el-menu--vertical > .el-menu--popup {
  max-height: 100vh;
  overflow-y: auto;
}

.el-menu--vertical > .el-menu--popup::-webkit-scrollbar-track-piece {
  background: #d3dce6;
}

.el-menu--vertical > .el-menu--popup::-webkit-scrollbar {
  width: 6px;
}

.el-menu--vertical > .el-menu--popup::-webkit-scrollbar-thumb {
  background: #99a9bf;
  border-radius: 20px;
}

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  /* font-family: Noto Sans SC; */
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix:after {
  visibility: hidden;
  display: block;
  font-size: 0;
  content: " ";
  clear: both;
  height: 0;
}

.app-container {
  padding-bottom: 40px;
}

h4 {
  margin: 5px 0px;
}

.transfer-title {
  margin: 0;
  font-size: 14px !important;
  font-weight: 500 !important;
}
