
import { queryCodeValueContent } from "@/api/codeValueMgt/codeValueContent"
const mixin = {
  data() {
    return {
      checkRuleFatherTypeData: [], // 规则大类列表数据
      checkRuleTypeData: [], // 规则小类列表数据
      loading: false, // 规则小类列表加载状态
      queryCheckRuleFatherTypeData: {
        // 查询规则大类列表数据
        contentKey: "",
        typeCode: "checkRuleFatherType",
      },
      queryCheckRuleTypeData: {
        // 查询规则小类列表数据
        contentKey: "",
        typeCode: "checkRuleType",
      },
    }
  },
  methods: {
    // 获取规则大类
    getCheckRuleFatherType() {
      queryCodeValueContent(this.queryCheckRuleFatherTypeData).then((res) => {
        if (res.status === 0) {
          this.checkRuleFatherTypeData = res.data.list
        }
      })
    },
    // 获取规则小类
    getCheckRuleType() {
      this.loading = true
      this.checkRuleTypeData = []
      queryCodeValueContent(this.queryCheckRuleTypeData).then((res) => {
        if (res.status !== 0) {
          this.$message({
            type: "error",
            message: res.msg
          })
          this.loading = false
          return
        }
        this.checkRuleTypeData = res.data.list
        this.loading = false
      })
    },
  }
}
export default mixin
