<template>
  <el-dialog
    v-dialogDrag
    :visible="dialogDrag"
    width="800px"
    :show-close="false"
    @close="closeCreateModal"
    :close-on-click-modal="false"
  >
    <div class="dialog-new-title">
      <h4>{{ dialogtitle }}</h4>
      <i class="el-icon-close" @click="handlerClose()"></i>
    </div>
    <el-form
      :model="updateForm"
      class="dialog-body"
      ref="updateFormRef"
      :rules="rules"
    >
      <div class="dialog-form-item">
        <el-form-item label-width="0px" prop="projectName">
          <el-input
            style="width: 100%"
            v-model="updateForm.projectName"
          ></el-input>
        </el-form-item>
      </div>
    </el-form>
    <div slot="footer">
      <el-button @click="dialogDrag = false" size="mini">取 消</el-button>
      <el-button type="primary" @click="createUser" size="mini"
        >保 存</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { updateProject } from "@/api/document-management/project-management";
export default {
  data() {
    return {
      dialogDrag: false,
      dialogtitle: "", //动态标题
      updateForm: {},
      rules: {
        projectName: [
          {
            trigger: "blur",
            required: true,
            message: "请填写项目名称",
          },
        ],
      },
    };
  },
  methods: {
    // 关闭弹窗
    closeCreateModal() {
      this.dialogDrag = false;
      this.updateForm = {};
    },
    // 不同方式打开弹窗展示不同的信息,重命名
    openrechristen(category, data) {
      this.dialogDrag = true;
      this.dialogtitle = category;
      this.updateForm = {
        projectName: data.projectName,
        personInCharge: JSON.parse(data.personInCharge),
        levelCode: data.levelCode,
        projectType: data.projectType,
        id: data.id,
      };
    },
    // 保存
    createUser() {
      this.$refs["updateFormRef"].validate((valid) => {
        if (valid) {
          updateProject(this.updateForm).then((res) => {
            if (res && res.msg == "success") {
              this.$message.success("重命名项目成功!");
              this.dialogDrag = false;
              this.updateFormRef = {};
              this.$parent.queryProject();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          return false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/emr-styles/emr-dialog.scss";

::v-deep .el-dialog__body {
  height: 100%;
  padding: 10px 20px;
  .dialog-new-title {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 10px 20px;
  }
}
</style>