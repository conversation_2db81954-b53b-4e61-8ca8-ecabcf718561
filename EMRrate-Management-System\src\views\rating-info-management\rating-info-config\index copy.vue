<template>
  <MainCard>
    <div class="rating-config-container">
      <div class="rating-config-header">
        <el-form
          :model="queryData"
          ref="ruleForm"
          inline>
          <el-form-item
            label="消息类型"
            prop="msgType">
            <el-input
              v-model="queryData.msgType"
              placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item
            label="消息标题"
            prop="msgTitle">
            <el-input
              v-model="queryData.msgTitle"
              placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item
            label="发送人"
            prop="sender">
            <el-input
              v-model="queryData.sender"
              placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="queryAllMsgList"
              icon="el-icon-search"
              >搜索</el-button
            >
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="handlerAddClick"
              icon="el-icon-plus"
              >新增</el-button
            >
          </el-form-item>
          <el-form-item>
            <el-button @click="resetForm('ruleForm')">重置</el-button>
          </el-form-item>
        </el-form>
        <!-- 新增/编辑数据源 -->
        <AddOrEditDataSource
          ref="addOrEditDataSource"
          :btnType="btnType"
          :row="row" />
        <!-- 结构设置 -->
        <StructureConfig
          v-if="isShowConfig"
          @dialogChange="handleDialogChange"
          ref="structureConfig"
          :row="row" />
      </div>
      <div class="rating-config-main">
        <div class="rating-config-table">
          <el-table
            :data="tableData"
            ref="sourceMgtTable"
            style="width: 100%"
            @selection-change="handleSelectionChange"
            v-loading="loading"
            :header-cell-style="{ background: '#fff', color: '#606266' }">
            <el-table-column
              width="100"
              prop="sender"
              label="发送人">
            </el-table-column>
            <el-table-column
              width="120"
              prop="msgType"
              label="消息类型">
            </el-table-column>
            <el-table-column
              width="120"
              prop="msgTitle"
              label="消息标题">
            </el-table-column>
            <el-table-column
              prop="msgContent"
              text-align="left"
              label="消息内容">
            </el-table-column>
            <el-table-column
              width="180"
              prop="updateTime"
              label="发送时间">
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              width="220">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  plain
                  type="primary"
                  icon="el-icon-edit-outline"
                  @click="handleShowDialog(scope.$index, scope.row, 1)"
                  >编辑</el-button
                >
                <el-form-item>
                  <el-button
                    type="danger"
                    @click="deleteDataSource"
                    icon="el-icon-delete"
                    >删除</el-button
                  >
                </el-form-item>
                <el-button
                  size="mini"
                  plain
                  type="primary"
                  icon="el-icon-edit-outline"
                  @click="handleShowDialog(scope.$index, scope.row, 2)"
                  >结构设置</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="rating-config-pag">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="queryData.pageNum"
            :page-sizes="[5, 10, 15, 20]"
            :page-size="queryData.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalNum">
          </el-pagination>
        </div>
      </div>
    </div>
  </MainCard>
</template>

<script>
import {
  queryDataSourceList,
  deleteDataSource
} from '@/api/dataSourceStructureMgt/dataSourceMgt'
import {
  queryAllMsgList,
  addMsg
} from '@/api/ratingInfoManagement/ratingInfoConfig'
import AddOrEditDataSource from './components/AddOrEditDataSource.vue'
import StructureConfig from './components/StructureConfig.vue'
export default {
  data() {
    return {
      queryData: {
        // 查询数据
        beginTime: '',
        endTime: '',
        msgTitle: '',
        msgType: '',
        sender: '',
        pageNum: 1,
        pageSize: 10
      },
      isShowConfig: false, // 结构配置页面是否销毁，节省性能
      btnType: 1, // 1 代表新增 0 代表编辑
      tableData: [], // 表格数据
      totalNum: 1,
      loading: false,
      env: process.env.NODE_ENV, // 环境变量
      dataSourceIds: [], // 需要删除的数据源的ID
      row: {} // 点击编辑或结构设置时整行的数据
    }
  },
  components: {
    AddOrEditDataSource,
    StructureConfig
  },
  created() {
    this.queryAllMsgList()
  },
  methods: {
    // 查询消息列表
    queryAllMsgList() {
      this.loading = true
      queryAllMsgList(this.queryData).then((res) => {
        if (res.status === 0) {
          this.tableData = res.data.list
          this.totalNum = res.data.total
          this.loading = false
        }
      })
    },
    // 结构配置弹窗隐藏时
    handleDialogChange() {
      this.isShowConfig = false
    },
    handlerAddClick() {
      this.btnType = 1
      this.$refs.addOrEditDataSource.dialogFormVisible = true
    },
    // 改变页面显示条数
    handleSizeChange(val) {
      this.queryData.pageSize = val
      this.queryAllMsgList()
    },
    // 改变页数
    handleCurrentChange(val) {
      this.queryData.pageNum = val
      this.queryAllMsgList()
    },
    // 重置
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.queryAllMsgList()
    },
    // 驱动文件上传成功时
    handleSuccess(res, file, fileList) {
      if (res.status === 0) {
        this.$message({
          message: '上传驱动文件成功!',
          type: 'success'
        })
      } else if (res.status === -1) {
        this.$message({
          message: res.msg,
          type: 'warning'
        })
      }
    },
    // 选择项发生变化时
    handleSelectionChange(val) {
      this.dataSourceIds = val.map((item) => {
        return item.dataSourceId
      })
    },
    // 删除
    deleteDataSource() {
      this.$confirm('此操作将删除消息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteDataSource({ dataSourceIds: this.dataSourceIds }).then(
            (res) => {
              this.loading = true
              if (res.status === 0) {
                this.$message({
                  message: '删除成功!',
                  type: 'success'
                })
                this.queryDataSourceList()
              } else {
                this.$message({
                  message: res.msg,
                  type: 'error'
                })
              }
            }
          )
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 打开编辑数据源
    handleShowDialog(index, row, type) {
      if (type == 1) {
        this.btnType = 0
        this.row = JSON.parse(JSON.stringify(row))
        this.$refs.addOrEditDataSource.dialogFormVisible = true
      } else {
        this.isShowConfig = true
        this.row = JSON.parse(JSON.stringify(row))
        this.$nextTick(() => {
          this.$refs.structureConfig.dialogFormVisible = true
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.rating-config-container {
  .rating-config-header {
    display: flex;
    margin: 10px 0;
  }
  .rating-config-main {
    // padding-left: 10px;
    .rating-config-table {
      margin-top: 10px;
    }
    .rating-config-dialog {
      .mgt-dialog-upload {
        margin-left: 50px;
      }
    }
    .rating-config-pag {
      margin-top: 10px;
    }
  }
}
</style>
