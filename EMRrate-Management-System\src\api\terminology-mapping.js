import request from '@/utils/request'

//查询术语映射列表
export function queryDictMappings(data) {
    return request({
        url: '/DictMappings/query',
        method: 'post',
        data
    })
}

//根据术语ID获取字段名称
export function getAllFieldByTermId(params) {
    return request({
        url: '/DictMappings/getAllFieldByTermId',
        method: 'get',
        params
    })
}


//获取对应值域信息
export function getAllBdInfo(data) {
    return request({
        url: '/DictMappings/getAllBdInfo',
        method: 'post',
        data
    })
}


//删除术语映射表单项
export function deleteDictMappings(params) {
    return request({
        url: '/DictMappings/delete',
        method: 'delete',
        params
    })
}
//新增术语映射
export function addDictMappings(data) {
    return request({
        url: '/DictMappings/add',
        method: 'post',
        data
    })
}
//修改术语映射
export function updateDictMappings(data) {
    return request({
        url: '/DictMappings/update',
        method: 'put',
        data
    })
}

//获取映射类别
export function getMappingType() {
    return request({
        url: '/DictMappings/getMappingType',
        method: 'get',
    })
}


//导出表格
export function excelExport(params) {
    return request({
        url: '/termMappingExcel/excelExport',
        method: 'get',
        responseType:'blob',//下载文件的时候加上，否则会乱码。
        params
    })
}

//导入表格
export function excelImport(data) {
    return request({
        url: '/termMappingExcel/excelImport',
        method: 'post',
        data
    })
}

//下载模板
export function downloadMappingExcel() {
    return request({
        url: '/termMappingExcel/downloadMappingExcel',
        method: 'get',
        responseType:'blob',//下载文件的时候加上，否则会乱码。
    })
}

//导入 标准表格
export function excelStandardImport(data,params) {
    return request({
        url: `/termMappingExcel/excelStandardImport`,
        method: 'post',
        data,
        params
    })
}


/**************术语映射内容**************/
//查询术语映射内容
export function queryDictMappingContent(data) {
    return request({
        url: '/DictMappingContent/query',
        method: 'post',
        data
    })
}
//术语映射内容删除
export function deleteDictMappingContent(params) {
    return request({
        url: '/DictMappingContent/delete',
        method: 'delete',
        params
    })
}
//术语映射内容重置
export function clearDictMappingContent(params) {
    return request({
        url: '/DictMappingContent/clear',
        method: 'delete',
        params
    })
}
//术语映射内容新增
export function addDictMappingContent(data) {
    return request({
        url: '/DictMappingContent/add',
        method: 'post',
        data
    })
}
//术语映射内容修改
export function updateDictMappingContent(data) {
    return request({
        url: '/DictMappingContent/update',
        method: 'put',
        data
    })
}

//弹窗 目标字段
export function queryStddDictMappingContent(data) {
    return request({
        url: '/DictMappingContent/queryStdd',
        method: 'post',
        data
    })
}

