<template>
  <div class="quality-problem-container">
    <h1 class="quality-problem-title">问题管理</h1>
    <div class="quality-problem-header">
      <el-form :model="queryData" ref="ruleForm" inline>
        <el-form-item label="系统及数据源" prop="dbNm">
          <el-select
            v-model="queryData.dbNm"
            placeholder="请选择"
            @change="handlerSysAndDbChange"
            clearable
          >
            <el-option label="全部" value=""></el-option>
            <el-option
              v-for="item in SysAndDbData"
              :key="item.id"
              :label="item.sysName"
              :value="item.dbNm"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="检测表或视图" prop="checkRuleTableOrView">
          <el-autocomplete
            ref="abc"
            v-model="queryData.checkRuleTableOrView"
            :fetch-suggestions="querySearchAsync"
            value-key="tableOrViewName"
            placeholder="请输入内容"
            :disabled="isTableOrViewDisabled"
            @select="handlerTableOrViewSelect"
            clearable
            @click.native="handlerTableOrViewClick"
          ></el-autocomplete>
        </el-form-item>
        <el-form-item label="检测字段" prop="checkRuleColumn">
          <el-select
            v-model="queryData.checkRuleColumn"
            placeholder="请选择"
            :disabled="isDisabled"
            @click.native="handlerclick"
            filterable
          >
            <el-option label="全部" value=""></el-option>
            <el-option
              v-for="item in checkRuleColumnData"
              :key="item.id"
              :label="item.fieldName"
              :value="item.fieldName"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="规则大类" prop="checkRuleFatherType">
          <el-select
            v-model="queryData.checkRuleFatherType"
            @change="handlerChange"
          >
            <el-option label="全部" value=""></el-option>
            <el-option
              v-for="item in checkRuleFatherTypeData"
              :key="item.id"
              :label="item.contentValue"
              :value="item.contentKey"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="规则小类" prop="checkRuleType">
          <el-select v-model="queryData.checkRuleType" placeholder="请选择">
            <el-option label="全部" value=""></el-option>
            <el-option
              v-for="item in checkRuleTypeData"
              :key="item.id"
              :label="item.contentValue"
              :value="item.contentKey"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="问题名称" prop="dataQltyQstnNm">
          <el-input
            v-model="queryData.dataQltyQstnNm"
            placeholder="请输入问题或名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="检测时间">
          <el-row>
            <el-col :span="11">
              <el-form-item prop="checkDt">
                <el-date-picker
                  type="datetime"
                  placeholder="请选择开始时间"
                  v-model="queryData.checkDt"
                  style="width: 100%"
                  value-format="yyyy-MM-dd hh:mm:ss"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col class="line" :span="2">-</el-col>
            <el-col :span="11">
              <el-form-item prop="checkEndDt">
                <el-date-picker
                  style="width: 100%"
                  value-format="yyyy-MM-dd hh:mm:ss"
                  type="datetime"
                  placeholder="请选择结束时间"
                  v-model="queryData.checkEndDt"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="处理时间">
          <el-row>
            <el-col :span="11">
              <el-form-item prop="handleDt">
                <el-date-picker
                  type="datetime"
                  placeholder="请选择开始时间"
                  v-model="queryData.handleDt"
                  value-format="yyyy-MM-dd hh:mm:ss"
                  style="width: 100%"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col class="line" :span="2">-</el-col>
            <el-col :span="11">
              <el-form-item prop="handleEndDt">
                <el-date-picker
                  style="width: 100%"
                  value-format="yyyy-MM-dd hh:mm:ss"
                  type="datetime"
                  placeholder="请选择结束时间"
                  v-model="queryData.handleEndDt"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item>
          <div style="margin-left: 10px">
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="searchExecutionRecord"
              >搜索</el-button
            >
          </div>
        </el-form-item>
        <el-form-item>
          <el-button @click="resetForm('ruleForm')">重置</el-button>
        </el-form-item>
      </el-form>
      <div></div>
    </div>
    <div class="quality-problem-main">
      <div class="quality-problem-table">
        <el-table
          :data="tableData"
          style="width: 100%"
          border
          v-loading="loading"
          :header-cell-style="{ background: '#F5F7FA', color: '#606266' }"
        >
          <el-table-column
            label="序号"
            type="index"
            width="50"
          ></el-table-column>
          <el-table-column
            prop="dataQltyQstnNm"
            label="问题名称"
          ></el-table-column>
          <el-table-column
            prop="qstnPrdusSysNm"
            width="100"
            label="问题产生系统"
          ></el-table-column>
          <el-table-column
            prop="checkRuleName"
            label="规则名称"
          ></el-table-column>
          <el-table-column prop="checkRuleFatherType" label="规则大类">
            <template slot-scope="scope">
              <span
                v-for="item in checkRuleFatherTypeData"
                :key="item.contentKey"
              >
                <span v-if="scope.row.checkRuleFatherType === item.contentKey">
                  {{ item.contentValue }}
                </span>
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="checkRuleType" label="规则小类" width="100">
            <template slot-scope="scope">
              <span v-for="item in checkRuleTypeAllData" :key="item.contentKey">
                <span v-if="scope.row.checkRuleType == item.contentKey">
                  {{ item.contentValue }}
                </span>
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="checkRuleTableOrView"
            width="180"
            label="表名"
          ></el-table-column>
          <el-table-column
            prop="checkRuleColumn"
            width="120"
            label="字段名"
          ></el-table-column>
          <el-table-column
            prop="pbSubsidiarySql"
            width="180"
            label="检查SQL或其他附加信息"
            ><template slot-scope="scope">
              <el-popover placement="left" width="400" trigger="hover">
                <span>{{ scope.row.pbSubsidiarySql }}</span>
                <el-button type="text" size="small" slot="reference"
                  >查看详情</el-button
                >
              </el-popover>
            </template></el-table-column
          >
          <el-table-column prop="dataQstnNum" label="问题数"></el-table-column>
          <el-table-column prop="dataTotalNum" label="检测数"></el-table-column>
          <el-table-column label="错误率">
            <template slot-scope="scope">
              <span>{{
                (
                  (scope.row.dataQstnNum / scope.row.dataTotalNum) *
                  100
                ).toFixed(2) + "%"
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="qstnCheckTime"
            width="180"
            label="问题检测时间"
          ></el-table-column>

          <el-table-column prop="qstnClsfCd" label="问题分类">
            <template slot-scope="scope">
              <span v-for="item in qstnClsfCdData" :key="item.contentKey">
                <span v-if="scope.row.qstnClsfCd === item.contentKey">
                  {{ item.contentValue }}
                </span>
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="qstnRsnClsfCd" label="问题原因">
            <template slot-scope="scope">
              <span v-for="item in qstnRsnClsfCdData" :key="item.contentKey">
                <span v-if="scope.row.qstnRsnClsfCd === item.contentKey">
                  {{ item.contentValue }}
                </span>
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="rctfctnSchemTypCd" label="整改方式">
            <template slot-scope="scope">
              <span
                v-for="item in rctfctnSchemTypCdData"
                :key="item.contentKey"
              >
                <span v-if="scope.row.rctfctnSchemTypCd === item.contentKey">
                  {{ item.contentValue }}
                </span>
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="rctfctnOpinions"
            label="描述"
          ></el-table-column>
          <el-table-column prop="qstnRctfctnStusCd" label="问题处理状态">
            <template slot-scope="scope">
              <span
                v-for="item in qstnRctfctnStusCdData"
                :key="item.contentKey"
              >
                <span v-if="scope.row.qstnRctfctnStusCd === item.contentKey">
                  {{ item.contentValue }}
                </span>
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="qstnHandleTime"
            label="问题处理时间"
          ></el-table-column>
          <el-table-column
            prop="pushMessageReceiver"
            width="300"
            label="推送消息接收方"
          >
            <template slot-scope="scope">
              <div
                v-for="(item, index) in scope.row.pushMessageReceivers"
                :key="index"
              >
                {{ item.account }}
              </div>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="240">
            <template slot-scope="scope">
              <el-button
                v-if="
                  scope.row.qstnRctfctnStusCd === '2' ||
                  scope.row.qstnRctfctnStusCd === '3'
                "
                size="mini"
                @click="handleShowDialog(scope.$index, scope.row, '1')"
                type="primary"
                >关闭</el-button
              >

              <el-button
                v-if="scope.row.qstnRctfctnStusCd === '3'"
                size="mini"
                @click="handleShowDialog(scope.$index, scope.row, '2')"
                type="primary"
                >处理</el-button
              >

              <el-button
                v-if="scope.row.qstnRctfctnStusCd === '1'"
                @click="undo(scope.$index, scope.row, '3')"
                size="mini"
                type="primary"
                >撤销关闭</el-button
              >

              <el-button
                v-if="scope.row.qstnRctfctnStusCd === '2'"
                @click="undo(scope.$index, scope.row, '4')"
                size="mini"
                type="primary"
                >撤销处理</el-button
              >

              <el-button
                size="mini"
                @click="handleShowDialog(scope.$index, scope.row, '5')"
                type="primary"
                >查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="quality-problem-pag">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryData.pageNum"
          :page-sizes="[5, 10, 15, 20]"
          :page-size="queryData.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalNum"
        >
        </el-pagination>
      </div>
      <!-- 关闭/处理/查看dialog -->
      <ShutDownDisposeView
        ref="shutDownDisposeView"
        @queryList="queryList"
        :btnType="btnType"
        :checkRuleTypeData="checkRuleTypeData"
        :checkRuleFatherTypeData="checkRuleFatherTypeData"
        :row="row"
      />
    </div>
  </div>
</template>

<script>
import tableViewAndField from "@/mixins/tableViewAndField"
import getQualityProblemCommonList from "@/mixins/getQualityProblemCommonList"
import ShutDownDisposeView from "./components/ShutDownDisposeView.vue"
import {
  queryList,
  getAllSysAndDb,
  editQstnInfo,
} from "@/api/qualityProblemMgt/qualityProblemMgt"
import {
  getRuleExecutionTable,
  getRuleExecutionTableField,
} from "@/api/qualityRuleMgt/ruleExecution"
export default {
  components: {
    ShutDownDisposeView,
  },
  mixins: [tableViewAndField, getQualityProblemCommonList],
  watch: {
    queryData: {
      handler(newVal) {
        if (!newVal.checkRuleTableOrView) {
          this.isDisabled = true
          this.queryData.checkRuleColumn = ""
        }
        if (!newVal.dbNm) this.isTableOrViewDisabled = true
      },
      deep: true,
    },
  },
  methods: {
    // 关闭/处理/查看时打开dialog
    handleShowDialog(index, row, type) {
      this.$refs.shutDownDisposeView.dialogFormVisible = true
      this.row = row
      this.btnType = type
    },
    // 点击检测表或视图输入框时
    handlerTableOrViewClick() {
      if (this.isTableOrViewDisabled) {
        this.$message({
          type: "warning",
          message: "请先选择系统及数据源",
        })
      }
    },
    // 撤销关闭和撤销处理时
    undo(index, row, type) {
      this.editData = JSON.parse(JSON.stringify(row))
      if (type === "3") {
        this.editData.operationType = type
      } else {
        this.editData.operationType = type
      }
      this.$confirm(
        `此操作将${type === "3" ? "撤销关闭" : "撤销处理"}, 是否继续?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          this.editQstnInfo()
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: `已取消${type === "3" ? "撤销关闭" : "撤销处理"}操作`,
          })
        })
    },
    // 编辑
    editQstnInfo() {
      editQstnInfo(this.editData).then((res) => {
        if (res.status !== 0) {
          this.$message({
            message: res.msg,
            type: "error",
          })
        }
        this.$message({
          message: res.msg,
          type: "success",
        })
        this.queryList()
      })
    },
    // 选择系统及数据源时
    handlerSysAndDbChange(e) {
      if (e) {
        let curSelectItem = this.SysAndDbData.find((item) => {
          return item.dbNm === e
        })
        this.isTableOrViewDisabled = false
        this.queryData.checkRuleTableOrView = ""
        this.queryData.sysCode = curSelectItem.sysCode
        this.queryRuleTableOrViewData.dbNm = curSelectItem.dbNm
        this.queryRuleTableOrViewData.sysCode = curSelectItem.sysCode
        this.queryCheckRuleColumnData.dbNm = curSelectItem.dbNm
        this.queryCheckRuleColumnData.sysCode = curSelectItem.sysCode
      } else {
        this.isTableOrViewDisabled = true
        this.queryData.checkRuleTableOrView = ""
        this.queryData.sysCode = ""
        this.queryRuleTableOrViewData.dbNm = ""
        this.queryRuleTableOrViewData.sysCode = ""
        this.queryCheckRuleColumnData.dbNm = ""
        this.queryCheckRuleColumnData.sysCode = ""
      }
    },
    // 搜索问题管理列表
    searchExecutionRecord() {
      this.queryList()
    },
    // 查询问题管理列表
    queryList() {
      this.loading = true
      queryList(this.queryData).then((res) => {
        if (res.status === 0) {
          this.tableData = res.data.list
          this.totalNum = res.data.total
          this.loading = false
        } else {
          this.loading = false
          this.$message({
            message: res.msg,
            type: "error",
          })
        }
      })
    },
    // 获取表对应的字段
    getRuleExecutionTableField() {
      getRuleExecutionTableField(this.queryCheckRuleColumnData).then((res) => {
        if (res.status === 0) {
          let checkRuleColumnDataArr = []
          res.data.forEach((el, index) => {
            checkRuleColumnDataArr.push({ id: index, fieldName: el })
          })
          this.checkRuleColumnData = checkRuleColumnDataArr
        }
      })
    },
    // 异步搜索 当前系统和数据库的表
    querySearchAsync(queryString, cb) {
      this.checkRuleTableOrViewData = []
      this.queryRuleTableOrViewData.tableName = queryString
      getRuleExecutionTable(this.queryRuleTableOrViewData).then((res) => {
        if (res.status === 0) {
          res.data.forEach((element, index) => {
            this.checkRuleTableOrViewData.push({
              id: index,
              tableOrViewName: element,
            })
          })
          cb(this.checkRuleTableOrViewData)
        }
      })
    },
    // 查询系统及数据源列表
    getAllSysAndDbList() {
      getAllSysAndDb().then((res) => {
        if (res.status === 0) {
          res.data.forEach((item, index) => {
            this.SysAndDbData.push({
              id: index,
              ...item,
            })
          })
        }
      })
    },
    // 当规则大类进行选择时
    handlerChange(val) {
      if (val == "") {
        this.getCodeValueContent("checkRuleTypeData", "checkRuleType")
      } else {
        this.getCodeValueContent("checkRuleTypeData", val)
      }
      this.queryData.checkRuleType = ""
    },
    // 先选择表或视图
    handlerclick() {
      if (this.isDisabled) {
        this.$message({
          type: "warning",
          message: "请先选择检测表或视图内容",
        })
        this.$refs["abc"].focus()
      }
    },
    // 重置
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.queryList()
    },
    // 当当前检测表或视图选择时
    handlerTableOrViewSelect(val) {
      this.isDisabled = false
      this.queryCheckRuleColumnData.tableName = val.tableOrViewName
      this.getRuleExecutionTableField()
    },
    // 改变页数
    handleCurrentChange(val) {
      this.queryData.pageNum = val
      this.queryList()
    },
    // 改变页面显示条数
    handleSizeChange(val) {
      this.queryData.pageSize = val
      this.queryList()
    },
  },
  data() {
    return {
      queryData: {
        // 查询数据
        checkDt: "",
        checkEndDt: "",
        handleDt: "",
        handleEndDt: "",
        checkRuleColumn: "", // 检核字段
        checkRuleFatherType: "", // 检核规则父类型
        checkRuleTableOrView: "", // 	检查表或视图
        checkRuleType: "", // 检核规则类型
        dataQltyQstnNm: "", // 问题名称
        dbNm: "", // 	数据库名称
        pageNum: 1,
        pageSize: 10,
        sysCode: "",
      },
      btnType: "1", // 按钮类型
      checkRuleFatherTypeData: [], // 规则大类列表数据
      checkRuleTypeData: [], // 规则小类列表数据
      checkRuleTypeAllData: [], // 规则小类所有数据
      qstnRctfctnStusCdData: [], // 问题处理状态列表数据
      editData: {},
      queryRuleTableOrViewData: {
        // 查询表跟视图
        dbNm: "",
        fieldName: "",
        pageNum: 1,
        pageSize: 9999,
        sysCode: "",
        tableName: "",
      },
      queryCheckRuleColumnData: {
        // 查询表对应的字段
        dbNm: "",
        fieldName: "",
        pageNum: 1,
        pageSize: 9999,
        sysCode: "",
        tableName: "",
      },
      row: {},
      isTableOrViewDisabled: true,
      SysAndDbData: [], // 系统及数据源数据
      tableData: [], // 表格数据
      totalNum: 1,
      loading: false,
    }
  },
  created() {
    // 初始化查询问题管理列表
    this.queryList()
    this.getAllSysAndDbList()
    this.getCodeValueContent("checkRuleFatherTypeData", "checkRuleFatherType")
    this.getCodeValueContent("checkRuleTypeData", "checkRuleType")
    this.getCodeValueContent("checkRuleTypeAllData", "checkRuleType")
    this.getCodeValueContent("qstnRctfctnStusCdData", "qstnRctfctnStusCd")
  },
}
</script>

<style lang="scss" scoped>
.quality-problem-container {
  margin-bottom: 40px;

  .quality-problem-header {
    display: flex;
    margin: 10px 0;
    min-width: 1200px;

    .search {
      margin: 0 20px;
    }
  }

  .quality-problem-main {
    // padding-left: 10px;
    .quality-problem-table {
      margin-top: 10px;
    }

    .quality-problem-dialog {
      .mgt-dialog-upload {
        margin-left: 50px;
      }
    }

    .quality-problem-pag {
      margin-top: 10px;
    }
  }
}
</style>
