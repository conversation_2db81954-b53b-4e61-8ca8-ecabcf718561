<template>
  <div class="dictionary-details-container">
    <div class="dictionary-details-header">
      <el-form :model="queryData" :inline="true">
        <el-form-item style="margin-right: 30px">
          <el-button
            type="text"
            size="medium"
            @click="goBack"
            icon="el-icon-arrow-left"
            ><span>返回</span></el-button
          >
        </el-form-item>
        <el-form-item label="映射类别" prop="mappingsType">
          <el-select
            v-model="queryData.mappingsType"
            placeholder="请选择映射类别"
          >
            <el-option label="全部" value=""></el-option>
            <el-option label="互联互通字典映射" value="1"></el-option>
            <el-option label="院内字典映射" value="2"></el-option>
            <el-option label="三医监管" value="3"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="术语编码" prop="termId">
          <el-input
            v-model="queryData.termId"
            placeholder="请输入术语编码"
          ></el-input>
        </el-form-item>
        <el-form-item label="匹配状态" prop="isMatch">
          <el-select
            v-model="queryData.isMatch"
            placeholder="请选择匹配状态"
          >
            <el-option label="全部" value=""></el-option>
            <el-option label="已匹配" value="Y"></el-option>
            <el-option label="未匹配" value="N"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="源字段编码" prop="sourceFieldCode">
          <el-input
            v-model="queryData.sourceFieldCode"
            placeholder="请输入源字段编码"
          ></el-input>
        </el-form-item>
        <el-form-item label="源字段" prop="sourceField">
          <el-input
            v-model="queryData.sourceField"
            placeholder="请输入源字段"
          ></el-input>
        </el-form-item>
        <el-form-item label="目标字段编码" prop="targetFieldCode">
          <el-input
            v-model="queryData.targetFieldCode"
            placeholder="请输入目标字段编码"
          ></el-input>
        </el-form-item>
        <el-form-item label="目标字段" prop="targetField">
          <el-input
            v-model="queryData.targetField"
            placeholder="请输入目标字段"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="searchDictDetails"
            icon="el-icon-search"
            >搜索</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <div class="tips">
      <span>术语编码：{{ curDictionaryData.termId }}</span>
      <span>术语名称：{{ curDictionaryData.termName }}</span>
      <span>字典对应表名：{{ curDictionaryData.mappingsTable }}</span>
      <span>系统编码：{{ curDictionaryData.sysCode }}</span>
      <span>标准类别名称：{{ curDictionaryData.nameField }}</span>
      <el-divider></el-divider>
    </div>
    <div class="dictionary-details-main">
      <div class="dictionary-details-table">
        <el-table
          :data="tableData"
          style="width: 100%"
          border
          v-loading="loading"
          :header-cell-style="{ background: '#F5F7FA', color: '#606266' }"
        >
          <el-table-column prop="sourceFieldCode" label="源字段编码"> </el-table-column>
          <el-table-column prop="sourceField" label="源字段名称"> </el-table-column>
          <el-table-column prop="targetFieldCode" label="目标字段编码"> </el-table-column>
          <el-table-column prop="targetField" label="目标字段"> </el-table-column>
        </el-table>
      </div>
      <div class="dictionary-details-pag">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryData.pageNum"
          :page-sizes="[5, 10, 15, 20]"
          :page-size="queryData.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalNum"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { getDictDataList } from "@/api/dictionary-mgt/dictionary-details"
export default {
  data() {
    return {
      queryData: {
        isMatch: "", // 是否匹配
        mappingsType: "", // 映射类型
        pageNum: 1,
        pageSize: 10,
        sourceField: "", // 源字段
        sourceFieldCode: "", // 源字段编码
        targetField: "", // 目标字段
        targetFieldCode: "", // 目标字段编码
        termId: "", // 术语编码
      },
      tableData: [], // 表格数据
      totalNum: 1,
      loading: false,
      curDictionaryData: {},
    }
  },
  created() {
    this.curDictionaryData = this.$route.params.curDictionaryData
    this.queryData.termId = this.curDictionaryData.termId
    this.queryData.mappingsType = this.curDictionaryData.mappingsType
    // 进入页面初始化查询
    this.queryDictDetailsList()
  },
  methods: {
    // 查询字典详情列表
    queryDictDetailsList() {
      this.loading = true
      getDictDataList(this.queryData).then((res) => {
        if (res.status === 0) {
          this.tableData = res.data.list
          this.totalNum = res.data.total
          this.loading = false
        } else {
          this.$message({
            type: "error",
            message: res.msg
          })
          this.loading = false
        }
      })
    },
    // 返回上一级
    goBack() {
      this.$router.go(-1)
    },
    // 搜索任务组
    searchDictDetails() {
      this.queryDictDetailsList()
    },
    // 改变页面显示条数
    handleSizeChange(val) {
      this.queryData.pageSize = val
      this.queryDictDetailsList()
    },
    // 改变页数
    handleCurrentChange(val) {
      this.queryData.pageNum = val
      this.queryDictDetailsList()
    },
  },
}
</script>

<style scoped lang="scss">
.dictionary-details-container {
  margin-bottom: 40px;
  // padding-left: 10px;
  .dictionary-details-header {
    display: flex;
    margin: 10px 0;
    min-width: 800px;
    .search {
      margin: 0 20px;
    }
    .delete {
      margin-left: 20px;
    }
  }
  .tips {
    margin-bottom: 30px;
    > span {
      margin-right: 90px;
      color: rgba($color: #000000, $alpha: 0.6);
      font-size: 14px;
    }
  }
  .dictionary-details-main {
    .dictionary-details-table {
      margin-top: 10px;
    }
    .dictionary-details-dialog {
      .mgt-dialog-upload {
        margin-left: 50px;
      }
    }
    .dictionary-details-pag {
      margin-top: 10px;
    }
  }
  ::v-deep .el-dialog {
    // dialog的定位
    top: -60px;
    .el-dialog__body {
      // height: 74vh;
      overflow: auto;
    }
  }
}
</style>
