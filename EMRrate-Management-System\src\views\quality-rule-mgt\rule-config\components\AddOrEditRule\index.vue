<template>
  <div class="add-edit-rule">
    <el-dialog
      v-dialogDrag
      :title="btnTitle"
      :visible.sync="dialogFormVisible"
      @open="handlerOpen"
      @closed="handlerClose"
      width="46%"
      :close-on-click-modal="false"
    >
      <el-form
        :model="formData"
        :rules="rules"
        ref="ruleForm"
        label-width="140px"
        class="demo-ruleForm"
      >
        <el-form-item label="规则名称" prop="checkRuleName">
          <el-input v-model="formData.checkRuleName"></el-input>
        </el-form-item>
        <el-form-item label="规则说明" prop="checkRuleDesc">
          <el-input v-model="formData.checkRuleDesc"></el-input>
        </el-form-item>
        <el-form-item label="问题分类" prop="qstType">
          <el-radio-group v-model="formData.qstType">
            <el-radio
              v-for="item in problemTypeData"
              :key="item.id"
              :label="item.contentValue"
            ></el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否启用" prop="checkRuleStatus">
          <el-radio-group v-model="formData.checkRuleStatus">
            <el-radio label="0">是</el-radio>
            <el-radio label="1">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="规则大类" prop="checkRuleFatherType">
          <el-select
            v-model="formData.checkRuleFatherType"
            @change="handlerChange"
          >
            <el-option
              v-for="item in checkRuleFatherTypeData"
              :key="item.id"
              :label="item.contentValue"
              :value="item.contentKey"
            ></el-option>
          </el-select>
        </el-form-item>
        <template v-if="formData.checkRuleFatherType">
          <el-form-item label="规则小类" prop="checkRuleType">
            <div style="width: 70%" v-loading="loading">
              <el-radio-group
                @change="handlerRuleTypeChange"
                v-model="formData.checkRuleType"
              >
                <el-radio
                  v-for="item in checkRuleTypeData"
                  :key="item.id"
                  :label="item.contentKey"
                  >{{ item.contentValue }}</el-radio
                >
              </el-radio-group>
            </div>
          </el-form-item>
        </template>

        <!-- 完整性start -->
        <template v-if="formData.checkRuleFatherType == 'WZX'">
          <IntegrityForm
            @clearCheckSQLData="clearCheckSQLData"
            :dataSourceId="this.dataSourceId"
            :formData="formData"
          >
            <CheckRuleSQL
              :isLoading="isLoading"
              @goCheck="goCheck('ruleForm')"
              :checkSQLData="checkSQLData"
            />
          </IntegrityForm>
        </template>
        <!-- 完整性end -->

        <!-- 一致性start -->
        <template v-if="formData.checkRuleFatherType == 'YZX'">
          <ConsistentForm
            @clearCheckSQLData="clearCheckSQLData"
            :dataSourceId="this.dataSourceId"
            :formData="formData"
          >
            <CheckRuleSQL
              :isLoading="isLoading"
              @goCheck="goCheck('ruleForm')"
              :checkSQLData="checkSQLData"
            />
          </ConsistentForm>
        </template>
        <!-- 一致性end -->

        <!-- 准确性start -->
        <template v-if="formData.checkRuleFatherType == 'ZQX'">
          <AccuracyForm
            @clearCheckSQLData="clearCheckSQLData"
            :dataSourceId="this.dataSourceId"
            :formData="formData"
          >
            <CheckRuleSQL
              :isLoading="isLoading"
              @goCheck="goCheck('ruleForm')"
              :checkSQLData="checkSQLData"
            />
          </AccuracyForm>
        </template>
        <!-- 准确性end -->

        <!-- 有效性start -->
        <template v-if="formData.checkRuleFatherType == 'YXX'">
          <EffectForm
            @clearCheckSQLData="clearCheckSQLData"
            :dataSourceId="this.dataSourceId"
            :formData="formData"
          >
            <CheckRuleSQL
              :isLoading="isLoading"
              @goCheck="goCheck('ruleForm')"
              :checkSQLData="checkSQLData"
            />
          </EffectForm>
        </template>
        <!-- 有效性end -->

        <!-- 规范性start -->
        <template v-if="formData.checkRuleFatherType == 'GFX'">
          <NormativeForm
            @clearCheckSQLData="clearCheckSQLData"
            :dataSourceId="this.dataSourceId"
            :formData="formData"
          >
            <CheckRuleSQL
              :isLoading="isLoading"
              @goCheck="goCheck('ruleForm')"
              :checkSQLData="checkSQLData"
            />
          </NormativeForm>
        </template>
        <!-- 规范性end -->

        <!-- 及时性start -->
        <template v-if="formData.checkRuleFatherType == 'JSX'">
          <TimelinessForm
            @clearCheckSQLData="clearCheckSQLData"
            :dataSourceId="this.dataSourceId"
            :formData="formData"
          >
            <CheckRuleSQL
              :isLoading="isLoading"
              @goCheck="goCheck('ruleForm')"
              :checkSQLData="checkSQLData"
            />
          </TimelinessForm>
        </template>
        <!-- 及时性end -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" v-if="btnType!==3" @click="submitForm('ruleForm')"
          >保存</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ruleFatherAndRuleType from "@/mixins/ruleFatherAndRuleType"
import tableViewAndField from "@/mixins/tableViewAndField"
import IntegrityForm from "./components/IntegrityForm.vue"
import ConsistentForm from "./components/ConsistentForm.vue"
import AccuracyForm from "./components/AccuracyForm.vue"
import EffectForm from "./components/EffectForm.vue"
import NormativeForm from "./components/NormativeForm.vue"
import TimelinessForm from "./components/TimelinessForm.vue"
import CheckRuleSQL from "./components/common/CheckRuleSQL.vue"
import {
  addRule,
  updateRule,
  getCheckSql,
  getCopiedSerialNumber,
} from "@/api/qualityRuleMgt/ruleConfig"
import { removeEndingNumber } from "@/utils"

export default {
  components: {
    IntegrityForm,
    ConsistentForm,
    AccuracyForm,
    EffectForm,
    NormativeForm,
    TimelinessForm,
    CheckRuleSQL,
  },
  mixins: [ruleFatherAndRuleType, tableViewAndField],
  watch: {
    row1: {
      handler(newVal, oldVal) {
        if (this.btnType === 0) {
          // 当连续点击编辑的规则 规则大类相同时 直接用上一次获取的规则小类数据 减少不必要的请求
          if (newVal.checkRuleFatherType == oldVal.checkRuleFatherType) {
            return
          }
          this.queryCheckRuleTypeData.typeCode = newVal.checkRuleFatherType
          this.getCheckRuleType()
        }
      },
      deep: true,
    },
  },
  computed: {
    titleBtnType() {},
  },
  methods: {
    clearCheckSQLData() {
      this.checkSQLData = ""
    },
    // 获取检核规则SQL时进行校验
    goCheck(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (!this.formData.checkRuleColumn) {
            this.parseField()
          }
          this.getCheckSql()
        } else {
          this.$message({
            type: "warning",
            message: "请先完成必填项",
          })
          return false
        }
      })
    },
    // 获取检核规则SQL
    getCheckSql() {
      this.isLoading = true
      getCheckSql(this.formData).then((res) => {
        if (res.status !== 0) {
          this.$message({
            message: res.msg,
            type: "error",
          })
          this.isLoading = false
          return
        }
        this.checkSQLData = res.data.qstDetailFieldSql
        this.isLoading = false
      })
    },
    // 处理dialog打开时
    async handlerOpen() {
      this.formData.sysCode = this.sysCode
      this.formData.checkSchema = this.databaseSchema
      this.formData.dbNm = this.databaseName
      this.formData.sysName = this.sysName

      var titleArr = ["编辑规则", "新增规则", "复制规则", "查看规则"]
      this.btnTitle = titleArr[this.btnType]

      if (this.checkRuleFatherTypeData.length == 0) {
        // 减少不必要的请求
        this.getCheckRuleFatherType()
      }

      this.formDataCopy = JSON.parse(JSON.stringify(this.formData))

      if (this.btnType != 1) {
        // 编辑时
        if (this.row1.checkRuleFatherType) {
          this.queryCheckRuleTypeData.typeCode = this.row1.checkRuleFatherType
          this.getCheckRuleType()
        }
        this.formData = this.row1
        // 复制时
        if (this.btnType == 2) {
          const checkRuleName = this.row1.checkRuleName
          const { status, data } = await getCopiedSerialNumber({
            checkRuleName,
          })
          if (status != 0) {
            this.$message({
              message: res.msg,
              type: "error",
            })
            return
          }
          if (data) {
            this.formData.checkRuleName =
              removeEndingNumber(checkRuleName) + "_" + data
          }
        }
        this.$nextTick(() => {
          this.checkSQLData = this.row1.pbSubsidiarySql
        })
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (!this.formData.checkRuleColumn) {
            this.parseField()
          }
          if (this.btnType == 0) {
            this.updateRule()
          } else {
            this.addRule()
          }
        } else {
          return false
        }
      })
    },
    // 解析主副键字段
    parseField() {
      let primaryKeyTableFieldArr = []
      let secondaryKeyTableFieldArr = []
      this.formData.primaryForeignFileds.forEach((item) => {
        primaryKeyTableFieldArr.push(item.primaryKeyTableField)
        secondaryKeyTableFieldArr.push(item.secondaryKeyTableField)
      })
      this.formData.checkRuleColumn = primaryKeyTableFieldArr.join(",")
      this.formData.stCheckRuleColumn = secondaryKeyTableFieldArr.join(",")
    },
    // 新增规则
    addRule() {
      addRule(this.formData).then((res) => {
        if (res.status == 0) {
          this.$message({
            message: "新增成功",
            type: "success",
          })
          this.$parent.getConfigPageList()
        } else {
          this.$message({
            message: res.msg,
            type: "error",
          })
        }
        this.dialogFormVisible = false
      })
    },
    // 更新规则
    updateRule() {
      updateRule(this.formData).then((res) => {
        if (res.status !== 0) {
          this.$message({
            message: res.msg,
            type: "error",
          })
          return
        }
        this.$message({
          message: "更新成功",
          type: "success",
        })
        this.$parent.getConfigPageList()
        this.dialogFormVisible = false
      })
    },
    // 当规则大类进行选择时
    handlerChange(val) {
      this.checkSQLData = ""
      this.queryCheckRuleTypeData.typeCode = val
      this.getCheckRuleType()
      this.formData = JSON.parse(
        JSON.stringify(Object.assign(this.formData, this.resetData2))
      ) // 合并重置数据
    },
    // 规则小类选择时重置部分选项
    handlerRuleTypeChange(val) {
      const timer = setTimeout(() => {
        this.$refs["ruleForm"].clearValidate() // 移除所有校验结果
        clearTimeout(timer)
      }, 0)
      this.checkSQLData = ""
      this.formData = JSON.parse(
        JSON.stringify(Object.assign(this.formData, this.resetData2))
      ) // 合并重置数据
    },
    // 处理dialog关闭后
    handlerClose() {
      this.formData = this.formDataCopy
      this.checkSQLData = ""
      this.$refs["ruleForm"].resetFields()
    },
  },
  data() {
    return {
      dialogFormVisible: false, // 弹框状态
      isLoading: false,
      loading: true,
      checkSQLData: "", // 检核SQL结果数据
      btnTitle: "新增规则",
      formData: {
        // 表单数据
        checkRuleName: "", // 检核规则名称
        checkRuleDesc: "", // 检核规则说明
        qstType: "", // 问题分类编码：提示，预警，严重
        checkRuleStatus: "", // 检核规则状态 0：启用 1：未启用
        checkRuleFatherType: "", // 检核规则父类型，完整性，一致性，准确性，有效性，规范性，及时性
        checkRuleType: "", // 检核规则类型，整体完整性，条件完整性，一致性，唯一性，计算正确性，代码有效性，范围有效性，长度规范性，及时性，自定义（每个大类一个）
        checkRuleTableOrView: "", // 检查表或视图
        checkRuleColumn: "", // 检核字段
        isnulltag: "", // 检核方式0:null，1：空字符串
        checkRuleWhere: "", // 	where条件
        questionNmSql: "", // 问题数SQL
        pbSubsidiarySql: "", // 问题明细SQL
        stCheckRuleTableOrView: "", // 副表表名或视图名/字典或视图
        stCheckRuleColumn: "", // 副表字段名/明细表字段1/字典字段
        cdValFieldNm: "", // 码值字段名称/明细表字段2
        sysName: "", // 检核系统名称
        sysCode: "", // 检核系统代码
        checkSchema: "", // 检核SCHEMA
        dbNm: "", // 	数据库名称
        calcRuleCd: "", // 计算规则代码
        checkObjectType: "", // 值域：0数值、1日期 ,2字符串；长度：0等于、1不等于、2大于、3小于、4.大于等于、5小于等于 ； 一致性：count、sum
        maxValue: "", // 最大值
        maxValueContain: "1", // 最大值是否包含0：包含 1不包含
        minValue: "", // 最小值
        minValueContain: true, // 最小值是否包含0：包含 1不包含
        columnsLength: "", // 	字段长度
        resultSmlNmrlDgit: "", // 结果小数位数/字段精度
        dtExecCondDescr: "", // 日期执行条件描述(0：T 1：T-1 2：T-2；3:T-3)
        primaryForeignFileds: [
          {
            primaryKeyTableField: "",
            secondaryKeyTableField: "",
          },
        ],
        cdVal: "", // 代码值/类型
        checkRuleColumnCn: "", // 检测字段中文
        checkRuleId: "", // 	规则ID
        checkRuleTableOrViewCn: "", // 检查表或视图中文名
        createDate: "", // 	创建时间
        fieldDataTyp: "", // 字段数据类型
        isTableOrView: "", // 规则是表还是视图0是表1是视图
        pbSubsidiaryColumns: "", // 问题明细列
        prcsnExecCondCd: "", // 精度执行条件代码
        stCheckRuleColumnCn: "", // 副表字段中文名
        stCheckRuleTableOrViewCn: "", // 副表表中文名或视图中文名
        stCheckSchema: "", // 副表检核SCHEMA代码
        stIsTableOrView: "", // 是外键的表还是视图0是表1是视图
        totalNmSql: "", // 总数据记录数SQL
        updateDate: "", // 修改时间
      },
      // 重置数据
      resetData2: {
        checkRuleTableOrView: "", // 检查表或视图
        checkRuleColumn: "", // 检核字段
        isnulltag: "", // 检核方式0:null，1：空字符串
        checkRuleWhere: "", // 	where条件
        questionNmSql: "", // 问题数SQL
        pbSubsidiarySql: "", // 问题明细SQL
        columnsLength: "", // 	字段长度
        calcRuleCd: "", // 计算规则代码
        cdVal: "", // 代码值
        cdValFieldNm: "", // 码值字段名称/明细表字段2
        checkObjectType: "", // 值域：0数值、1日期 ,2字符串；长度：0等于、1不等于、2大于、3小于、4.大于等于、5小于等于 ； 一致性：count、sum
        prcsnExecCondCd: "", // 精度执行条件代码
        checkRuleColumnCn: "", // 检测字段中文
        checkRuleTableOrViewCn: "", // 检查表或视图中文名
        createDate: "", // 	创建时间
        dtExecCondDescr: "", // 日期执行条件描述
        primaryForeignFileds: [
          // 主外键对应关系
          {
            primaryKeyTableField: "", // 主键表字段
            secondaryKeyTableField: "", // 副键表字段
          },
        ],
        fieldDataTyp: "", // 字段数据类型
        isTableOrView: "", // 规则是表还是视图0是表1是视图
        maxValue: "", // 最大值
        maxValueContain: "", // 最大值是否包含0：包含 1不包含
        minValue: "", // 最小值
        minValueContain: "", // 最小值是否包含0：包含 1不包含
        pbSubsidiaryColumns: "", // 问题明细列
        resultSmlNmrlDgit: "", // 结果小数位数
        stCheckRuleColumn: "", // 副表字段名/明细表字段1
        stCheckRuleColumnCn: "", // 副表字段中文名
        stCheckRuleTableOrView: "", // 副表表名或视图名
        stCheckRuleTableOrViewCn: "", // 副表表中文名或视图中文名
        stCheckSchema: "", // 副表检核SCHEMA代码
        stIsTableOrView: "", // 是外键的表还是视图0是表1是视图
        totalNmSql: "", // 总数据记录数SQL
        updateDate: "", // 修改时间
      },
      rules: {
        checkRuleName: [
          { required: true, message: "请输入规则名称", trigger: "blur" },
        ],
        qstType: [{ required: true, message: "请选择分类", trigger: "change" }],
        checkRuleStatus: [
          { required: true, message: "请选择是否启用", trigger: "change" },
        ],
        checkRuleFatherType: [
          { required: true, message: "请选择规则", trigger: "change" },
        ],
        checkRuleType: [
          { required: true, message: "请选择规则", trigger: "blur" },
        ],
        checkRuleTableOrView: [
          { required: true, message: "请选择表或视图", trigger: "change" },
        ],
        stCheckRuleTableOrView: [
          { required: true, message: "请选择表或视图", trigger: "change" },
        ],
        checkRuleColumn: [
          { required: true, message: "请选择字段", trigger: "change" },
        ],
        stCheckRuleColumn: [
          { required: true, message: "请选择字段", trigger: "change" },
        ],
        isnulltag: [
          { required: true, message: "请选择检核方式", trigger: "change" },
        ],
        pbSubsidiaryColumns: [
          {
            required: true,
            message: "请选择字段",
            trigger: "change",
          },
        ],
        totalNmSql: [
          { required: true, message: "请输入总记录数SQL", trigger: "blur" },
        ],
        questionNmSql: [
          { required: true, message: "请输入问题数SQL", trigger: "blur" },
        ],
        pbSubsidiarySql: [
          { required: true, message: "请输入问题明细显示SQL", trigger: "blur" },
        ],
        cdValFieldNm: [
          { required: true, message: "请选择字段", trigger: "change" },
        ],
        calcRuleCd: [
          { required: true, message: "请选择四则运算符", trigger: "change" },
        ],
        cdVal: [
          { required: true, message: "请选择映射类型", trigger: "change" },
        ],
        checkObjectType: [
          { required: true, message: "请选择监测类型", trigger: "change" },
        ],
        maxValue: [
          { required: true, message: "请输入上限值", trigger: "blur" },
        ],
        minValue: [
          { required: true, message: "请输入下限值", trigger: "blur" },
        ],
        columnsLength: [
          { required: true, message: "请输入字段长度", trigger: "blur" },
        ],
        dtExecCondDescr: [
          { required: true, message: "请选择执行条件", trigger: "change" },
        ],
      },
    }
  },
  props: {
    row1: {
      type: Object,
    },
    btnType: {
      type: Number,
      default: 1,
    },
    dataSourceId: {
      type: Number,
    },
    sysCode: {
      type: String,
    },
    databaseName: {
      type: String,
    },
    databaseSchema: {
      type: String,
    },
    sysName: {
      type: String,
    },
    problemTypeData: {
      type: Array,
      default: [],
    },
  },
}
</script>

<style lang="scss" scoped>
.add-edit-rule {
  display: inline-block;
  margin-right: 10px;

  .mgt-dialog-upload {
    margin-left: 50px;
  }

  .dialog-footer {
    position: relative;

    .test-rule {
      position: absolute;
      top: 0;
      bottom: 0;
    }
  }
}

.el-form {
  margin-right: 10px;
}

::v-deep .el-dialog .el-dialog__body {
  // 设置dialog的固定高度
  height: 60vh !important;
  overflow: auto;
}

::v-deep .el-dialog {
  top: -80px !important;
}
</style>
