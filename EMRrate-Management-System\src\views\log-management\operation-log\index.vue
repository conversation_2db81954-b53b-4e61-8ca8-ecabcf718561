<template>
  <MainCard>
    <div class="operation-log">
      <div class="log-header">
        <el-form
          :model="queryData"
          ref="ruleForm"
          :inline="true">
          <el-form-item
            label="用户账号"
            prop="userId">
            <el-input
              v-model="queryData.userId"
              placeholder="请输入用户账号"></el-input>
          </el-form-item>
          <el-form-item
            label="用户名"
            prop="username">
            <el-select
              v-model="queryData.username"
              placeholder="请选择任务状态">
              <el-option
                label="全部"
                value=""></el-option>
              <el-option
                v-for="item in userNames"
                :label="item.userName"
                :value="item.userName"
                :key="item.id"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            label="开始时间"
            prop="startTime">
            <el-date-picker
              v-model="queryData.startTime"
              type="date"
              placeholder="请选择开始时间"
              value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
          <el-form-item
            label="结束时间"
            prop="endTime">
            <el-date-picker
              v-model="queryData.endTime"
              type="date"
              placeholder="请选择结束时间"
              value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <div style="margin-left: 10px">
              <el-button
                type="primary"
                @click="searchTaskStatus"
                icon="el-icon-search"
                >搜索</el-button
              >
            </div>
          </el-form-item>
          <el-form-item>
            <el-button @click="resetForm('ruleForm')">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="task-status-table">
        <el-table
          :data="tableData"
          ref="taskStatusTable"
          style="width: 100%"
          v-loading="loading"
          :header-cell-style="{ background: '#fff', color: '#606266' }">
          <el-table-column
            prop="userId"
            label="用户账号">
          </el-table-column>
          <el-table-column
            prop="userName"
            label="用户名">
          </el-table-column>
          <el-table-column
            prop="functionModule"
            label="功能模块"></el-table-column>
          <el-table-column
            prop="operateContent"
            label="操作内容"></el-table-column>
          <el-table-column
            prop="operateTime"
            label="操作时间">
          </el-table-column>
        </el-table>
      </div>
      <div class="log-pag">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryData.pageNum"
          :page-sizes="[5, 10, 15, 20]"
          :page-size="queryData.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalNum">
        </el-pagination>
      </div>
    </div>
  </MainCard>
</template>

<script>
import { queryOperationLog, getUserNameList } from '@/api/logMgt/operationLog'
export default {
  data() {
    return {
      queryData: {
        // 查询数据
        endTime: '',
        pageNum: 1,
        pageSize: 10,
        startTime: '',
        userId: '',
        username: ''
      },
      tableData: [],
      userNames: [],
      totalNum: 1,
      loading: false
    }
  },
  created() {
    // 初始化操作日志列表
    this.queryOperationLogList()
    // 初始化用户名下拉列表
    this.queryUserNameList()
  },
  methods: {
    // 查询用户名下拉列表
    async queryUserNameList() {
      const { data } = await getUserNameList()
      data.forEach((el, index) => {
        this.userNames.push({
          id: index,
          userName: el
        })
      })
    },
    // 查询操作日志列表
    queryOperationLogList() {
      this.loading = true
      queryOperationLog(this.queryData).then((res) => {
        if (res.status === 0) {
          this.tableData = res.data.list
          this.totalNum = res.data.total
          this.loading = false
        } else {
          this.loading = false
          this.$message({
            message: res.msg,
            type: 'error'
          })
        }
      })
    },
    // 重置
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.queryOperationLogList()
    },
    // 搜索任务状态
    searchTaskStatus() {
      this.queryOperationLogList()
    },
    // 改变页面显示条数
    handleSizeChange(val) {
      this.queryData.pageSize = val
      this.queryOperationLogList()
    },
    // 改变页数
    handleCurrentChange(val) {
      this.queryData.pageNum = val
      this.queryOperationLogList()
    }
  }
}
</script>

<style scoped lang="scss">
.operation-log {
  .log-header {
    display: flex;
  }
  .log-pag {
    margin-top: 10px;
  }
}
</style>
