<template>
  <el-dialog
    v-dialogDrag
    :visible="dialogDrag"
    width="800px"
    :show-close="false"
    @close="closeCreateModal"
    :close-on-click-modal="false"
  >
    <div class="dialog-new-title">
      <h4>{{ dialogtitle }}</h4>
      <i class="el-icon-close" @click="handlerClose()"></i>
    </div>
    <el-form
      :model="updateForm"
      class="dialog-body"
      ref="updateFormRef"
      :rules="rules"
    >
      <div class="dialog-form-item">
        <el-form-item label="项目成员" prop="personInCharge" label-width="85px">
        </el-form-item>

        <div class="member-list">
          <div class="dialog-left">
            <div class="leftmainlist">
              <el-input
                placeholder="查找人员…"
                suffix-icon="el-icon-search"
                v-model="input1"
                @input="queryUserInfoList()"
              >
              </el-input>
              <div class="userlist">
                <div
                  @click="addselect(item)"
                  v-for="item in useralllist"
                  :key="item.loginId"
                  class="itemuser"
                >
                  {{ item.userName }}
                </div>
              </div>
            </div>
          </div>
          <div class="dialog-right">
            <div class="listname">
              <b>已选</b>（{{ updateForm.projectMembers.length }}）
            </div>
            <el-table :data="updateForm.projectMembers" height="300px">
              <el-table-column
                label="姓名"
                prop="userName"
                min-width="100px"
              ></el-table-column>
              <el-table-column
                label="是否负责人"
                align="center"
                min-width="100px"
              >
                <template slot-scope="scope">
                  <el-checkbox v-model="scope.row.isincharge"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="50px">
                <template slot-scope="scope">
                  <img
                    v-show="!scope.row.isincharge"
                    src="@/assets/emrimg/delicon.png"
                    @click="delselect(scope.row)"
                  />
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </el-form>
    <div slot="footer">
      <el-button @click="dialogDrag = false" size="mini">取 消</el-button>
      <el-button type="primary" @click="createUser" size="mini"
        >保 存</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { updateProject } from "@/api/document-management/project-management";
import { queryUserInfoList } from "@/api/document-management/catalogue-configuration";

export default {
  data() {
    return {
      dialogDrag: false,
      input1: "", //查询人员关联字段
      dialogtitle: "", //动态标题
      useralllist: [], //人员列表
      updateForm: {
        projectMembers: [],
      },
      rules: {
        personInCharge: [
          {
            trigger: "blur",
            required: true,
            message: "请选择项目负责人",
          },
        ],
      },
    };
  },
  async created() {
    this.queryUserInfoList();
  },
  methods: {
    // 选择人员
    addselect(data) {
      if (
        this.updateForm.projectMembers.filter((item) =>
          item.loginId.includes(data.loginId)
        ).length === 0
      ) {
        this.updateForm.projectMembers.push({
          isincharge: false,
          userName: data.userName,
          loginId: data.loginId,
          gender: data.gender,
        });
      } else {
        this.$message({
          message: "该人员已选择，请选择其他人员",
          type: "error",
        });
      }
    },

    // 删除人员
    delselect(item) {
      this.updateForm.projectMembers.splice(
        this.updateForm.projectMembers.indexOf(item),
        1
      );
    },
    // 查询人员
    queryUserInfoList() {
      this.useralllist = [];
      queryUserInfoList({
        current: 1,
        size: 99999,
        userName: this.input1,
      }).then((res) => {
        this.useralllist = res.data.list;
      });
    },
    // 关闭弹窗
    closeCreateModal() {
      this.dialogDrag = false;
      this.updateForm = { projectMembers: [] };
    },
    // 不同方式打开弹窗展示不同的信息
    openremember(category, data) {
      this.dialogtitle = category;
      this.updateForm = {
        projectName: data.projectName,
        personInCharge: [],
        projectMembers:
          data.projectMembers === "" ? [] : JSON.parse(data.projectMembers),
        levelCode: data.levelCode,
        projectType: data.projectType,
        id: data.id,
      };
      this.dialogDrag = true;
    },
    // 保存
    createUser() {
      this.updateForm.projectMembers.forEach((item) => {
        if (item.isincharge === true) {
          this.updateForm.personInCharge.push(item.loginId);
        }
      });
      this.$refs["updateFormRef"].validate((valid) => {
        if (valid) {
          updateProject(this.updateForm).then((res) => {
            if (res && res.msg == "success") {
              this.$message.success("修改成员成功!");
              this.$parent.queryProject();
              this.closeCreateModal();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          return false;
        }
      });
    },
    // 关闭弹窗
    handlerClose() {
      this.dialogDrag = false;
      this.updateForm = {
        projectName: "",
        personInCharge: [],
        projectMembers: [],
        levelCode: "",
        projectType: "",
        id: "",
      };
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/emr-styles/emr-dialog.scss";

::v-deep .el-dialog__body {
  height: 100%;
  padding: 10px 20px;
  .dialog-new-title {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 10px 20px;
  }
}
.member-list {
  display: flex;
  justify-content: space-around;
  align-items: center;
  border: 1px solid #e3e6e8;
  border-radius: 9px;
  overflow: hidden;
  .dialog-left {
    width: 280px;
    border-right: 1px solid #e3e6e8;
    .leftmainlist {
      height: 341px;
      background: #ffffff;
      .el-input {
        margin: 10px 10%;
        width: 80%;
      }
      .userlist {
        height: 300px;
        overflow: scroll;
      }
      .itemuser {
        line-height: 30px;
        height: 30px;
        font-size: 13px;
        color: #333333;
        margin: 0px 10%;
        border-radius: 6px;
        padding: 0px 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        img {
          width: 14px;
          height: 14px;
        }
      }
      .itemuser:hover {
        background: #eef1f9;
      }
    }
  }
  .dialog-right {
    position: relative;
    flex: 1;
    padding: 10px 20px;
  }
  .listname {
    font-size: 14px;
    color: #333333;
    margin-bottom: 10px;
  }
}

::v-deep .el-table__header-wrapper {
  .has-gutter tr th {
    font-weight: 400;
  }
}
</style>