import { getBrandsUrl } from "@/api/setting"

const state = {
  icon: "",
  loginLogo: "",
  tabBrand: "",
}

const mutations = {
  SET_STATE: (state, newState) => {
    state = Object.assign(state, newState)
  }
}

const actions = {
  getBrandsUrl({ commit }) {
    return new Promise((resolve, reject) => {
      getBrandsUrl().then(res => {
        let newState={};
        if (res && res.data) {
          res.data.forEach(element => {
            newState[element.imageName]="data:image/jpg;base64," +element.imageBase64
          });
          commit('SET_STATE', newState)
          resolve();
        }
      })
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
