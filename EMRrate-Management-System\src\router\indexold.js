import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/singleLogin',
    component: () => import('@/views/login/single.vue'),
    hidden: true
  },
  {
    path: '/',
    redirect: '/dashboard',
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: Layout,
    hidden: true,
    meta: { title: '首页' },
    children: [{
      path: '',
      component: () => import('@/views/dashboard'),
      meta: { title: '首页' }
    }]
  },


  {
    path: '/404',
    component: () => import('@/views/404/index.vue'),
    hidden: true
  },
]



/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [
  {
    path: '/data-collection-mgt',
    component: Layout,
    name: 'Data-collection-mgt',
    redirect: '/data-collection-mgt/data-source-mgt',
    meta: { title: '数据字典采集', icon: "data", activeMenu: 'info' },
    children: [
      {
        path: 'data-source-mgt',
        name: 'Data-source-mgt',
        component: () => import('@/views/data-collection-mgt/data-source-mgt'),
        meta: {
          title: '数据源管理',
        }
      },
      {
        path: 'collection-task',
        name: 'Collection-task',
        component: () => import('@/views/data-collection-mgt/collection-task'),
        meta: {
          title: '采集任务',
        }
      },
      {
        path: 'collection-task-status',
        name: 'Collection-task-status',
        component: () => import('@/views/data-collection-mgt/collection-task-status'),
        meta: {
          title: '采集任务状态',
        }
      },
      {
        path: 'data-structure-details',
        name: 'Data-structure-details',
        component: () => import('@/views/data-collection-mgt/data-structure-details'),
        meta: {
          title: '数据结构详情',
        }
      },
    ]
  },
  {
    path: '/quality-rule-mgt',
    name: 'Quality-rule-mgt',
    component: Layout,
    redirect: '/quality-rule-mgt',
    meta: { title: '数据字典浏览' },
    children: [{
      path: '',
      component: () => import('@/views/dashboard'),
      meta: { title: '数据字典浏览', icon: "data" }
    }]
  },
  {
    path: '/quality-control-mgt',
    component: Layout,
    name: 'Quality-control-mgt',
    redirect: '/quality-control-mgt/check-task-mgt',
    meta: { title: '数据地图', icon: "data", activeMenu: 'info' },
    children: [
      {
        path: 'check-task-mgt',
        name: 'Check-task-mgt',
        component: () => import('@/views/data-collection-mgt/data-source-mgt'),
        meta: {
          title: '检核任务管理',
        }
      },
      {
        path: 'check-status-monitor',
        name: 'Check-status-monitor',
        component: () => import('@/views/data-collection-mgt/collection-task'),
        meta: {
          title: '检核状态监控',
        }
      }
    ]
  },
  {
    path: '/quality-issue-mgt',
    component: Layout,
    name: 'Quality-issue-mgt',
    redirect: '/quality-issue-mgt/check-result-mgt',
    meta: { title: '数据字典管理', icon: "data", activeMenu: 'info' },
    children: [
      {
        path: 'check-result-mgt',
        name: 'Check-result-mgt',
        component: () => import('@/views/data-collection-mgt/data-source-mgt'),
        meta: {
          title: '检核结果管理',
        }
      },
      {
        path: 'quality-issue1-mgt',
        name: 'Quality-issue1-mgt',
        component: () => import('@/views/data-collection-mgt/collection-task'),
        meta: {
          title: '质量问题管理',
        }
      }
    ]
  },
  {
    path: '/authority-management',
    component: Layout,
    meta: { title: '映射关系管理', icon: 'security' },
    children: [
      {
        path: '/authority-management/authority',
        name: 'authority',
        component: () => import("@/views/authority-management/authority"),
        meta: { title: '权限配置' },
      },
      {
        path: '/authority-management/user',
        name: 'user',
        component: () => import("@/views/authority-management/user"),
        meta: { title: '用户管理' },
      },
    ]
  },
  {
    path: '/system-settings',
    name: 'System-settings',
    redirect: '/system-settings/system-config',
    component: Layout,
    meta: { title: '地图关系发布', icon: "el-icon-setting" },
    children: [
      {
        path: 'system-config',
        name: 'System-config',
        component: () => import("@/views/system-settings/system-config"),
        meta: { title: '系统配置', icon: "config" },
      },
      {
        path: 'code-value-mgt',
        name: 'Code-value-mgt',
        component: () => import("@/views/system-settings/code-value-mgt"),
        redirect: '/system-settings/code-value-mgt/code-value-type',
        meta: { title: '码值管理', icon: "codeValMgt" },
        children: [
          {
            path: 'code-value-type',
            component: () => import('@/views/system-settings/code-value-mgt/code-value-type'),
            name: 'Code-value-type',
            meta: { title: '码值类型' }
          },
          {
            path: 'code-value-content',
            component: () => import('@/views/system-settings/code-value-mgt/code-value-content'),
            name: 'Code-value-content',
            meta: { title: '码值内容' }
          }
        ]
      },
    ]
  },

  // {
  //   path: '/log-manage',
  //   component: Layout,
  //   meta: { title: '日志管理', icon: "snippets", activeMenu: 'info' },
  //   children: [
  //     {
  //       path: '/log-manage',
  //       redirect: '/log-manage/operation-log',
  //     },
  //     {
  //       path: 'operation-log',
  //       component: () => import('@/views/log-manage/operationLog.vue'),
  //       meta: {
  //         title: '操作日志',
  //       }
  //     },
  //     {
  //       path: 'post-log',
  //       component: () => import('@/views/log-manage/postLog.vue'),
  //       meta: {
  //         title: '发送消息日志',
  //       }
  //     },
  //     {
  //       path: 'receive-log',
  //       component: () => import('@/views/log-manage/receiveLog.vue'),
  //       meta: {
  //         title: '接收消息日志',
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/terminology-structure',
  //   name: 'terminology-structure',
  //   component: Layout,
  //   meta: { title: '术语结构', icon: "cluster", activeMenu: 'info' },
  //   children: [
  //     {
  //       path: '/terminology-structure',
  //       redirect: '/terminology-structure/terminology-list',
  //     },
  //     {
  //       path: 'terminology-list',
  //       name: 'terminology-list',
  //       component: () => import('@/views/terminology-structure/terminologyList.vue'),
  //       meta: {
  //         title: '术语列表',
  //       }
  //     },
  //     {
  //       path: 'terminology-edit',
  //       name: 'terminology-edit',
  //       component: () => import('@/views/terminology-structure/terminologyEdit.vue'),
  //       props: route => ({ termName: route.query.termName, termId: route.query.termId }),
  //       meta: {
  //         title: '术语结构编辑',
  //       }
  //     },
  //     {
  //       path: 'terminology-add',
  //       name: 'terminology-add',
  //       component: () => import('@/views/terminology-structure/terminologyAdd.vue'),
  //       meta: {
  //         title: '新增术语',
  //       }
  //     },
  //     {
  //       path: 'terminology-subscription',
  //       name: 'terminology-subscription',
  //       component: () => import('@/views/terminology-structure/terminologySubscription'),
  //       meta: {
  //         title: '订阅术语',
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/terminology-data',
  //   name: 'terminology-data',
  //   component: Layout,
  //   meta: { title: '术语数据', icon: "database", activeMenu: 'info' },
  //   children: [
  //     {
  //       path: '/terminology-data',
  //       redirect: '/terminology-data/terminology-edit',
  //     },
  //     {
  //       path: 'terminology-edit',
  //       name: "terminology-edit",
  //       component: () => import('@/views/terminology-data/terminologyEdit'),
  //       meta: {
  //         title: '术语编辑',
  //       }
  //     },
  //     {
  //       path: 'terminology-edit-viewdata',
  //       name: "terminology-edit-viewdata",
  //       component: () => import('@/views/terminology-data/terminologyEdit/viewdata'),
  //       props: route => ({ dictTable: route.query.dictTable, termId: route.query.termId }),
  //       hidden: true,
  //       meta: {
  //         title: '术语字段编辑',
  //       }
  //     },

  //     {
  //       path: 'terminology-examine',
  //       name: "terminology-examine",
  //       component: () => import('@/views/terminology-data/terminologyExamine'),
  //       meta: {
  //         title: '术语审批',
  //       }
  //     },
  //     {
  //       path: 'terminology-examine-viewExamine',
  //       name: "terminology-examine-viewExamine",
  //       component: () => import('@/views/terminology-data/terminologyExamine/viewExamine'),
  //       props: route => ({ dictTable: route.query.dictTable, termId: route.query.termId }),
  //       hidden: true,
  //       meta: {
  //         title: '术语内容审批',
  //       }
  //     },
  //     {
  //       path: 'terminology-publish',
  //       name: "terminology-publish",
  //       component: () => import('@/views/terminology-data/terminologyPublish'),
  //       meta: {
  //         title: '术语发布',
  //       }
  //     },


  //   ]
  // },
  // {
  //   path: '/terminology-mapping',
  //   name: 'terminology-mapping',
  //   component: Layout,
  //   meta: { title: '术语映射', icon: "map", activeMenu: 'info' },
  //   children: [
  //     {
  //       path: '/terminology-mapping',
  //       redirect: '/terminology-mapping/mapping-list',
  //     },
  //     {
  //       path: 'mapping-list',
  //       name: "mapping-list",
  //       component: () => import('@/views/terminology-mapping/mappingList'),
  //       meta: {
  //         title: '映射关系列表',
  //       }
  //     },
  //     {
  //       path: 'mapping-content',
  //       name: "mapping-content",
  //       component: () => import('@/views/terminology-mapping/mappingContent.vue'),
  //       props: route => ({ termName: route.query.termName, termId: route.query.termId }),
  //       meta: {
  //         title: '术语映射内容',
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/authority-management',
  //   component: Layout,
  //   meta: { title: '权限管理', icon: 'security' },
  //   children: [
  //     {
  //       path: '/authority-management/authority',
  //       name: 'authority',
  //       component: () => import("@/views/authority-management/authority"),
  //       meta: { title: '权限配置' },
  //     },
  //     {
  //       path: '/authority-management/user',
  //       name: 'user',
  //       component: () => import("@/views/authority-management/user"),
  //       meta: { title: '用户管理' },
  //     },
  //   ]
  // },
  // {
  //   path: '/setting',
  //   name: 'setting',
  //   component: Layout,
  //   meta: { title: '系统配置', icon: 'el-icon-setting' },
  //   children: [
  //     {
  //       path: '',
  //       component: () => import("@/views/setting/index.vue"),
  //       meta: { title: '系统配置', },
  //     },

  //   ]
  // },
]

// export const asyncRoutes = []
const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()


// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
