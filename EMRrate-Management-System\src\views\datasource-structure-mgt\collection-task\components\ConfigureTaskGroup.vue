<template>
  <div class="configure-task-container">
    <el-dialog
      v-dialogDrag
      title="配置子任务"
      :visible.sync="dialogFormVisible"
      @open="handlerOpen"
      @closed="handlerClose"
      top="40px"
      width="80%"
      :close-on-click-modal="false">
      <div class="task-container-main">
        <div class="configure-task-header">
          <el-select
            v-model="selectValue"
            multiple
            filterable
            clearable
            @change="handlerSelectChange"
            placeholder="请选择或搜索添加子任务">
            <el-option
              v-for="item in subTaskOptions"
              :key="item.dataSourceId"
              :label="item.dataSourceName"
              :value="item.dataSourceId">
            </el-option>
          </el-select>
          <span class="button">
            <el-button
              type="primary"
              @click="addSubTask"
              >保存</el-button
            >
            <el-button
              type="danger"
              @click="batchDeleteSubtasks"
              >批量删除</el-button
            >
          </span>
        </div>
        <div class="configure-task-table">
          <el-table
            :data="subTaskTableData"
            ref="sourceMgtTable"
            style="width: 100%"
            @selection-change="handleSelectionChange"
            v-loading="loading"
            :header-cell-style="{ background: '#fff', color: '#606266' }">
            <el-table-column
              type="selection"
              width="55">
            </el-table-column>
            <el-table-column
              prop="dataSourceId"
              v-if="true"
              label="数据源ID">
            </el-table-column>
            <el-table-column
              prop="dataSourceName"
              label="数据源名称">
            </el-table-column>
            <el-table-column
              prop="dataSourceDescribe"
              label="描述">
            </el-table-column>
            <el-table-column
              prop="databaseName"
              label="数据库名称">
            </el-table-column>
            <el-table-column
              prop="databaseSchema"
              label="SCHEMA">
            </el-table-column>
            <el-table-column
              prop="databaseType"
              label="数据库类型">
            </el-table-column>
            <el-table-column
              show-overflow-tooltip
              prop="databaseUrl"
              label="数据库访问URL"
              width="280">
            </el-table-column>
            <el-table-column
              prop="driverFiles"
              label="驱动文件">
            </el-table-column>
            <el-table-column
              label="操作"
              width="120">
              <template slot-scope="scope">
                <el-popconfirm
                  @onConfirm="deleteCurSubTask(scope.$index, scope.row)"
                  title="确定删除子任务吗？"
                  style="margin-left: 10px">
                  <el-button
                    size="mini"
                    type="text"
                    slot="reference"
                    >删除</el-button
                  >
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { queryDataSourceList } from '@/api/dataSourceStructureMgt/dataSourceMgt'
import { querySubtasks } from '@/api/dataSourceStructureMgt/collectionMgt'
import {
  addOrUpdateSubTask,
  deleteSubTask
} from '@/api/dataSourceStructureMgt/collectionMgt'
export default {
  data() {
    return {
      dialogFormVisible: false, // 弹框状态
      querySelectData: {
        // 查询下拉搜索框数据
        associatedTaskStatus: '2', //未关联
        dataSourceName: '',
        databaseType: ''
      },
      queryTableData: {
        // 查询表格数据
        associatedTaskStatus: '1', //已关联
        dataSourceName: '',
        databaseType: '',
        pageNum: 1,
        pageSize: 10
      },
      totalNum: 1,
      subTaskOptions: [],
      selectValue: [],
      subTaskTableData: [],
      selectedTableSubtaskIds: [], // 表格选中的删除的子任务id数组
      dqmTaskGroupSubtasks: [], // 选中的子任务参数
      subtaskIds: [], // 需要删除的子任务ID
      loading: false
    }
  },
  created() {},
  props: {
    row: {
      type: Object
    }
  },
  watch: {},
  methods: {
    // 改变页面显示条数
    handleSizeChange(val) {
      this.queryTableData.pageSize = val
      this.querySubTaskTableList()
    },
    // 改变页数
    handleCurrentChange(val) {
      this.queryTableData.pageNum = val
      this.querySubTaskTableList()
    },
    // 当diolog打开时渲染表格数据及下拉数据
    handlerOpen() {
      // 初始化下拉框数据
      this.querySelectSearchList()
      // 初始化已关联子任务表格数据
      this.querySubTaskTableList()
    },
    // 查询下拉列表未关联的子任务
    querySelectSearchList() {
      queryDataSourceList(this.querySelectData).then((res) => {
        if (res.status === 0) {
          this.subTaskOptions = res.data.list
        }
      })
    },
    // 查询已关联子任务表格数据
    querySubTaskTableList() {
      this.loading = true
      querySubtasks({ taskGroupId: this.row.jobId }).then((res) => {
        if (res.status === 0) {
          // console.log(res.data)
          this.subTaskTableData = res.data
          this.loading = false
        }
      })
    },
    // 保存并添加子任务
    addSubTask() {
      if (this.selectValue.length === 0) {
        // 没有选择子任务
        this.$message({
          type: 'warning',
          message: '请选择子任务'
        })
      } else {
        addOrUpdateSubTask({
          dqmTaskGroupSubtasks: this.dqmTaskGroupSubtasks,
          taskGroupId: this.row.jobId
        }).then((res) => {
          if (res.status === 0) {
            this.$message({
              type: 'success',
              message: '添加子任务成功'
            })
            this.selectValue = [] // 重置选择框
            this.querySelectSearchList()
            this.querySubTaskTableList()
          }
        })
      }
    },
    // 子任务选择下拉框选中时触发
    handlerSelectChange(val) {
      this.dqmTaskGroupSubtasks = [] // 置空
      val.forEach((item) => {
        this.dqmTaskGroupSubtasks.push({
          // id: 0,
          subTaskId: item,
          taskGroupId: this.row.jobId,
          taskType: 'MG' // 任务类型 MG:元数据采集 QC：数据质量检查
        })
      })
    },
    // 删除单条子任务
    deleteCurSubTask(index, row) {
      // console.log(row)
      deleteSubTask({ ids: row.taskSubTaskId }).then((res) => {
        if (res.status === 0) {
          this.$message({
            type: 'success',
            message: '删除成功'
          })
          this.querySelectSearchList()
          this.querySubTaskTableList()
        }
      })
    },
    // 选择项发生变化时
    handleSelectionChange(val) {
      this.subtaskIds = val.map((item) => {
        return item.taskSubTaskId
      })
    },
    // 批量删除任务组
    batchDeleteSubtasks() {
      if (this.subtaskIds.length > 0) {
        this.$confirm('此操作将删除子任务, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            deleteSubTask({ ids: this.subtaskIds.join() }).then((res) => {
              if (res.status === 0) {
                this.$message({
                  type: 'success',
                  message: '删除成功'
                })
                this.querySelectSearchList()
                this.querySubTaskTableList()
              }
            })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            })
          })
      } else {
        this.$message({
          type: 'info',
          message: '请勾选需要删除的子任务'
        })
      }
    },
    // Dialog 关闭动画结束时的回调
    handlerClose() {
      this.selectValue = [] // 重置下拉搜索框
      this.subTaskTableData = [] // 重置子任务表格数据
    }
  }
}
</script>

<style lang="scss" scoped>
.configure-task-container {
  .task-container-main {
    .configure-task-header {
      .button {
        margin-left: 20px;
      }
    }
    margin: 10px;
    .configure-task-table {
      margin-top: 20px;
    }
  }
}
::v-deep .el-dialog .el-dialog__body {
  height: 80vh;
  overflow: auto;
}
// ::v-deep.configure-task-container .el-dialog {
//   height: 80vh;
// }
</style>
