<template>
  <div class="authorization">
    <div class="top">
      <h3>当前授权文件</h3>
      <div>
        <el-button type="primary" @click="handlerDownload"
          >下载证书授权文件</el-button
        >
        <el-button type="danger" @click="handlerCancellation"
          >注销证书</el-button
        >
      </div>
    </div>
    <div class="main">
      <ul class="auth-details">
        <li v-for="item in authDetailsData" :key="item.id">
          <span>{{ item.title }}：</span>
          <span>{{ item.value }}</span>
        </li>
      </ul>
      <div class="upload">
        <el-upload
          class="upload-demo"
          drag
          :action="`${baseURL}/license/install`"
          :before-upload="handlerBeforeUpload"
          :on-success="handleSuccess"
          :data="fileData"
          :limit="1"
          accept=".lic"
        >
          <i class="el-icon-upload"></i>

          <div class="el-upload__text">
            将证书文件拖到此处，或<em>点击上传</em>
          </div>
          <div class="el-upload__tip" slot="tip">
            文件只能是.lic格式，一次只能上传一个文件，如需多次上传，请清空列表后上传
          </div>
        </el-upload>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getLicenseContent as _getLicenseContent,
  licenseUninstall as _licenseUninstall,
} from "@/api/system-config/sysConfigAuthorization";
export default {
  data() {
    return {
      authDetailsData: [],
      baseURL: process.env.VUE_APP_BASE_API,
      fileData: {},
    };
  },
  created() {
    //先注释，目前不需要证书
    this.getLicenseContent();
  },
  methods: {
    // 下载
    handlerDownload() {
      this.download(
        "/license/serverInfo",
        {},
        `authorization_${new Date().getTime()}.zip`
      );
    },
    // 文件上传前
    handlerBeforeUpload(file) {
      this.fileData.file = file.name;
      //限制文件上传类型
      const isLic = file.name.split(".")[1] === "lic";
      if (!isLic) {
        this.$message.error("上传图片只能是 lic 格式!");
        return false;
      }
      return true;
    },
    // 注销
    handlerCancellation() {
      _licenseUninstall().then((res) => {
        if (res.code != 200) {
          this.$message.error(res.message);
          return;
        }
        this.$message.success(res.data);
        this.getLicenseContent();
      });
    },
    // 文件上传成功时
    handleSuccess(res) {
      if (res.code != 200) {
        this.$message.error(res.message);
        return;
      }
      this.$message.success(res.data);
      this.getLicenseContent();
    },
    // 获取授权文件信息
    async getLicenseContent() {
      const { code, data, message } = await _getLicenseContent();
      if (code !== 200) {
        this.$message.error(message);
        return;
      }
      let id = 0;
      this.authDetailsData = [];
      for (const key in data) {
        switch (key) {
          case "description":
            this.authDetailsData.push({
              id: ++id,
              title: "证书名称",
              value: data[key],
            });
            break;
          case "loginUserCount":
            this.authDetailsData.push({
              id: ++id,
              title: "使用方",
              value: data[key],
            });
            break;
          case "notAfter":
            this.authDetailsData.push({
              id: ++id,
              title: "过期日期",
              value: data[key],
            });
            break;
          case "notBefore":
            this.authDetailsData.push({
              id: ++id,
              title: "签发日期",
              value: data[key],
            });
            break;
          case "subject":
            this.authDetailsData.push({
              id: ++id,
              title: "证书类目",
              value: data[key],
            });
            break;
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.authorization {
  .top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
  }
  .main {
    .auth-details {
      li {
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        height: 50px;
        display: flex;
        align-items: center;
        span {
          flex: 1;
        }
      }
    }
  }
}
ul,
li {
  list-style: none;
  padding: 0;
  margin: 0;
}
::v-deep .el-upload {
  width: 100%;
}
::v-deep .el-upload-dragger {
  width: 100%;
}
</style>
