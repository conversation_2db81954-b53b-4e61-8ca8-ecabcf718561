<template>
  <div class="structure-config-container">
    <div class="structure-config-header">
      <div>系统名称：{{ row.dataSourceName }}</div>
    </div>
    <div
      @wheel.stop="() => {}"
      class="structure-config-main">
      <div class="components-container">
        <split-pane
          split="vertical"
          :min-percent="30"
          @resize="resize">
          <template slot="paneL">
            <div  v-loading="isLoading" class="left-container">
              <div class="search">
                <el-input
                  @change="handlerTreeQuery"
                  size="mini"
                  style="width: 60%; margin-right: 10px"
                  v-model="filterText"
                  placeholder="查找表/视图"></el-input>
                <el-button
                  size="mini"
                  @click="reset"
                  >重置</el-button
                >
              </div>
              <div class="tree">
                <el-tree
                  :data="treeData"
                  show-checkbox
                  default-expand-all
                  node-key="id"
                  ref="tree"
                  highlight-current
                  @node-click="handlerNodeClick"
                  :default-checked-keys="defaultCheckedArr"
                  :props="defaultProps"
                  @check="(val1, val2) => handlerChecked(val2)"
                  @check-change="handleCheckChange">
                  <template #default="props">
                    <span>
                      <!-- 加载更多 -->
                      <i :class="getIconClass(props.node)"></i>
                      <!-- 每个层级的图表 -->
                      <img
                        class="icon"
                        :src="getIconClass(props.node)"
                        alt="" />
                      <span
                        class="tree-itemLabel"
                        style="margin-left: 6px"
                        >{{ getLabelName(props.node) }}</span
                      >
                    </span>
                  </template>
                </el-tree>
              </div>
            </div>
          </template>
          <template slot="paneR">
            <div v-loading="isSelLoading" class="right-container">
              <svg-icon
                class="svg-icon"
                icon-class="icon_tuozhuai"
                style="font-size: 30px" />
              <div class="title">已选择的表：</div>
              <div
                v-if="savedStructureData.length === 0"
                style="color: #999; text-align: center; margin-top: 150px">
                暂无数据
              </div>
              <div class="list">
                <div
                  v-for="item in savedStructureData"
                  :key="item"
                  class="item">
                  <img
                    :src="treeIcons.tree_table"
                    alt="图片" />
                  <span>{{ item }}</span>
                </div>
              </div>
            </div>
          </template>
        </split-pane>
      </div>
      <div class="structure-config-footer">
        <div class="left">
          <Button
            name="全选"
            @click.native="selectAllNodes"></Button>
          <Button
            @click.native="invertSelectNodes"
            name="反选"></Button>
          <Button
            name="同步数据结构"
            @click.native="synchronousDataStructure"
            ><svg-icon
              icon-class="icon_23"
              style="font-size: 14px; margin-right: 4px" />
          </Button>
        </div>
        <div class="right">选择的结构表：{{ savedStructureData.length }}个</div>
      </div>
    </div>
  </div>
</template>

<script>
import splitPane from 'vue-splitpane'
import Button from './Button.vue'
import ArrowAnimation from '@/components/ArrowAnimation'

import {
  synchronousDataStructure,
  querySavedStructures,
  saveDbStructure
} from '@/api/dataSourceStructureMgt/dataSourceMgt'

import { getLeftTree } from '@/api/dataSourceStructureMgt/dataStructureDetails'

// 找到树形结构某一节点
function findItemByLabel(data, label) {
  for (let i = 0; i < data.length; i++) {
    const item = data[i]
    if (item.label === label) {
      return item
    }
    if (item.children) {
      const result = findItemByLabel(item.children, label)
      if (result) {
        return result
      }
    }
  }
  return null
}
function filterNodesByLabel(node, keyword) {
  if (node.label.includes(keyword)) {
    return node // 当前节点包含关键字，返回该节点
  } else if (node.children) {
    const filteredChildren = node.children
      .map((child) => filterNodesByLabel(child, keyword))
      .filter(Boolean) // 递归过滤子节点
    if (filteredChildren.length > 0) {
      return {
        ...node,
        children: filteredChildren
      } // 子节点包含关键字，返回该节点及其子节点
    }
  }
}
// 模糊查询
function filterTreeByLabel(tree, keyword) {
  return tree.map((node) => filterNodesByLabel(node, keyword)).filter(Boolean)
}
// 找到前后两个数组的变化，并返回新增或者移除的元素
function findAddedOrRemovedItems(oldArray, newArray) {
  const addedItems = newArray.filter((item) => !oldArray.includes(item))
  const removedItems = oldArray.filter((item) => !newArray.includes(item))
  return {
    addedItems,
    removedItems
  }
}
// 获取树所有节点的id组成的数组
function getAllKeys(nodes, keys) {
  nodes.forEach((node) => {
    keys.push(node.id)
    if (node.children) {
      getAllKeys(node.children, keys)
    }
  })
}

export default {
  components: {
    ArrowAnimation,
    splitPane,
    Button
  },

  data() {
    return {
      treeData: [],
      defaultCheckedArr: [],
      loadQuantity: 10, // 每次点击加载的数量
      defaultShowQuantity: 20, // 默认展示的数量
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      treeIcons: {
        tree_data_sourse: require('@/assets/tree/tree_data_sourse.png'),
        tree_folder: require('@/assets/tree/tree_folder.png'),
        tree_shouqi: require('@/assets/tree/tree_shouqi.png'),
        tree_table: require('@/assets/tree/tree_table.png'),
        tree_view: require('@/assets/tree/tree_view.png'),
        tree_zhankai: require('@/assets/tree/tree_zhankai.png')
      },
      dialogFormVisible: false, // 弹框状态
      saveDbStructureData: {
        catalogName: '', //	CATALOG名称
        chooseResult: 0, // 0/1 0有且，1不包含
        dataSourceId: '',
        databaseName: '',
        schemaName: '',
        sysCode: '',
        sysName: '',
        tableNames: []
      },
      filterText: '',
      moreNode: {
        id: -1,
        label: '加载更多',
        disabled: true
      },
      savedStructureData: [], // 已保存的表结构数据
      curSelectedTableData: [], // 当前选中的表
      queryLeftTree: {
        // 查询左侧树数据
        dataSourceId: ''
      },
      savedStructuresPromise: '',
      leftTreePromise: '',
      preLength: 0,
      treeCopyAllData: [],
      treeAllData: [],
      tableChildNodes: [],
      residueTableNodes: [],
      viewChildNodes: [],
      residueViewNodes: [],
      flatArr: [],
      isLoading: false,
      isSelLoading: false
    }
  },
  props: {
    row: {
      type: Object
    }
  },
  created() {
    this.initStructureData()
  },
  methods: {
    resize() {
      console.log('resize')
    },
    // 获取元素组成的keys
    getNodeKeys(data) {
      let keys = []
      getAllKeys(data, keys)
      return keys
    },
    async initStructureData() {
      this.savedStructureData = []
      this.queryLeftTree.dataSourceId = this.row.dataSourceId
      this.getSavedStructures()
      this.getLeftTreeList()
      // 异步请求都完成后再设置默认选中
      await Promise.all([this.savedStructuresPromise, this.leftTreePromise])
      this.setDefaultSelect()
    },
    // 递归根据右侧已选表生成默认选中id数组
    lookForAllId(data = [], arr = []) {
      for (let item of data) {
        arr.push(item)
        if (item.children && item.children.length)
          this.lookForAllId(item.children, arr)
      }
      return arr
    },
    // 点击复选框
    handlerChecked(val2) {
      val2.checkedNodes = val2.checkedNodes.filter((item) => item.pid > 2) // 过滤当全选时非table或非view里面的非法表格数据
      this.curSelectedTableData = [
        ...new Set(val2.checkedNodes.map((item) => item.label))
      ]
      // 向右侧添加
      this.savedStructureData = [
        ...new Set([...this.curSelectedTableData, ...this.savedStructureData])
      ]
    },
    // 点击复选框 删除右侧元素
    handleCheckChange(val1, val2) {
      if (val2 === false) {
        let index = this.savedStructureData.indexOf(val1.label)
        if (index > -1) {
          this.savedStructureData.splice(index, 1)
        }
      }
    },
    // 删除已添加表标签
    handleClose(index) {
      this.savedStructureData.splice(index, 1)
      // 设置默认选中
      this.setDefaultSelect()
    },
    // 设置默认选中
    setDefaultSelect() {
      this.flatArr = this.lookForAllId(this.treeData)
      this.defaultCheckedArr = this.flatArr
        .filter((item) => this.savedStructureData.includes(item.label))
        .map((item) => item.id)
      this.$nextTick(() => {
        this.$refs.tree.setCheckedKeys(this.defaultCheckedArr)
      })
    },
    // 获取左侧树列表数据
    getLeftTreeList() {
      this.treeData = [] // 先置空树
      this.isLoading = true
      this.leftTreePromise = getLeftTree(this.queryLeftTree)
        .then((res) => {
          if (res.status === 0) {
            if (res.data.length === 0) {
              this.isLoading = false
              this.treeData = []
            } else {
              this.treeAllData = res.data
              this.treeCopyAllData = JSON.parse(
                JSON.stringify(this.treeAllData)
              )
              this.treeAllData = res.data
              this.handlerLoadMoreNode(this.treeAllData)
              this.setDefaultSelect()
              this.isLoading = false
            }
          }
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    // 全选
    selectAllNodes() {
      this.$refs.tree.setCheckedKeys(this.getNodeKeys(this.treeData))
      this.handlerChecked({ checkedNodes: this.$refs.tree.getCheckedNodes() })
    },
    // 反选
    invertSelectNodes() {
      const curCheckedNodes = this.$refs.tree.getCheckedNodes()
      const curCheckedKeys = this.getNodeKeys(curCheckedNodes)
      const flatTreeData = this.lookForAllId(this.treeData).filter(
        (item) => item.pid > 2
      )
      const notChreckedNodes = flatTreeData.filter((item) => {
        return !curCheckedKeys.includes(item.id)
      })
      console.log(notChreckedNodes, '00000')
      this.$refs.tree.setCheckedKeys(this.getNodeKeys(notChreckedNodes))
      this.handlerChecked({ checkedNodes: notChreckedNodes })
    },

    // 重置
    reset() {
      this.filterText = ''
      this.handlerTreeQuery()
    },
    // 模糊查询
    handlerTreeQuery() {
      let filterNodes = []
      filterNodes = JSON.parse(
        JSON.stringify(filterTreeByLabel(this.treeCopyAllData, this.filterText))
      )
      this.handlerLoadMoreNode(filterNodes)
    },

    // 处理表根视图子节点加载更多
    handlerLoadMoreNode(treeAllData) {
      // 处理表
      const tableNodes = findItemByLabel(treeAllData, 'TABLE')
      if (tableNodes) {
        this.tableChildNodes = tableNodes.children
        if (this.tableChildNodes && this.tableChildNodes.length > 0) {
          const length = this.tableChildNodes.length
          this.residueTableNodes = this.tableChildNodes.splice(
            this.defaultShowQuantity
          )
          if (length > this.defaultShowQuantity) {
            this.tableChildNodes.push(this.moreNode)
          }
        }
      }
      // 处理视图
      const viewNodes = findItemByLabel(treeAllData, 'VIEW')
      if (viewNodes) {
        this.viewChildNodes = findItemByLabel(treeAllData, 'VIEW').children
        if (this.viewChildNodes && this.viewChildNodes.length > 0) {
          const length = this.viewChildNodes.length
          this.residueViewNodes = this.viewChildNodes.splice(
            this.defaultShowQuantity
          )
          if (length > this.defaultShowQuantity) {
            this.viewChildNodes.push(this.moreNode)
          }
        }
      }

      this.treeData = treeAllData
      this.setDefaultSelect()
    },
    // 获取已保存结构
    getSavedStructures() {
      this.isSelLoading = true
      this.savedStructureData = []
      this.savedStructuresPromise = querySavedStructures({
        dataSourceId: this.row.dataSourceId
      }).then((res) => {
        if (res.status === 0) {
          res.data.forEach((item, index) => {
            this.savedStructureData.push(item.tableName)
          })
        }
        this.isSelLoading = false
      })
    },
    // 处理加载更多图标
    getIconClass(node) {
      if (node.level == 4 && node.label == '加载更多') {
        return 'el-icon-arrow-down'
      }
      // 模式、表、视图目录图标
      if (node.level == 2 || node.level == 3) {
        return this.treeIcons.tree_folder
      }
      // 表内容
      if (node.level == 4 || node.parent.label === 'TABLE') {
        return this.treeIcons.tree_table
      }
      // 视图内容
      if (node.level == 4 || node.parent.label === 'VIEW') {
        return this.treeIcons.tree_table
      }
    },
    getLabelName(node) {
      if (node.level == 2) {
        return '模式'
      }
      if (node.label === 'TABLE') {
        return '表'
      }
      if (node.label === 'VIEW') {
        return '视图'
      }
      return node.label
    },
    // 节点被点击时
    handlerNodeClick(node, Node) {
      if (node.label == '加载更多') {
        if (Node.parent.label == 'TABLE') {
          this.$refs.tree.remove(Node)
          const curNodes = this.residueTableNodes.splice(0, this.loadQuantity)
          if (this.residueTableNodes.length > this.loadQuantity) {
            this.tableChildNodes.push(...curNodes, this.moreNode)
          } else {
            this.tableChildNodes.push(...curNodes)
          }
        } else {
          this.$refs.tree.remove(Node)
          const curNodes = this.residueViewNodes.splice(0, this.loadQuantity)
          if (this.residueViewNodes.length > this.loadQuantity) {
            this.viewChildNodes.push(...curNodes, this.moreNode)
          } else {
            this.viewChildNodes.push(...curNodes)
          }
        }
        this.setDefaultSelect()
      }
    },
    // 同步数据结构
    synchronousDataStructure() {
      this.isLoading = true
      synchronousDataStructure({ dataSourceId: this.row.dataSourceId }).then(
        (res) => {
          if (res.status !== 0) {
            this.$message({
              message: res.msg,
              type: 'error'
            })
            this.isLoading = false
            return
          }
          this.$message({
            type: 'success',
            message: '同步成功'
          })
          this.getLeftTreeList()
        }
      )
    },
    // 保存结构
    saveStructure() {
      return new Promise((rsl) => {
        this.saveDbStructureData.dataSourceId = this.row.dataSourceId
        this.saveDbStructureData.tableNames = this.savedStructureData
        saveDbStructure(this.saveDbStructureData).then((res) => {
          if (res.status === 0) {
            this.$message({
              type: 'success',
              message: '保存结构设置成功'
            })
            this.getSavedStructures()
            rsl()
            return
          }
          this.$message({
            type: 'error',
            message: res.msg
          })
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.structure-config-container {
  .structure-config-header {
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: 600;
  }
  padding: 0 20px;
  .structure-config-main {
    border: 1px solid #e3e6e8;
    border-radius: 9px;
    width: 760px;
    height: 480px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .components-container {
      height: 92%;
    }
    .structure-config-footer {
      .left {
        .btn {
          margin-right: 10px;
        }
      }
      height: 40px;
      background-color: #f3f3f3;
      border-top: 1px solid #e3e6e8;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px;
    }
  }

  .left-container {
    border-right: 1px solid #e3e6e8;
    height: 100%;
    padding: 10px;
    display: flex;
    flex-direction: column;
    .search {
      margin-bottom: 10px;
    }
    .tree {
      overflow-x: auto;
      .fold {
        width: 18px;
        margin-left: -2px;
      }
      .icon {
        vertical-align: middle;
      }
    }
  }
  .right-container {
    width: 100%;
    height: 100%;
    position: relative;
    padding: 10px;
    padding-left: 20px;
    display: flex;
    flex-direction: column;
    .svg-icon {
      position: absolute;
      left: -8px;
      top: 50%;
      transform: translateY(-50%);
    }
    .title {
      font-size: 14px;
      font-weight: 600;
    }
    .list {
      overflow: auto;
      margin-top: 16px;
      .item {
        margin-bottom: 12px;
        img {
          vertical-align: middle;
          margin-right: 8px;
        }
      }
    }
  }
}
::v-deep.structure-config-container .el-dialog {
  /* height: 80vh; */
  top: -70px;
}
</style>
